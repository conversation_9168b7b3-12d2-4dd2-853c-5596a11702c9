
/* /website/static/src/js/editor/editor.js */
odoo.define('website.editor',function(require){'use strict';var weWidgets=require('web_editor.widget');var wUtils=require('website.utils');weWidgets.LinkDialog.include({start:async function(){const options={body:this.linkWidget.$link&&this.linkWidget.$link[0].ownerDocument.body,};const result=await this._super.apply(this,arguments);wUtils.autocompleteWithPages(this,this.$('input[name="url"]'),options);return result;},});});;

/* /website/static/src/js/editor/snippets.editor.js */
odoo.define('website.snippet.editor',function(require){'use strict';const{qweb,_t,_lt}=require('web.core');const Dialog=require('web.Dialog');const weSnippetEditor=require('web_editor.snippet.editor');const wSnippetOptions=require('website.editor.snippets.options');const OdooEditorLib=require('@web_editor/js/editor/odoo-editor/src/utils/utils');const getDeepRange=OdooEditorLib.getDeepRange;const getTraversedNodes=OdooEditorLib.getTraversedNodes;const FontFamilyPickerUserValueWidget=wSnippetOptions.FontFamilyPickerUserValueWidget;const wSnippetMenu=weSnippetEditor.SnippetsMenu.extend({events:_.extend({},weSnippetEditor.SnippetsMenu.prototype.events,{'click .o_we_customize_theme_btn':'_onThemeTabClick','click .o_we_animate_text':'_onAnimateTextClick','click .o_we_highlight_animated_text':'_onHighlightAnimatedTextClick',}),custom_events:Object.assign({},weSnippetEditor.SnippetsMenu.prototype.custom_events,{'gmap_api_request':'_onGMapAPIRequest','gmap_api_key_request':'_onGMapAPIKeyRequest','reload_bundles':'_onReloadBundles',}),tabs:_.extend({},weSnippetEditor.SnippetsMenu.prototype.tabs,{THEME:'theme',}),optionsTabStructure:[['theme-colors',_lt("Theme Colors")],['theme-options',_lt("Theme Options")],['website-settings',_lt("Website Settings")],],async start(){await this._super(...arguments);this.$currentAnimatedText=$();this.__onSelectionChange=ev=>{this._toggleAnimatedTextButton();};this.$body[0].addEventListener('selectionchange',this.__onSelectionChange);this._hideBackendNavbarTimeout=setTimeout(()=>{this.el.ownerDocument.body.classList.add('editor_has_snippets_hide_backend_navbar');},500);},destroy(){this._super(...arguments);this.$body[0].removeEventListener('selectionchange',this.__onSelectionChange);this.$body[0].classList.remove('o_animated_text_highlighted');clearTimeout(this._hideBackendNavbarTimeout);this.el.ownerDocument.body.classList.remove('editor_has_snippets_hide_backend_navbar');},async callPostSnippetDrop($target){if($target[0].classList.contains('s_share')){$target[0].classList.add('o_no_link_popover');}
return this._super(...arguments);},_computeSnippetTemplates:function(html){const $html=$(html);const fontVariables=_.map($html.find('we-fontfamilypicker[data-variable]'),el=>{return el.dataset.variable;});FontFamilyPickerUserValueWidget.prototype.fontVariables=fontVariables;return this._super(...arguments);},_patchForComputeSnippetTemplates($html){this._super(...arguments);$html.find('[data-js="WebsiteAnimate"]').eq(0).before($(_.str.sprintf(`
            <div data-js="GridImage" data-selector="img">
                <we-select string="%s">
                    <we-button data-change-grid-image-mode="cover">%s</we-button>
                    <we-button data-change-grid-image-mode="contain">%s</we-button>
                </we-select>
            </div>
        `,_t("Position"),_t("Cover"),_t("Contain"))));$html.find('[data-attribute-name="interval"]')[0].dataset.attributeName="bsInterval";},async _configureGMapAPI({reconfigure,onlyIfUndefined}){if(!reconfigure&&!onlyIfUndefined){return false;}
const apiKey=await new Promise(resolve=>{this.getParent().trigger_up('gmap_api_key_request',{onSuccess:key=>resolve(key),});});const apiKeyValidation=apiKey?await this._validateGMapAPIKey(apiKey):{isValid:false,message:undefined,};if(!reconfigure&&onlyIfUndefined&&apiKey&&apiKeyValidation.isValid){return false;}
let websiteId;this.trigger_up('context_get',{callback:ctx=>websiteId=ctx['website_id'],});function applyError(message){const $apiKeyInput=this.find('#api_key_input');const $apiKeyHelp=this.find('#api_key_help');$apiKeyInput.addClass('is-invalid');$apiKeyHelp.empty().text(message);}
const $content=$(qweb.render('website.s_google_map_modal',{apiKey:apiKey,}));if(!apiKeyValidation.isValid&&apiKeyValidation.message){applyError.call($content,apiKeyValidation.message);}
return new Promise(resolve=>{let invalidated=false;const dialog=new Dialog(this,{size:'medium',title:_t("Google Map API Key"),buttons:[{text:_t("Save"),classes:'btn-primary',click:async(ev)=>{const valueAPIKey=dialog.$('#api_key_input').val();if(!valueAPIKey){applyError.call(dialog.$el,_t("Enter an API Key"));return;}
const $button=$(ev.currentTarget);$button.prop('disabled',true);const res=await this._validateGMapAPIKey(valueAPIKey);if(res.isValid){await this._rpc({model:'website',method:'write',args:[[websiteId],{google_maps_api_key:valueAPIKey},],});invalidated=true;dialog.close();}else{applyError.call(dialog.$el,res.message);}
$button.prop("disabled",false);}},{text:_t("Cancel"),close:true}],$content:$content,});dialog.on('closed',this,()=>resolve(invalidated));dialog.open();});},async _validateGMapAPIKey(key){try{const response=await fetch(`https://maps.googleapis.com/maps/api/staticmap?center=belgium&size=10x10&key=${encodeURIComponent(key)}`);const isValid=(response.status===200);return{isValid:isValid,message:!isValid&&_t("Invalid API Key. The following error was returned by Google:")+" "+(await response.text()),};}catch(_err){return{isValid:false,message:_t("Check your connection and try again"),};}},_getScrollOptions(options={}){const finalOptions=this._super(...arguments);if(!options.offsetElements||!options.offsetElements.$top){const $header=$('#top');if($header.length){finalOptions.offsetElements=finalOptions.offsetElements||{};finalOptions.offsetElements.$top=$header;}}
finalOptions.jQueryDraggableOptions.iframeFix=true;return finalOptions;},async _handleGMapRequest(ev,gmapRequestEventName){ev.stopPropagation();const reconfigured=await this._configureGMapAPI({reconfigure:ev.data.reconfigure,onlyIfUndefined:ev.data.configureIfNecessary,});this.getParent().trigger_up(gmapRequestEventName,{refetch:reconfigured,editableMode:true,onSuccess:key=>ev.data.onSuccess(key),});},_updateRightPanelContent:function({content,tab}){this._super(...arguments);this.$('.o_we_customize_theme_btn').toggleClass('active',tab===this.tabs.THEME);},_getAnimatedTextElement(){const editable=this.options.wysiwyg.$editable[0];const animatedTextNode=getTraversedNodes(editable).find(n=>n.parentElement.closest(".o_animated_text"));return animatedTextNode?animatedTextNode.parentElement.closest('.o_animated_text'):false;},_addToolbar(){this._super(...arguments);this.$('#o_we_editor_toolbar_container > we-title > span').after($(`
            <div class="btn fa fa-fw fa-2x o_we_highlight_animated_text d-none
                ${this.$body.hasClass('o_animated_text_highlighted') ? 'fa-eye text-success' : 'fa-eye-slash'}"
                title="${_t('Highlight Animated Text')}"
                aria-label="Highlight Animated Text"/>
        `));this._toggleAnimatedTextButton();this._toggleHighlightAnimatedTextButton();},_toggleAnimatedTextButton(){const sel=this.options.wysiwyg.odooEditor.document.getSelection();if(!this._isValidSelection(sel)){return;}
const animatedText=this._getAnimatedTextElement();this.$('.o_we_animate_text').toggleClass('active',!!animatedText);this.$currentAnimatedText=animatedText?$(animatedText):$();},_toggleHighlightAnimatedTextButton(){const $animatedText=this.getEditableArea().find('.o_animated_text');this.$('#o_we_editor_toolbar_container .o_we_highlight_animated_text').toggleClass('d-none',!$animatedText.length);},_isValidSelection(sel){return sel.rangeCount&&[...this.getEditableArea()].some(el=>el.contains(sel.anchorNode));},_onGMapAPIRequest(ev){this._handleGMapRequest(ev,'gmap_api_request');},_onGMapAPIKeyRequest(ev){this._handleGMapRequest(ev,'gmap_api_key_request');},async _onThemeTabClick(ev){let releaseLoader;try{const promise=new Promise(resolve=>releaseLoader=resolve);this._execWithLoadingEffect(()=>promise,false,0);await new Promise(resolve=>requestAnimationFrame(()=>requestAnimationFrame(resolve)));if(!this.topFakeOptionEl){let el;for(const[elementName,title]of this.optionsTabStructure){const newEl=document.createElement(elementName);newEl.dataset.name=title;if(el){el.appendChild(newEl);}else{this.topFakeOptionEl=newEl;}
el=newEl;}
this.bottomFakeOptionEl=el;this.$body[0].appendChild(this.topFakeOptionEl);}
this.topFakeOptionEl.classList.remove('d-none');const editorPromise=this._activateSnippet($(this.bottomFakeOptionEl));releaseLoader();releaseLoader=undefined;const editor=await editorPromise;this.topFakeOptionEl.classList.add('d-none');editor.toggleOverlay(false);this._updateRightPanelContent({tab:this.tabs.THEME,});}catch(e){if(releaseLoader){releaseLoader();}
throw e;}},_onOptionsTabClick(ev){if(!ev.currentTarget.classList.contains('active')){this._activateSnippet(false);this._mutex.exec(async()=>{const switchableViews=await new Promise((resolve,reject)=>{this.trigger_up('get_switchable_related_views',{onSuccess:resolve,onFailure:reject,});});if(switchableViews.length){this._activateSnippet(this.$body.find('#wrapwrap > main'));return;}
let $pageOptionsTarget=$();let i=0;const pageOptions=this.templateOptions.filter(template=>template.data.pageOptions);while(!$pageOptionsTarget.length&&i<pageOptions.length){$pageOptionsTarget=pageOptions[i].selector.all();i++;}
if($pageOptionsTarget.length){this._activateSnippet($pageOptionsTarget);}else{this._activateEmptyOptionsTab();}});}},_onAnimateTextClick(ev){const sel=this.options.wysiwyg.odooEditor.document.getSelection();if(!this._isValidSelection(sel)){return;}
const editable=this.options.wysiwyg.$editable[0];const range=getDeepRange(editable,{splitText:true,select:true,correctTripleClick:true});if(this.$currentAnimatedText.length){this.$currentAnimatedText.contents().unwrap();this.options.wysiwyg.odooEditor.historyResetLatestComputedSelection();this._toggleHighlightAnimatedTextButton();ev.target.classList.remove('active');this.options.wysiwyg.odooEditor.historyStep();}else{if(sel.getRangeAt(0).collapsed){return;}
const animatedTextEl=document.createElement('span');animatedTextEl.classList.add('o_animated_text','o_animate','o_animate_preview','o_anim_fade_in');let $snippet=null;try{range.surroundContents(animatedTextEl);$snippet=$(animatedTextEl);}catch(_e){if(range.commonAncestorContainer.textContent===range.toString()){const $commonAncestor=$(range.commonAncestorContainer);$commonAncestor.wrapInner(animatedTextEl);$snippet=$commonAncestor.find('.o_animated_text');}}
if($snippet){$snippet[0].normalize();this.trigger_up('activate_snippet',{$snippet:$snippet,previewMode:false,});this.options.wysiwyg.odooEditor.historyStep();}else{this.displayNotification({message:_t("The current text selection cannot be animated. Try clearing the format and try again."),type:'danger',sticky:true,});}}},_onHighlightAnimatedTextClick(ev){this.$body.toggleClass('o_animated_text_highlighted');$(ev.target).toggleClass('fa-eye fa-eye-slash').toggleClass('text-success');},_onReloadBundles(ev){if(this._currentTab===this.tabs.THEME){const excludeSelector=this.optionsTabStructure.map(element=>element[0]).join(', ');for(const editor of this.snippetEditors){if(!editor.$target[0].matches(excludeSelector)){this._mutex.exec(()=>editor.destroy());}}}},});weSnippetEditor.SnippetEditor.include({layoutElementsSelector:[weSnippetEditor.SnippetEditor.prototype.layoutElementsSelector,'.s_parallax_bg','.o_bg_video_container',].join(','),getName(){if(this.$target[0].closest('[data-oe-field=logo]')){return _t("Logo");}
return this._super(...arguments);},_prepareDrag(){const restore=this._super(...arguments);const wrapwrapEl=this.$body[0].ownerDocument.defaultView.document.body.querySelector('#wrapwrap');const hasFooterScrollEffect=wrapwrapEl&&wrapwrapEl.classList.contains('o_footer_effect_enable');if(hasFooterScrollEffect){wrapwrapEl.classList.remove('o_footer_effect_enable');return()=>{wrapwrapEl.classList.add('o_footer_effect_enable');restore();};}
return restore;},});return{SnippetsMenu:wSnippetMenu,};});;

/* /website/static/src/js/editor/snippets.options.js */
odoo.define('website.editor.snippets.options',function(require){'use strict';const{ColorpickerWidget}=require('web.Colorpicker');var core=require('web.core');const{loadBundle,loadCSS}=require("@web/core/assets");var Dialog=require('web.Dialog');const{Markup,sprintf}=require('web.utils');const weUtils=require('web_editor.utils');var options=require('web_editor.snippets.options');const wLinkPopoverWidget=require('@website/js/widgets/link_popover_widget')[Symbol.for("default")];const wUtils=require('website.utils');const{isImageSupportedForStyle}=require('web_editor.image_processing');require('website.s_popup_options');var _t=core._t;var qweb=core.qweb;const InputUserValueWidget=options.userValueWidgetsRegistry['we-input'];const SelectUserValueWidget=options.userValueWidgetsRegistry['we-select'];options.UserValueWidget.include({loadMethodsData(){this._super(...arguments);const indexVariable=this._methodsNames.indexOf('customizeWebsiteVariable');if(indexVariable>=0){const indexView=this._methodsNames.indexOf('customizeWebsiteViews');if(indexView>=0){this._methodsNames[indexVariable]='customizeWebsiteViews';this._methodsNames[indexView]='customizeWebsiteVariable';}}},});const UrlPickerUserValueWidget=InputUserValueWidget.extend({custom_events:_.extend({},InputUserValueWidget.prototype.custom_events||{},{'website_url_chosen':'_onWebsiteURLChosen',}),events:_.extend({},InputUserValueWidget.prototype.events||{},{'click .o_we_redirect_to':'_onRedirectTo',}),start:async function(){await this._super(...arguments);const linkButton=document.createElement('we-button');const icon=document.createElement('i');icon.classList.add('fa','fa-fw','fa-external-link')
linkButton.classList.add('o_we_redirect_to');linkButton.title=_t("Redirect to URL in a new tab");linkButton.appendChild(icon);this.containerEl.appendChild(linkButton);this.el.classList.add('o_we_large');this.inputEl.classList.add('text-start');const options={position:{collision:'flip flipfit',},classes:{"ui-autocomplete":'o_website_ui_autocomplete'},body:this.getParent().$target[0].ownerDocument.body,};wUtils.autocompleteWithPages(this,$(this.inputEl),options);},_onWebsiteURLChosen:function(ev){this._value=this.inputEl.value;this._onUserValueChange(ev);},_onRedirectTo:function(){if(this._value){window.open(this._value,'_blank');}},});const FontFamilyPickerUserValueWidget=SelectUserValueWidget.extend({events:_.extend({},SelectUserValueWidget.prototype.events||{},{'click .o_we_add_google_font_btn':'_onAddGoogleFontClick','click .o_we_delete_google_font_btn':'_onDeleteGoogleFontClick',}),fontVariables:[],start:async function(){const style=window.getComputedStyle(this.$target[0].ownerDocument.documentElement);const nbFonts=parseInt(weUtils.getCSSVariableValue('number-of-fonts',style));const googleFontsProperty=weUtils.getCSSVariableValue('google-fonts',style);this.googleFonts=googleFontsProperty?googleFontsProperty.split(/\s*,\s*/g):[];this.googleFonts=this.googleFonts.map(font=>font.substring(1,font.length-1));const googleLocalFontsProperty=weUtils.getCSSVariableValue('google-local-fonts',style);this.googleLocalFonts=googleLocalFontsProperty?googleLocalFontsProperty.slice(1,-1).split(/\s*,\s*/g):[];await this._super(...arguments);const fontsToLoad=[];for(const font of this.googleFonts){const fontURL=`https://fonts.googleapis.com/css?family=${encodeURIComponent(font).replace(/%20/g, '+')}`;fontsToLoad.push(fontURL);}
for(const font of this.googleLocalFonts){const attachmentId=font.split(/\s*:\s*/)[1];const fontURL=`/web/content/${encodeURIComponent(attachmentId)}`;fontsToLoad.push(fontURL);}
const proms=fontsToLoad.map(async fontURL=>loadCSS(fontURL));const fontsLoadingProm=Promise.all(proms);const fontEls=[];const methodName=this.el.dataset.methodName||'customizeWebsiteVariable';const variable=this.el.dataset.variable;_.times(nbFonts,fontNb=>{const realFontNb=fontNb+1;const fontKey=weUtils.getCSSVariableValue(`font-number-${realFontNb}`,style);let fontName=fontKey.slice(1,-1);let fontFamily=fontName;if(fontName==="SYSTEM_FONTS"){fontName=_t("System Fonts");fontFamily='var(--o-system-fonts)';}
const fontEl=document.createElement('we-button');fontEl.classList.add(`o_we_option_font_${realFontNb}`);fontEl.setAttribute('string',fontName);fontEl.dataset.variable=variable;fontEl.dataset[methodName]=fontKey;fontEl.dataset.font=realFontNb;fontEl.dataset.fontFamily=fontFamily;fontEls.push(fontEl);this.menuEl.appendChild(fontEl);});if(this.googleLocalFonts.length){const googleLocalFontsEls=fontEls.splice(-this.googleLocalFonts.length);googleLocalFontsEls.forEach((el,index)=>{$(el).append(core.qweb.render('website.delete_google_font_btn',{index:index,local:true,}));});}
if(this.googleFonts.length){const googleFontsEls=fontEls.splice(-this.googleFonts.length);googleFontsEls.forEach((el,index)=>{$(el).append(core.qweb.render('website.delete_google_font_btn',{index:index,}));});}
$(this.menuEl).append($(core.qweb.render('website.add_google_font_btn',{variable:variable,})));return fontsLoadingProm;},async setValue(){await this._super(...arguments);for(const className of this.menuTogglerEl.classList){if(className.match(/^o_we_option_font_\d+$/)){this.menuTogglerEl.classList.remove(className);}}
const activeWidget=this._userValueWidgets.find(widget=>!widget.isPreviewed()&&widget.isActive());if(activeWidget){this.menuTogglerEl.style.fontFamily=activeWidget.el.dataset.fontFamily;this.menuTogglerEl.classList.add(`o_we_option_font_${activeWidget.el.dataset.font}`);}},_onAddGoogleFontClick:function(ev){const variable=$(ev.currentTarget).data('variable');const dialog=new Dialog(this,{title:_t("Add a Google Font"),$content:$(core.qweb.render('website.dialog.addGoogleFont')),buttons:[{text:_t("Save & Reload"),classes:'btn-primary',click:async()=>{const inputEl=dialog.el.querySelector('.o_input_google_font');let m=inputEl.value.match(/\bspecimen\/([\w+]+)/);if(!m){m=inputEl.value.match(/\bfamily=([\w+]+)/);if(!m){inputEl.classList.add('is-invalid');return;}}
let isValidFamily=false;try{const result=await fetch("https://fonts.googleapis.com/css?family="+m[1]+':300,300i,400,400i,700,700i',{method:'HEAD'});if(result.ok){isValidFamily=true;}}catch(error){console.error(error);}
if(!isValidFamily){inputEl.classList.add('is-invalid');return;}
const font=m[1].replace(/\+/g,' ');const googleFontServe=dialog.el.querySelector('#google_font_serve').checked;if(googleFontServe){this.googleFonts.push(font);}else{this.googleLocalFonts.push(`'${font}': ''`);}
this.trigger_up('google_fonts_custo_request',{values:{[variable]:`'${font}'`},googleFonts:this.googleFonts,googleLocalFonts:this.googleLocalFonts,});},},{text:_t("Discard"),close:true,},],});dialog.open();},_onDeleteGoogleFontClick:async function(ev){ev.preventDefault();const values={};const save=await new Promise(resolve=>{Dialog.confirm(this,_t("Deleting a font requires a reload of the page. This will save all your changes and reload the page, are you sure you want to proceed?"),{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!save){return;}
const googleFontIndex=parseInt(ev.target.dataset.fontIndex);const isLocalFont=ev.target.dataset.localFont;let googleFontName;if(isLocalFont){const googleFont=this.googleLocalFonts[googleFontIndex].split(':');googleFontName=googleFont[0];values['delete-font-attachment-id']=googleFont[1];this.googleLocalFonts.splice(googleFontIndex,1);}else{googleFontName=this.googleFonts[googleFontIndex];this.googleFonts.splice(googleFontIndex,1);}
const style=window.getComputedStyle(this.$target[0].ownerDocument.documentElement);_.each(FontFamilyPickerUserValueWidget.prototype.fontVariables,variable=>{const value=weUtils.getCSSVariableValue(variable,style);if(value.substring(1,value.length-1)===googleFontName){values[variable]='null';}});this.trigger_up('google_fonts_custo_request',{values:values,googleFonts:this.googleFonts,googleLocalFonts:this.googleLocalFonts,});},});const GPSPicker=InputUserValueWidget.extend({events:{'blur input':'_onInputBlur',},init(){this._super(...arguments);this._gmapCacheGPSToPlace={};this.contentWindow=this.$target[0].ownerDocument.defaultView;},async willStart(){await this._super(...arguments);this._gmapLoaded=await new Promise(resolve=>{this.trigger_up('gmap_api_request',{editableMode:true,configureIfNecessary:true,onSuccess:key=>{if(!key){resolve(false);return;}
this._nearbySearch('(50.854975,4.3753899)',!!key).then(place=>resolve(!!place));},});});if(!this._gmapLoaded&&!this._gmapErrorNotified){this.trigger_up('user_value_widget_critical');return;}},async start(){await this._super(...arguments);this.el.classList.add('o_we_large');if(!this._gmapLoaded){return;}
this._gmapAutocomplete=new this.contentWindow.google.maps.places.Autocomplete(this.inputEl,{types:['geocode']});this.contentWindow.google.maps.event.addListener(this._gmapAutocomplete,'place_changed',this._onPlaceChanged.bind(this));},destroy(){this._super(...arguments);for(const el of document.body.querySelectorAll('.pac-container')){el.remove();}},getMethodsParams:function(methodName){return Object.assign({gmapPlace:this._gmapPlace||{}},this._super(...arguments));},async setValue(){await this._super(...arguments);if(!this._gmapLoaded){return;}
this._gmapPlace=await this._nearbySearch(this._value);if(this._gmapPlace){this.inputEl.value=this._gmapPlace.formatted_address;}},async _nearbySearch(gps,notify=true){if(this._gmapCacheGPSToPlace[gps]){return this._gmapCacheGPSToPlace[gps];}
const p=gps.substring(1).slice(0,-1).split(',');const location=new this.contentWindow.google.maps.LatLng(p[0]||0,p[1]||0);return new Promise(resolve=>{const service=new this.contentWindow.google.maps.places.PlacesService(document.createElement('div'));service.nearbySearch({location:location,radius:1,},(results,status)=>{const GMAP_CRITICAL_ERRORS=[this.contentWindow.google.maps.places.PlacesServiceStatus.REQUEST_DENIED,this.contentWindow.google.maps.places.PlacesServiceStatus.UNKNOWN_ERROR];if(status===this.contentWindow.google.maps.places.PlacesServiceStatus.OK){service.getDetails({placeId:results[0].place_id,fields:['geometry','formatted_address'],},(place,status)=>{if(status===this.contentWindow.google.maps.places.PlacesServiceStatus.OK){this._gmapCacheGPSToPlace[gps]=place;resolve(place);}else if(GMAP_CRITICAL_ERRORS.includes(status)){if(notify){this._notifyGMapError();}
resolve();}});}else if(GMAP_CRITICAL_ERRORS.includes(status)){if(notify){this._notifyGMapError();}
resolve();}else{resolve();}});});},_notifyGMapError(){if(this._gmapErrorNotified){return;}
this._gmapErrorNotified=true;this.displayNotification({type:'danger',sticky:true,message:_t("A Google Map error occurred. Make sure to read the key configuration popup carefully."),});this.trigger_up('gmap_api_request',{editableMode:true,reconfigure:true,onSuccess:()=>{this._gmapErrorNotified=false;},});setTimeout(()=>this.trigger_up('user_value_widget_critical'));},_onPlaceChanged(ev){const gmapPlace=this._gmapAutocomplete.getPlace();if(gmapPlace&&gmapPlace.geometry){this._gmapPlace=gmapPlace;const location=this._gmapPlace.geometry.location;const oldValue=this._value;this._value=`(${location.lat()},${location.lng()})`;this._gmapCacheGPSToPlace[this._value]=gmapPlace;if(oldValue!==this._value){this._onUserValueChange(ev);}}},_onInputBlur(){},});options.userValueWidgetsRegistry['we-urlpicker']=UrlPickerUserValueWidget;options.userValueWidgetsRegistry['we-fontfamilypicker']=FontFamilyPickerUserValueWidget;options.userValueWidgetsRegistry['we-gpspicker']=GPSPicker;options.Class.include({custom_events:_.extend({},options.Class.prototype.custom_events||{},{'google_fonts_custo_request':'_onGoogleFontsCustoRequest',}),specialCheckAndReloadMethodsNames:['customizeWebsiteViews','customizeWebsiteVariable','customizeWebsiteColor'],init(){this._super(...arguments);this.$bsTarget=this.ownerDocument.defaultView.$(this.$target[0]);},customizeWebsiteViews:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'views');},customizeWebsiteVariable:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'variable');},customizeWebsiteColor:async function(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'color');},async customizeWebsiteAssets(previewMode,widgetValue,params){await this._customizeWebsite(previewMode,widgetValue,params,'assets');},async _checkIfWidgetsUpdateNeedReload(widgets){const needReload=await this._super(...arguments);if(needReload){return needReload;}
for(const widget of widgets){const methodsNames=widget.getMethodsNames();const specialMethodsNames=[];if(this.data.pageOptions){specialMethodsNames.push(methodsNames);}else{for(const methodName of methodsNames){if(this.specialCheckAndReloadMethodsNames.includes(methodName)){specialMethodsNames.push(methodName);}}}
if(!specialMethodsNames.length){continue;}
let paramsReload=false;for(const methodName of specialMethodsNames){if(widget.getMethodsParams(methodName).reload){paramsReload=true;break;}}
if(paramsReload){return true;}}
return false;},_computeWidgetState:async function(methodName,params){switch(methodName){case'customizeWebsiteViews':{return this._getEnabledCustomizeValues(params.possibleValues,true);}
case'customizeWebsiteVariable':{const ownerDocument=this.$target[0].ownerDocument;const style=ownerDocument.defaultView.getComputedStyle(ownerDocument.documentElement);return weUtils.getCSSVariableValue(params.variable,style);}
case'customizeWebsiteColor':{const ownerDocument=this.$target[0].ownerDocument;const style=ownerDocument.defaultView.getComputedStyle(ownerDocument.documentElement);return weUtils.getCSSVariableValue(params.color,style);}
case'customizeWebsiteAssets':{return this._getEnabledCustomizeValues(params.possibleValues,false);}}
return this._super(...arguments);},_customizeWebsite:async function(previewMode,widgetValue,params,type){if(previewMode){return;}
switch(type){case'views':await this._customizeWebsiteData(widgetValue,params,true);break;case'variable':await this._customizeWebsiteVariable(widgetValue,params);break;case'color':await this._customizeWebsiteColor(widgetValue,params);break;case'assets':await this._customizeWebsiteData(widgetValue,params,false);break;default:if(params.customCustomization){await params.customCustomization.call(this,widgetValue,params);}}
if(params.reload||params.noBundleReload){return;}
await this._reloadBundles();this.trigger_up('option_update',{optionName:'ThemeColors',name:'update_color_previews',});await new Promise((resolve,reject)=>{this.trigger_up('widgets_start_request',{editableMode:true,onSuccess:()=>resolve(),onFailure:()=>reject(),});});},async _customizeWebsiteColor(color,params){await this._customizeWebsiteColors({[params.color]:color},params);},async _customizeWebsiteColors(colors,params){colors=colors||{};const baseURL='/website/static/src/scss/options/colors/';const colorType=params.colorType?(params.colorType+'_'):'';const url=`${baseURL}user_${colorType}color_palette.scss`;const finalColors={};for(const[colorName,color]of Object.entries(colors)){finalColors[colorName]=color;if(color){if(weUtils.isColorCombinationName(color)){finalColors[colorName]=parseInt(color);}else if(!ColorpickerWidget.isCSSColor(color)){finalColors[colorName]=`'${color}'`;}}}
return this._makeSCSSCusto(url,finalColors,params.nullValue);},_customizeWebsiteVariable:async function(value,params){return this._makeSCSSCusto('/website/static/src/scss/options/user_values.scss',{[params.variable]:value,},params.nullValue);},async _customizeWebsiteData(value,params,isViewData){const allDataKeys=this._getDataKeysFromPossibleValues(params.possibleValues);const enableDataKeys=value.split(/\s*,\s*/);const disableDataKeys=allDataKeys.filter(value=>!enableDataKeys.includes(value));const resetViewArch=!!params.resetViewArch;return this._rpc({route:'/website/theme_customize_data',params:{'is_view_data':isViewData,'enable':enableDataKeys,'disable':disableDataKeys,'reset_view_arch':resetViewArch,},});},_getDataKeysFromPossibleValues(possibleValues){const allDataKeys=[];for(const dataKeysStr of possibleValues){allDataKeys.push(...dataKeysStr.split(/\s*,\s*/));}
return allDataKeys.filter((v,i,arr)=>arr.indexOf(v)===i);},async _getEnabledCustomizeValues(possibleValues,isViewData){const allDataKeys=this._getDataKeysFromPossibleValues(possibleValues);const enabledValues=await this._rpc({route:'/website/theme_customize_data_get',params:{'keys':allDataKeys,'is_view_data':isViewData,},});let mostValuesStr='';let mostValuesNb=0;for(const valuesStr of possibleValues){const enableValues=valuesStr.split(/\s*,\s*/);if(enableValues.length>mostValuesNb&&enableValues.every(value=>enabledValues.includes(value))){mostValuesStr=valuesStr;mostValuesNb=enableValues.length;}}
return mostValuesStr;},_makeSCSSCusto:async function(url,values,defaultValue='null'){return this._rpc({model:'web_editor.assets',method:'make_scss_customization',args:[url,_.mapObject(values,v=>v||defaultValue)],});},_refreshPublicWidgets:async function($el){return new Promise((resolve,reject)=>{this.trigger_up('widgets_start_request',{editableMode:true,$target:$el||this.$target,onSuccess:resolve,onFailure:reject,});});},_reloadBundles:async function(){return new Promise((resolve,reject)=>{this.trigger_up('reload_bundles',{onSuccess:()=>resolve(),onFailure:()=>reject(),});});},_select:async function(previewMode,widget){await this._super(...arguments);if(this.options.isWebsite&&!widget.$el.closest('[data-no-widget-refresh="true"]').length){await this._refreshPublicWidgets();}},_onGoogleFontsCustoRequest:function(ev){const values=ev.data.values?_.clone(ev.data.values):{};const googleFonts=ev.data.googleFonts;const googleLocalFonts=ev.data.googleLocalFonts;if(googleFonts.length){values['google-fonts']="('"+googleFonts.join("', '")+"')";}else{values['google-fonts']='null';}
if(googleLocalFonts.length){values['google-local-fonts']="("+googleLocalFonts.join(", ")+")";}else{values['google-local-fonts']='null';}
this.trigger_up('snippet_edition_request',{exec:async()=>{return this._makeSCSSCusto('/website/static/src/scss/options/user_values.scss',values);}});this.trigger_up('request_save',{reloadEditor:true,});},});function _getLastPreFilterLayerElement($el){const $bgVideo=$el.find('> .o_bg_video_container');if($bgVideo.length){return $bgVideo[0];}
const $parallaxEl=$el.find('> .s_parallax_bg');if($parallaxEl.length){return $parallaxEl[0];}
return null;}
options.registry.BackgroundToggler.include({toggleBgVideo(previewMode,widgetValue,params){if(!widgetValue){this.$target.find('> .o_we_bg_filter').remove();const[bgVideoWidget]=this._requestUserValueWidgets('bg_video_opt');const bgVideoOpt=bgVideoWidget.getParent();return bgVideoOpt._setBgVideo(false,'');}else{this._requestUserValueWidgets('bg_video_opt')[0].el.click();}},_computeWidgetState(methodName,params){if(methodName==='toggleBgVideo'){return this.$target[0].classList.contains('o_background_video');}
return this._super(...arguments);},_getLastPreFilterLayerElement(){const el=_getLastPreFilterLayerElement(this.$target);if(el){return el;}
return this._super(...arguments);},});options.registry.BackgroundShape.include({_getLastPreShapeLayerElement(){const el=this._super(...arguments);if(el){return el;}
return _getLastPreFilterLayerElement(this.$target);},_removeShapeEl(shapeEl){this.trigger_up('widgets_stop_request',{$target:$(shapeEl),});return this._super(...arguments);},});options.registry.ReplaceMedia.include({setAnchor(previewMode,widgetValue,params){const linkEl=this.$target[0].parentElement;let url=linkEl.getAttribute('href');url=url.split('#')[0];linkEl.setAttribute('href',url+widgetValue);},_computeWidgetState(methodName,params){if(methodName==='setAnchor'){const parentEl=this.$target[0].parentElement;if(parentEl.tagName==='A'){const href=parentEl.getAttribute('href')||'';return href?`#${href.split('#')[1]}`:'';}
return'';}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='media_link_anchor_opt'){const parentEl=this.$target[0].parentElement;const linkEl=parentEl.tagName==='A'?parentEl:null;const href=linkEl?linkEl.getAttribute('href'):false;return href&&href.startsWith('/');}
return this._super(...arguments);},async _renderCustomXML(uiFragment){if(!this.options.isWebsite){return this._super(...arguments);}
await this._super(...arguments);const oldURLWidgetEl=uiFragment.querySelector('[data-name="media_url_opt"]');const URLWidgetEl=document.createElement('we-urlpicker');for(const{name,value}of oldURLWidgetEl.attributes){URLWidgetEl.setAttribute(name,value);}
URLWidgetEl.title=_t("Hint: Type '/' to search an existing page and '#' to link to an anchor.");oldURLWidgetEl.replaceWith(URLWidgetEl);const hrefValue=this.$target[0].parentElement.getAttribute('href');if(!hrefValue||!hrefValue.startsWith('/')){return;}
const urlWithoutAnchor=hrefValue.split('#')[0];const selectEl=document.createElement('we-select');selectEl.dataset.name='media_link_anchor_opt';selectEl.dataset.dependencies='media_url_opt';selectEl.dataset.noPreview='true';selectEl.classList.add('o_we_sublevel_1');selectEl.setAttribute('string',_t("Page Anchor"));const anchors=await wUtils.loadAnchors(urlWithoutAnchor);for(const anchor of anchors){const weButtonEl=document.createElement('we-button');weButtonEl.dataset.setAnchor=anchor;weButtonEl.textContent=anchor;selectEl.append(weButtonEl);}
URLWidgetEl.after(selectEl);},});options.registry.BackgroundVideo=options.Class.extend({background:function(previewMode,widgetValue,params){if(previewMode==='reset'&&this.videoSrc){return this._setBgVideo(false,this.videoSrc);}
return this._setBgVideo(previewMode,widgetValue);},_computeWidgetState:function(methodName,params){if(methodName==='background'){if(this.$target[0].classList.contains('o_background_video')){return this.$('> .o_bg_video_container iframe').attr('src');}
return'';}
return this._super(...arguments);},_setBgVideo:async function(previewMode,value){this.$('> .o_bg_video_container').toggleClass('d-none',previewMode===true);if(previewMode!==false){return;}
this.videoSrc=value;var target=this.$target[0];target.classList.toggle('o_background_video',!!(value&&value.length));if(value&&value.length){target.dataset.bgVideoSrc=value;}else{delete target.dataset.bgVideoSrc;}
await this._refreshPublicWidgets();},});options.registry.OptionsTab=options.Class.extend({GRAY_PARAMS:{EXTRA_SATURATION:"gray-extra-saturation",HUE:"gray-hue"},init(){this._super(...arguments);this.grayParams={};this.grays={};},async updateUI(){const ownerDocument=this.$target[0].ownerDocument;const style=ownerDocument.defaultView.getComputedStyle(ownerDocument.documentElement);const grayPreviewEls=this.$el.find(".o_we_gray_preview span");for(const e of grayPreviewEls){const bgValue=weUtils.getCSSVariableValue(e.getAttribute('variable'),style);e.style.setProperty("background-color",bgValue,"important");}
const hues=[];const saturationDiffs=[];let oneHasNoSaturation=false;const baseStyle=getComputedStyle(document.documentElement);for(let id=100;id<=900;id+=100){const gray=weUtils.getCSSVariableValue(`${id}`,style);const grayRGB=ColorpickerWidget.convertCSSColorToRgba(gray);const grayHSL=ColorpickerWidget.convertRgbToHsl(grayRGB.red,grayRGB.green,grayRGB.blue);const baseGray=weUtils.getCSSVariableValue(`base-${id}`,baseStyle);const baseGrayRGB=ColorpickerWidget.convertCSSColorToRgba(baseGray);const baseGrayHSL=ColorpickerWidget.convertRgbToHsl(baseGrayRGB.red,baseGrayRGB.green,baseGrayRGB.blue);if(grayHSL.saturation>0.01){if(grayHSL.lightness>0.01&&grayHSL.lightness<99.99){hues.push(grayHSL.hue);}
if(grayHSL.saturation<99.99){saturationDiffs.push(grayHSL.saturation-baseGrayHSL.saturation);}}else{oneHasNoSaturation=true;}}
this.grayHueIsDefined=!!hues.length;this.grayParams[this.GRAY_PARAMS.HUE]=(!hues.length)?0:Math.round((Math.atan2(hues.map(hue=>Math.sin(hue*Math.PI/180)).reduce((memo,value)=>memo+value,0)/hues.length,hues.map(hue=>Math.cos(hue*Math.PI/180)).reduce((memo,value)=>memo+value,0)/hues.length)*180/Math.PI)+360)%360;this.grayParams[this.GRAY_PARAMS.EXTRA_SATURATION]=saturationDiffs.length?saturationDiffs.reduce((memo,value)=>memo+value,0)/saturationDiffs.length:(oneHasNoSaturation?-100:100);await this._super(...arguments);},async customizeGray(previewMode,widgetValue,params){this.grayParams[params.param]=parseInt(widgetValue);for(let i=1;i<10;i++){const key=(100*i).toString();this.grays[key]=this._buildGray(key);}
this.$el.find(".o_we_gray_preview").each((_,e)=>{e.style.setProperty("background-color",this.grays[e.getAttribute('variable')],"important");});await this._customizeWebsite(previewMode,undefined,Object.assign({},params,{customCustomization:()=>{return this._customizeWebsiteColors(this.grays,Object.assign({},params,{colorType:'gray',}));},}));},async configureApiKey(previewMode,widgetValue,params){return new Promise(resolve=>{this.trigger_up('gmap_api_key_request',{editableMode:true,reconfigure:true,onSuccess:()=>resolve(),});});},async customizeBodyBgType(previewMode,widgetValue,params){if(widgetValue==='NONE'){this.bodyImageType='image';return this.customizeBodyBg(previewMode,'',params);}
this.bodyImageType=widgetValue;const widget=this._requestUserValueWidgets(params.imagepicker)[0];widget.enable();},async customizeBodyBg(previewMode,widgetValue,params){await this.customizeWebsiteVariable(previewMode,this.bodyImageType,{variable:'body-image-type'});await this.customizeWebsiteVariable(previewMode,widgetValue?`'${widgetValue}'`:'',{variable:'body-image'});},async openCustomCodeDialog(previewMode,widgetValue,params){const libsProm=loadBundle({jsLibs:['/web/static/lib/ace/ace.js','/web/static/lib/ace/mode-xml.js','/web/static/lib/ace/mode-qweb.js',],});let websiteId;this.trigger_up('context_get',{callback:(ctx)=>{websiteId=ctx['website_id'];},});let website;const dataProm=this._rpc({model:'website',method:'read',args:[[websiteId],['custom_code_head','custom_code_footer']],}).then(websites=>{website=websites[0];});let fieldName,title,contentText;if(widgetValue==='head'){fieldName='custom_code_head';title=_t('Custom head code');contentText=_t('Enter code that will be added into the <head> of every page of your site.');}else{fieldName='custom_code_footer';title=_t('Custom end of body code');contentText=_t('Enter code that will be added before the </body> of every page of your site.');}
await Promise.all([libsProm,dataProm]);await new Promise(resolve=>{const $content=$(core.qweb.render('website.custom_code_dialog_content',{contentText,}));const aceEditor=this._renderAceEditor($content.find('.o_ace_editor_container')[0],website[fieldName]||'');const dialog=new Dialog(this,{title,$content,buttons:[{text:_t("Save"),classes:'btn-primary',click:async()=>{await this._rpc({model:'website',method:'write',args:[[websiteId],{[fieldName]:aceEditor.getValue()},],});},close:true,},{text:_t("Discard"),close:true,},],});dialog.on('closed',this,resolve);dialog.open();});},async switchTheme(previewMode,widgetValue,params){const save=await new Promise(resolve=>{Dialog.confirm(this,_t("Changing theme requires to leave the editor. This will save all your changes, are you sure you want to proceed? Be careful that changing the theme will reset all your color customizations."),{confirm_callback:()=>resolve(true),cancel_callback:()=>resolve(false),});});if(!save){return;}
this.trigger_up('request_save',{reload:false,action:'website.theme_install_kanban_action',});},_buildGray(id){const gray=weUtils.getCSSVariableValue(`base-${id}`,getComputedStyle(document.documentElement));const grayRGB=ColorpickerWidget.convertCSSColorToRgba(gray);const hsl=ColorpickerWidget.convertRgbToHsl(grayRGB.red,grayRGB.green,grayRGB.blue);const adjustedGrayRGB=ColorpickerWidget.convertHslToRgb(this.grayParams[this.GRAY_PARAMS.HUE],Math.min(Math.max(hsl.saturation+this.grayParams[this.GRAY_PARAMS.EXTRA_SATURATION],0),100),hsl.lightness);return ColorpickerWidget.convertRgbaToCSSColor(adjustedGrayRGB.red,adjustedGrayRGB.green,adjustedGrayRGB.blue);},async _renderCustomXML(uiFragment){await this._super(...arguments);const extraSaturationRangeEl=uiFragment.querySelector(`we-range[data-param=${this.GRAY_PARAMS.EXTRA_SATURATION}]`);if(extraSaturationRangeEl){const baseGrays=_.range(100,1000,100).map(id=>{const gray=weUtils.getCSSVariableValue(`base-${id}`);const grayRGB=ColorpickerWidget.convertCSSColorToRgba(gray);const hsl=ColorpickerWidget.convertRgbToHsl(grayRGB.red,grayRGB.green,grayRGB.blue);return{id:id,hsl:hsl};});const first=baseGrays[0];const maxValue=baseGrays.reduce((gray,value)=>{return gray.hsl.saturation>value.hsl.saturation?gray:value;},first);const minValue=baseGrays.reduce((gray,value)=>{return gray.hsl.saturation<value.hsl.saturation?gray:value;},first);extraSaturationRangeEl.dataset.max=100-minValue.hsl.saturation;extraSaturationRangeEl.dataset.min=-maxValue.hsl.saturation;}
uiFragment.querySelectorAll('we-colorpicker').forEach(el=>{el.dataset.lazyPalette='true';});},async _checkIfWidgetsUpdateNeedWarning(widgets){const warningMessage=await this._super(...arguments);if(warningMessage){return warningMessage;}
for(const widget of widgets){if(widget.getMethodsNames().includes('customizeWebsiteVariable')&&widget.getMethodsParams('customizeWebsiteVariable').variable==='color-palettes-name'){const hasCustomizedColors=weUtils.getCSSVariableValue('has-customized-colors');if(hasCustomizedColors&&hasCustomizedColors!=='false'){return _t("Changing the color palette will reset all your color customizations, are you sure you want to proceed?");}}}
return'';},async _computeWidgetState(methodName,params){if(methodName==='customizeBodyBgType'){const bgImage=getComputedStyle(this.ownerDocument.querySelector('#wrapwrap'))['background-image'];if(bgImage==='none'){return"NONE";}
return weUtils.getCSSVariableValue('body-image-type');}
if(methodName==='customizeGray'){return this.grayParams[params.param];}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='body_bg_image_opt'){return false;}
if(params.param===this.GRAY_PARAMS.HUE){return this.grayHueIsDefined;}
return this._super(...arguments);},_renderAceEditor(node,content){const aceEditor=window.ace.edit(node);aceEditor.setTheme('ace/theme/monokai');aceEditor.setValue(content,1);aceEditor.setOptions({minLines:20,maxLines:Infinity,showPrintMargin:false,});aceEditor.renderer.setOptions({highlightGutterLine:true,showInvisibles:true,fontSize:14,});const aceSession=aceEditor.getSession();aceSession.setOptions({mode:"ace/mode/qweb",useWorker:false,});return aceEditor;},});options.registry.ThemeColors=options.registry.OptionsTab.extend({async start(){const style=window.getComputedStyle(this.$target[0].ownerDocument.documentElement);const supportOldColorSystem=weUtils.getCSSVariableValue('support-13-0-color-system',style)==='true';const hasCustomizedOldColorSystem=weUtils.getCSSVariableValue('has-customized-13-0-color-system',style)==='true';this._showOldColorSystemWarning=supportOldColorSystem&&hasCustomizedOldColorSystem;return this._super(...arguments);},notify(name,data){if(name==='update_color_previews'){this.updateColorPreviews=true;}},async updateUIVisibility(){await this._super(...arguments);const oldColorSystemEl=this.el.querySelector('.o_old_color_system_warning');oldColorSystemEl.classList.toggle('d-none',!this._showOldColorSystemWarning);},async updateUI(){if(this.updateColorPreviews){this.trigger_up('update_color_previews');this.updateColorPreviews=false;}
await this._super(...arguments);},_select(){this.updateColorPreviews=true;return this._super(...arguments);},async _renderCustomXML(uiFragment){const paletteSelectorEl=uiFragment.querySelector('[data-variable="color-palettes-name"]');const style=window.getComputedStyle(document.documentElement);const allPaletteNames=weUtils.getCSSVariableValue('palette-names',style).split(', ').map((name)=>{return name.replace(/'/g,"");});for(const paletteName of allPaletteNames){const btnEl=document.createElement('we-button');btnEl.classList.add('o_palette_color_preview_button');btnEl.dataset.customizeWebsiteVariable=`'${paletteName}'`;[1,3,2].forEach(c=>{const colorPreviewEl=document.createElement('span');colorPreviewEl.classList.add('o_palette_color_preview');const color=weUtils.getCSSVariableValue(`o-palette-${paletteName}-o-color-${c}`,style);colorPreviewEl.style.backgroundColor=color;btnEl.appendChild(colorPreviewEl);});paletteSelectorEl.appendChild(btnEl);}
const presetCollapseEl=uiFragment.querySelector('we-collapse.o_we_theme_presets_collapse');let ccPreviewEls=[];for(let i=1;i<=5;i++){const collapseEl=document.createElement('we-collapse');const ccPreviewEl=$(qweb.render('web_editor.color.combination.preview'))[0];ccPreviewEl.classList.add('text-center',`o_cc${i}`,'o_colored_level','o_we_collapse_toggler');collapseEl.appendChild(ccPreviewEl);const editionEls=$(qweb.render('website.color_combination_edition',{number:i}));for(const el of editionEls){collapseEl.appendChild(el);}
ccPreviewEls.push(ccPreviewEl);presetCollapseEl.appendChild(collapseEl);}
this.trigger_up('update_color_previews');await this._super(...arguments);},});options.registry.menu_data=options.Class.extend({start:function(){const wysiwyg=$(this.ownerDocument.getElementById('wrapwrap')).data('wysiwyg');const popoverContainer=this.ownerDocument.getElementById('oe_manipulators');wLinkPopoverWidget.createFor(this,this.$target[0],{wysiwyg,container:popoverContainer});return this._super(...arguments);},onBlur:function(){this.$target.popover('hide');},});options.registry.company_data=options.Class.extend({start:function(){var proto=options.registry.company_data.prototype;var prom;var self=this;if(proto.__link===undefined){prom=this._rpc({route:'/web/session/get_session_info'}).then(function(session){return self._rpc({model:'res.users',method:'read',args:[session.uid,['company_id']],});}).then(function(res){proto.__link='/web#action=base.action_res_company_form&view_type=form&id='+encodeURIComponent(res&&res[0]&&res[0].company_id[0]||1);});}
return Promise.all([this._super.apply(this,arguments),prom]);},onFocus:function(){var self=this;var proto=options.registry.company_data.prototype;Dialog.confirm(this,_t("Do you want to edit the company data ?"),{confirm_callback:function(){self.trigger_up('request_save',{reload:false,onSuccess:function(){window.location.href=proto.__link;},});},});},});options.registry.Carousel=options.Class.extend({start:function(){this.$bsTarget.carousel('pause');this.$indicators=this.$target.find('.carousel-indicators');this.$controls=this.$target.find('.carousel-control-prev, .carousel-control-next, .carousel-indicators');this.$controls.addClass('o_we_no_overlay');let _slideTimestamp;this.$bsTarget.on('slide.bs.carousel.carousel_option',()=>{_slideTimestamp=window.performance.now();setTimeout(()=>this.trigger_up('hide_overlay'));});this.$bsTarget.on('slid.bs.carousel.carousel_option',()=>{const _slideDuration=(window.performance.now()-_slideTimestamp);setTimeout(()=>{this.trigger_up('activate_snippet',{$snippet:this.$target.find('.carousel-item.active'),ifInactiveOptions:true,});this.$bsTarget.trigger('active_slide_targeted');},0.2*_slideDuration);});return this._super.apply(this,arguments);},destroy:function(){this._super.apply(this,arguments);this.$bsTarget.off('.carousel_option');},onBuilt:function(){this._assignUniqueID();},onClone:function(){this._assignUniqueID();},cleanForSave:function(){const $items=this.$target.find('.carousel-item');$items.removeClass('next prev left right active').first().addClass('active');this.$indicators.find('li').removeClass('active').empty().first().addClass('active');},notify:function(name,data){this._super(...arguments);if(name==='add_slide'){this._addSlide();}},addSlide(previewMode,widgetValue,params){this._addSlide();},_assignUniqueID:function(){const id='myCarousel'+Date.now();this.$target.attr('id',id);this.$target.find('[data-bs-target]').attr('data-bs-target','#'+id);_.each(this.$target.find('[data-bs-slide], [data-bs-slide-to]'),function(el){var $el=$(el);if($el.attr('data-bs-target')){$el.attr('data-bs-target','#'+id);}else if($el.attr('href')){$el.attr('href','#'+id);}});},_addSlide(){const $items=this.$target.find('.carousel-item');this.$controls.removeClass('d-none');const $active=$items.filter('.active');this.$indicators.append($('<li>',{'data-bs-target':'#'+this.$target.attr('id'),'data-bs-slide-to':$items.length,}));this.$indicators.append(' ');$active.clone(false).removeClass('active').insertAfter($active);this.$bsTarget.carousel('next');},});options.registry.CarouselItem=options.Class.extend({isTopOption:true,forceNoDeleteButton:true,start:function(){this.$carousel=this.$bsTarget.closest('.carousel');this.$indicators=this.$carousel.find('.carousel-indicators');this.$controls=this.$carousel.find('.carousel-control-prev, .carousel-control-next, .carousel-indicators');var leftPanelEl=this.$overlay.data('$optionsSection')[0];var titleTextEl=leftPanelEl.querySelector('we-title > span');this.counterEl=document.createElement('span');titleTextEl.appendChild(this.counterEl);return this._super(...arguments);},destroy:function(){this._super(...arguments);this.$carousel.off('.carousel_item_option');},updateUI:async function(){await this._super(...arguments);const $items=this.$carousel.find('.carousel-item');const $activeSlide=$items.filter('.active');const updatedText=` (${$activeSlide.index() + 1}/${$items.length})`;this.counterEl.textContent=updatedText;},addSlideItem(previewMode,widgetValue,params){this.trigger_up('option_update',{optionName:'Carousel',name:'add_slide',});},removeSlide:function(previewMode){const $items=this.$carousel.find('.carousel-item');const newLength=$items.length-1;if(!this.removing&&newLength>0){const $toDelete=$items.filter('.active').add(this.$indicators.find('.active'));this.$carousel.one('active_slide_targeted.carousel_item_option',()=>{$toDelete.remove();const indicatorsEls=this.$indicators[0].querySelectorAll('li');for(let i=0;i<indicatorsEls.length;i++){indicatorsEls[i].setAttribute('data-bs-slide-to',i);}
this.$controls.toggleClass('d-none',newLength===1);this.$carousel.trigger('content_changed');this.removing=false;});this.removing=true;this.$carousel.carousel('prev');}},switchToSlide:function(previewMode,widgetValue,params){switch(widgetValue){case'left':this.$controls.filter('.carousel-control-prev')[0].click();break;case'right':this.$controls.filter('.carousel-control-next')[0].click();break;}},});options.registry.Parallax=options.Class.extend({async start(){this.parallaxEl=this.$target.find('> .s_parallax_bg')[0]||null;this._updateBackgroundOptions();this.$target.on('content_changed.ParallaxOption',this._onExternalUpdate.bind(this));return this._super(...arguments);},onFocus(){if(this.parallaxEl){this._refreshPublicWidgets();}},onMove(){this._refreshPublicWidgets();},destroy(){this._super(...arguments);this.$target.off('.ParallaxOption');},async selectDataAttribute(previewMode,widgetValue,params){await this._super(...arguments);if(params.attributeName!=='scrollBackgroundRatio'){return;}
const isParallax=(widgetValue!=='0');this.$target.toggleClass('parallax',isParallax);this.$target.toggleClass('s_parallax_is_fixed',widgetValue==='1');this.$target.toggleClass('s_parallax_no_overflow_hidden',(widgetValue==='0'||widgetValue==='1'));if(isParallax){if(!this.parallaxEl){this.parallaxEl=document.createElement('span');this.parallaxEl.classList.add('s_parallax_bg');this.$target.prepend(this.parallaxEl);}}else{if(this.parallaxEl){this.parallaxEl.remove();this.parallaxEl=null;}}
this._updateBackgroundOptions();},async _computeVisibility(widgetName){return!this.$target.hasClass('o_background_video');},async _computeWidgetState(methodName,params){if(methodName==='selectDataAttribute'&&params.parallaxTypeOpt){const attrName=params.attributeName;const attrValue=(this.$target[0].dataset[attrName]||params.attributeDefaultValue).trim();switch(attrValue){case'0':case'1':{return attrValue;}
default:{return(attrValue.startsWith('-')?'-1.5':'1.5');}}}
return this._super(...arguments);},_updateBackgroundOptions(){this.trigger_up('option_update',{optionNames:['BackgroundImage','BackgroundPosition','BackgroundOptimize'],name:'target',data:this.parallaxEl?$(this.parallaxEl):this.$target,});},_onExternalUpdate(ev){if(!this.parallaxEl){return;}
const bgImage=this.parallaxEl.style.backgroundImage;if(!bgImage||bgImage==='none'||this.$target.hasClass('o_background_video')){const widget=this._requestUserValueWidgets('parallax_none_opt')[0];widget.enable();widget.getParent().close();}},});options.registry.collapse=options.Class.extend({start:function(){var self=this;this.$bsTarget.on('shown.bs.collapse hidden.bs.collapse','[role="tabpanel"]',function(){self.trigger_up('cover_update');self.$target.trigger('content_changed');});return this._super.apply(this,arguments);},onBuilt:function(){this._createIDs();},onClone:function(){this._createIDs();},onMove:function(){this._createIDs();var $panel=this.$bsTarget.find('.collapse').removeData('bs.collapse');if($panel.attr('aria-expanded')==='true'){$panel.closest('.accordion').find('.collapse[aria-expanded="true"]').filter((i,el)=>(el!==$panel[0])).collapse('hide').one('hidden.bs.collapse',function(){$panel.trigger('shown.bs.collapse');});}},_createIDs:function(){let time=new Date().getTime();const $tablist=this.$target.closest('[role="tablist"]');const $tab=this.$target.find('[role="tab"]');const $panel=this.$target.find('[role="tabpanel"]');const $body=this.$target.closest('body');const setUniqueId=($elem,label)=>{let elemId=$elem.attr('id');if(!elemId||$body.find('[id="'+elemId+'"]').length>1){do{time++;elemId=label+time;}while($body.find('#'+elemId).length);$elem.attr('id',elemId);}
return elemId;};const tablistId=setUniqueId($tablist,'myCollapse');$panel.attr('data-bs-parent','#'+tablistId);$panel.data('bs-parent','#'+tablistId);const panelId=setUniqueId($panel,'myCollapseTab');$tab.attr('data-bs-target','#'+panelId);$tab.data('bs-target','#'+panelId);},});options.registry.WebsiteLevelColor=options.Class.extend({specialCheckAndReloadMethodsNames:options.Class.prototype.specialCheckAndReloadMethodsNames.concat(['customizeWebsiteLayer2Color']),async customizeWebsiteLayer2Color(previewMode,widgetValue,params){if(previewMode){return;}
params.color=params.layerColor;params.variable=params.layerGradient;let color=undefined;let gradient=undefined;if(weUtils.isColorGradient(widgetValue)){color='';gradient=widgetValue;}else{color=widgetValue;gradient='';}
await this.customizeWebsiteVariable(previewMode,gradient,params);params.noBundleReload=false;return this.customizeWebsiteColor(previewMode,color,params);},async _computeWidgetState(methodName,params){if(methodName==='customizeWebsiteLayer2Color'){params.variable=params.layerGradient;const gradient=await this._computeWidgetState('customizeWebsiteVariable',params);if(gradient){return gradient.substring(1,gradient.length-1);}
params.color=params.layerColor;return this._computeWidgetState('customizeWebsiteColor',params);}
return this._super(...arguments);},});options.registry.HeaderLayout=options.registry.WebsiteLevelColor.extend({async customizeWebsiteViews(previewMode,widgetValue,params){const _super=this._super.bind(this);if(params.name==='header_sidebar_opt'){await new Promise(resolve=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_overlay',value:false}],onSuccess:()=>resolve(),});});}
return _super(...arguments);}});options.registry.HeaderNavbar=options.Class.extend({init(){this._super(...arguments);this.setTarget(this.$target.closest('#wrapwrap > header'));},async updateUIVisibility(){await this._super(...arguments);const noHamburgerWidget=this.findWidget('no_hamburger_opt');const noHamburgerHidden=noHamburgerWidget.$el.hasClass('d-none');if(noHamburgerHidden&&noHamburgerWidget.isActive()){this.findWidget('default_hamburger_opt').enable();}
const hamburgerTypeWidget=this.findWidget('header_hamburger_type_opt');const labelEl=hamburgerTypeWidget.el.querySelector('we-title');if(!this._originalHamburgerTypeLabel){this._originalHamburgerTypeLabel=labelEl.textContent;}
labelEl.textContent=noHamburgerHidden?this._originalHamburgerTypeLabel:_t("Mobile menu");},async start(){await this._super(...arguments);const signInOptionEl=this.el.querySelector('[data-customize-website-views="portal.user_sign_in"]');signInOptionEl.dataset.noPreview='true';},async updateUI(){await this._super(...arguments);if(!["'default'","'hamburger'","'sidebar'"].includes(weUtils.getCSSVariableValue('header-template'))){const alignmentOptionTitleEl=this.el.querySelector('[data-name="header_alignment_opt"] we-title');alignmentOptionTitleEl.textContent=_t("Mobile Alignment");}},async _computeWidgetVisibility(widgetName,params){switch(widgetName){case'option_logo_height_scrolled':{return!!this.$('.navbar-brand').length;}
case'no_hamburger_opt':{return!weUtils.getCSSVariableValue('header-template').includes('hamburger');}}
if(widgetName==='header_alignment_opt'){if(!this.$target[0].querySelector('.o_offcanvas_menu_toggler')){return!["'hamburger-full'","'magazine'"].includes(weUtils.getCSSVariableValue('header-template'));}
return true;}
return this._super(...arguments);},});const VisibilityPageOptionUpdate=options.Class.extend({pageOptionName:undefined,showOptionWidgetName:undefined,shownValue:'',async start(){await this._super(...arguments);this._isShown().then(isShown=>{this.trigger_up('snippet_option_visibility_update',{show:isShown});});},async onTargetShow(){if(await this._isShown()){return;}
const widget=this._requestUserValueWidgets(this.showOptionWidgetName)[0];widget.enable();},async visibility(previewMode,widgetValue,params){const show=(widgetValue!=='hidden');await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:this.pageOptionName,value:show}],onSuccess:()=>resolve(),onFailure:reject,});});this.trigger_up('snippet_option_visibility_update',{show:show});},async _computeWidgetState(methodName,params){if(methodName==='visibility'){const shown=await this._isShown();return shown?this.shownValue:'hidden';}
return this._super(...arguments);},async _isShown(){return new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:[this.pageOptionName],onSuccess:v=>resolve(!!v),onFailure:reject,});});},});options.registry.TopMenuVisibility=VisibilityPageOptionUpdate.extend({pageOptionName:'header_visible',showOptionWidgetName:'regular_header_visibility_opt',async visibility(previewMode,widgetValue,params){await this._super(...arguments);await this._changeVisibility(widgetValue);const targetWindow=this.$target[0].ownerDocument.defaultView;targetWindow.dispatchEvent(new targetWindow.Event('resize'));},async _changeVisibility(widgetValue){const show=(widgetValue!=='hidden');if(!show){return;}
const transparent=(widgetValue==='transparent');await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_overlay',value:transparent}],onSuccess:()=>resolve(),onFailure:reject,});});if(!transparent){return;}
await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_color',value:''}],onSuccess:()=>resolve(),onFailure:reject,});});},async _computeWidgetState(methodName,params){const _super=this._super.bind(this);if(methodName==='visibility'){this.shownValue=await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:['header_overlay'],onSuccess:v=>resolve(v?'transparent':'regular'),onFailure:reject,});});}
return _super(...arguments);},});options.registry.topMenuColor=options.Class.extend({async selectStyle(previewMode,widgetValue,params){await this._super(...arguments);const className=widgetValue?(params.colorPrefix+widgetValue):'';await new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'toggle_page_option',params:[{name:'header_color',value:className}],onSuccess:resolve,onFailure:reject,});});},_computeVisibility:async function(){const show=await this._super(...arguments);if(!show){return false;}
return new Promise((resolve,reject)=>{this.trigger_up('action_demand',{actionName:'get_page_option',params:['header_overlay'],onSuccess:value=>resolve(!!value),onFailure:reject,});});},});options.registry.DeviceVisibility=options.Class.extend({async toggleDeviceVisibility(previewMode,widgetValue,params){this.$target[0].classList.remove('d-none','d-md-none','d-lg-none','o_snippet_mobile_invisible','o_snippet_desktop_invisible','o_snippet_override_invisible',);const style=getComputedStyle(this.$target[0]);this.$target[0].classList.remove(`d-md-${style['display']}`,`d-lg-${style['display']}`);if(widgetValue==='no_desktop'){this.$target[0].classList.add('d-lg-none','o_snippet_desktop_invisible');}else if(widgetValue==='no_mobile'){this.$target[0].classList.add(`d-lg-${style['display']}`,'d-none','o_snippet_mobile_invisible');}
let isMobile;this.trigger_up('service_context_get',{callback:(ctx)=>{isMobile=ctx['isMobile'];},});this.trigger_up('snippet_option_visibility_update',{show:widgetValue!==(isMobile?'no_mobile':'no_desktop')});},async onTargetHide(){this.$target[0].classList.remove('o_snippet_override_invisible');},async onTargetShow(){if(this.$target[0].classList.contains('o_snippet_mobile_invisible')||this.$target[0].classList.contains('o_snippet_desktop_invisible')){this.$target[0].classList.add('o_snippet_override_invisible');}},cleanForSave(){this.$target[0].classList.remove('o_snippet_override_invisible');},async _computeWidgetState(methodName,params){if(methodName==='toggleDeviceVisibility'){const classList=[...this.$target[0].classList];if(classList.includes('d-none')&&classList.some(className=>className.match(/^d-(md|lg)-/))){return'no_mobile';}
if(classList.some(className=>className.match(/d-(md|lg)-none/))){return'no_desktop';}
return'';}
return await this._super(...arguments);},_computeWidgetVisibility(widgetName,params){if(this.$target[0].classList.contains('s_table_of_content_main')){return false;}
return this._super(...arguments);}});options.registry.HideFooter=VisibilityPageOptionUpdate.extend({pageOptionName:'footer_visible',showOptionWidgetName:'hide_footer_page_opt',shownValue:'shown',});options.registry.anchor=options.Class.extend({isTopOption:true,start:function(){this.$button=this.$el.find('we-button');const clipboard=new ClipboardJS(this.$button[0],{text:()=>this._getAnchorLink()});clipboard.on('success',()=>{const anchor=decodeURIComponent(this._getAnchorLink());const message=sprintf(Markup(_t("Anchor copied to clipboard<br>Link: %s")),anchor);this.displayNotification({type:'success',message:message,buttons:[{text:_t("Edit"),click:()=>this.openAnchorDialog(),primary:true}],});});return this._super.apply(this,arguments);},onClone:function(){this.$target.removeAttr('data-anchor');this.$target.filter(':not(.carousel)').removeAttr('id');},openAnchorDialog:function(previewMode,widgetValue,params){var self=this;var buttons=[{text:_t("Save & copy"),classes:'btn-primary',click:function(){var $input=this.$('.o_input_anchor_name');var anchorName=self._text2Anchor($input.val());if(self.$target[0].id===anchorName){this.close();return;}
const alreadyExists=!!document.getElementById(anchorName);this.$('.o_anchor_already_exists').toggleClass('d-none',!alreadyExists);$input.toggleClass('is-invalid',alreadyExists);if(!alreadyExists){self._setAnchorName(anchorName);this.close();self.$button[0].click();}},},{text:_t("Discard"),close:true,}];if(this.$target.attr('id')){buttons.push({text:_t("Remove"),classes:'btn-link ms-auto',icon:'fa-trash',close:true,click:function(){self._setAnchorName();},});}
new Dialog(this,{title:_t("Link Anchor"),$content:$(qweb.render('website.dialog.anchorName',{currentAnchor:decodeURIComponent(this.$target.attr('id')),})),buttons:buttons,}).open();},_setAnchorName:function(value){if(value){this.$target.attr({'id':value,'data-anchor':true,});}else{this.$target.removeAttr('id data-anchor');}
this.$target.trigger('content_changed');},_getAnchorLink:function(){if(!this.$target[0].id){const $titles=this.$target.find('h1, h2, h3, h4, h5, h6');const title=$titles.length>0?$titles[0].innerText:this.data.snippetName;const anchorName=this._text2Anchor(title);let n='';while(document.getElementById(anchorName+n)){n=(n||1)+1;}
this._setAnchorName(anchorName+n);}
return`${this.ownerDocument.location.pathname}#${this.$target[0].id}`;},_text2Anchor:function(text){return encodeURIComponent(text.trim().replace(/\s+/g,'-'));},});options.registry.HeaderBox=options.registry.Box.extend({async selectStyle(previewMode,widgetValue,params){if((params.variable||params.color)&&['border-width','border-style','border-color','border-radius','box-shadow'].includes(params.cssProperty)){if(previewMode){return;}
if(params.cssProperty==='border-color'){return this.customizeWebsiteColor(previewMode,widgetValue,params);}
return this.customizeWebsiteVariable(previewMode,widgetValue,params);}
return this._super(...arguments);},async setShadow(previewMode,widgetValue,params){if(params.variable){if(previewMode){return;}
const defaultShadow=this._getDefaultShadow(widgetValue,params.shadowClass);return this.customizeWebsiteVariable(previewMode,defaultShadow,params);}
return this._super(...arguments);},});options.registry.CookiesBar=options.registry.SnippetPopup.extend({selectLayout:function(previewMode,widgetValue,params){let websiteId;this.trigger_up('context_get',{callback:function(ctx){websiteId=ctx['website_id'];},});const $template=$(qweb.render(`website.cookies_bar.${widgetValue}`,{websiteId:websiteId,}));const $content=this.$target.find('.modal-content');const selectorsToKeep=['.o_cookies_bar_text_button','.o_cookies_bar_text_button_essential','.o_cookies_bar_text_policy','.o_cookies_bar_text_title','.o_cookies_bar_text_primary','.o_cookies_bar_text_secondary',];if(this.$savedSelectors===undefined){this.$savedSelectors=[];}
for(const selector of selectorsToKeep){const $currentLayoutEls=$content.find(selector).contents();const $newLayoutEl=$template.find(selector);if($currentLayoutEls.length){this.$savedSelectors[selector]=$currentLayoutEls;}
const $savedSelector=this.$savedSelectors[selector];if($newLayoutEl.length&&$savedSelector&&$savedSelector.length){$newLayoutEl.empty().append($savedSelector);}}
$content.empty().append($template);},});options.registry.CoverProperties=options.Class.extend({init:function(){this._super.apply(this,arguments);this.$image=this.$target.find('.o_record_cover_image');this.$filter=this.$target.find('.o_record_cover_filter');},start:function(){this.$filterValueOpts=this.$el.find('[data-filter-value]');return this._super.apply(this,arguments);},background:async function(previewMode,widgetValue,params){if(widgetValue===''){this.$image.css('background-image','');this.$target.removeClass('o_record_has_cover');}else{this.$image.css('background-image',`url('${widgetValue}')`);this.$target.addClass('o_record_has_cover');const $defaultSizeBtn=this.$el.find('.o_record_cover_opt_size_default');$defaultSizeBtn.click();$defaultSizeBtn.closest('we-select').click();}
if(!previewMode){this._updateSavingDataset();}},filterValue:function(previewMode,widgetValue,params){this.$filter.css('opacity',widgetValue||0);this.$filter.toggleClass('oe_black',parseFloat(widgetValue)!==0);if(!previewMode){this._updateSavingDataset();}},selectStyle:async function(previewMode,widgetValue,params){await this._super(...arguments);if(!previewMode){this._updateSavingDataset(widgetValue);}},selectClass:async function(previewMode,widgetValue,params){await this._super(...arguments);if(!previewMode){this._updateSavingDataset();}},_computeWidgetState:function(methodName,params){switch(methodName){case'filterValue':{return parseFloat(this.$filter.css('opacity')).toFixed(1);}
case'background':{const background=this.$image.css('background-image');if(background&&background!=='none'){return background.match(/^url\(["']?(.+?)["']?\)$/)[1];}
return'';}}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){if(params.coverOptName){return this.$target.data(`use_${params.coverOptName}`)==='True';}
return this._super(...arguments);},_updateColorDataset(bgColorStyle='',bgColorClass=''){this.$target[0].dataset.bgColorStyle=bgColorStyle;this.$target[0].dataset.bgColorClass=bgColorClass;},_updateSavingDataset(colorValue){const[colorPickerWidget,sizeWidget,textAlignWidget]=this._requestUserValueWidgets('bg_color_opt','size_opt','text_align_opt');const sizeOptValues=sizeWidget.getMethodsParams('selectClass').possibleValues;let coverClass=[...this.$target[0].classList].filter(value=>sizeOptValues.includes(value)).join(' ');const bg=this.$image.css('background-image');if(bg&&bg!=='none'){coverClass+=" o_record_has_cover";}
const textAlignOptValues=textAlignWidget.getMethodsParams('selectClass').possibleValues;const textAlignClass=[...this.$target[0].classList].filter(value=>textAlignOptValues.includes(value)).join(' ');const filterEl=this.$target[0].querySelector('.o_record_cover_filter');const filterValue=filterEl&&filterEl.style.opacity;this.$target[0].dataset.coverClass=coverClass;this.$target[0].dataset.textAlignClass=textAlignClass;this.$target[0].dataset.filterValue=filterValue||0.0;const ccValue=colorPickerWidget._ccValue;const colorOrGradient=colorPickerWidget._value;const isGradient=weUtils.isColorGradient(colorOrGradient);const isCSSColor=!isGradient&&ColorpickerWidget.isCSSColor(colorOrGradient);const colorNames=[];if(ccValue){colorNames.push(ccValue);}
if(colorOrGradient&&!isGradient&&!isCSSColor){colorNames.push(colorOrGradient);}
const bgColorClass=weUtils.computeColorClasses(colorNames).join(' ');const bgColorStyle=isCSSColor?`background-color: ${colorOrGradient};`:isGradient?`background-color: rgba(0, 0, 0, 0); background-image: ${colorOrGradient};`:'';this._updateColorDataset(bgColorStyle,bgColorClass);},});options.registry.ScrollButton=options.Class.extend({start:async function(){await this._super(...arguments);this.$button=this.$('.o_scroll_button');},async showScrollButton(previewMode,widgetValue,params){if(widgetValue){this.$button.show();}else{if(previewMode){this.$button.hide();}else{this.$button.detach();}}},toggleButton:function(previewMode,widgetValue,params){if(widgetValue){if(!this.$button.length){const anchor=document.createElement('a');anchor.classList.add('o_scroll_button','mb-3','rounded-circle','align-items-center','justify-content-center','mx-auto','bg-primary','o_not_editable',);anchor.href='#';anchor.contentEditable="false";anchor.title=_t("Scroll down to next section");const arrow=document.createElement('i');arrow.classList.add('fa','fa-angle-down','fa-3x');anchor.appendChild(arrow);this.$button=$(anchor);}
this.$target.append(this.$button);}else{this.$button.detach();}},_renderCustomXML(uiFragment){if(this.$target[0].dataset.snippet!=='s_image_gallery'){return;}
let minHeightEl=uiFragment.querySelector('[data-name="minheight_auto_opt"]');if(!minHeightEl){return;}
minHeightEl=minHeightEl.parentElement;minHeightEl.setAttribute('string',_t("Min-Height"));const heightEl=document.createElement('we-input');heightEl.setAttribute('string',_t("Height"));heightEl.classList.add('o_we_sublevel_1');heightEl.dataset.dependencies='minheight_auto_opt';heightEl.dataset.unit='px';heightEl.dataset.selectStyle='';heightEl.dataset.cssProperty='height';heightEl.dataset.forceStyle='';uiFragment.appendChild(heightEl);},_computeWidgetState:function(methodName,params){switch(methodName){case'toggleButton':return!!this.$button.parent().length;}
return this._super(...arguments);},});options.registry.ConditionalVisibility=options.registry.DeviceVisibility.extend({init(){this._super(...arguments);this.optionsAttributes=[];},async start(){await this._super(...arguments);for(const widget of this._userValueWidgets){const params=widget.getMethodsParams();if(params.saveAttribute){this.optionsAttributes.push({saveAttribute:params.saveAttribute,attributeName:params.attributeName,callWith:params.callWith||'value',});}}},async onTargetHide(){await this._super(...arguments);if(this.$target[0].classList.contains('o_snippet_invisible')){this.$target[0].classList.add('o_conditional_hidden');}},async onTargetShow(){await this._super(...arguments);this.$target[0].classList.remove('o_conditional_hidden');},async cleanForSave(){await this._super(...arguments);if(this.$target[0].classList.contains('o_snippet_invisible')){this.trigger_up('snippet_option_visibility_update',{show:true});}},selectRecord(previewMode,widgetValue,params){const recordsData=JSON.parse(widgetValue);if(recordsData.length){this.$target[0].dataset[params.saveAttribute]=widgetValue;}else{delete this.$target[0].dataset[params.saveAttribute];}
this._updateCSSSelectors();},selectValue(previewMode,widgetValue,params){if(widgetValue){const widgetValueIndex=params.possibleValues.indexOf(widgetValue);const value=[{value:widgetValue,id:widgetValueIndex}];this.$target[0].dataset[params.saveAttribute]=JSON.stringify(value);}else{delete this.$target[0].dataset[params.saveAttribute];}
this._updateCSSSelectors();},async selectDataAttribute(previewMode,widgetValue,params){await this._super(...arguments);if(params.attributeName==='visibility'){const targetEl=this.$target[0];if(widgetValue==='conditional'){const collapseEl=this.$el.children('we-collapse')[0];this._toggleCollapseEl(collapseEl);}else{delete targetEl.dataset.visibility;for(const attribute of this.optionsAttributes){delete targetEl.dataset[attribute.saveAttribute];delete targetEl.dataset[`${attribute.saveAttribute}Rule`];}}
this.trigger_up('snippet_option_visibility_update',{show:true});}else if(!params.isVisibilityCondition){return;}
this._updateCSSSelectors();},async _computeWidgetState(methodName,params){if(methodName==='selectRecord'){return this.$target[0].dataset[params.saveAttribute]||'[]';}
if(methodName==='selectValue'){const selectedValue=this.$target[0].dataset[params.saveAttribute];return selectedValue?JSON.parse(selectedValue)[0].value:params.attributeDefaultValue;}
return this._super(...arguments);},_updateCSSSelectors(){const visibilityIDParts=[];const onlyAttributes=[];const hideAttributes=[];const target=this.$target[0];for(const attribute of this.optionsAttributes){if(target.dataset[attribute.saveAttribute]){let records=JSON.parse(target.dataset[attribute.saveAttribute]).map(record=>{return{id:record.id,value:record[attribute.callWith]};});if(attribute.saveAttribute==='visibilityValueLang'){records=records.map(lang=>{lang.value=lang.value.replace(/_/g,'-');return lang;});}
const hideFor=target.dataset[`${attribute.saveAttribute}Rule`]==='hide';if(hideFor){hideAttributes.push({name:attribute.attributeName,records:records});}else{onlyAttributes.push({name:attribute.attributeName,records:records});}
const type=attribute.attributeName.replace('data-','');const valueIDs=records.map(record=>record.id).sort();visibilityIDParts.push(`${type}_${hideFor ? 'h' : 'o'}_${valueIDs.join('_')}`);}}
const visibilityId=visibilityIDParts.join('_');let selectors='';for(const attribute of onlyAttributes){const selector=attribute.records.reduce((acc,record)=>{return acc+=`:not([${attribute.name}="${record.value}"])`;},'html')+` body:not(.editor_enable) [data-visibility-id="${visibilityId}"]`;selectors+=selector+', ';}
for(const attribute of hideAttributes){const selector=attribute.records.reduce((acc,record,i,a)=>{acc+=`html[${attribute.name}="${record.value}"] body:not(.editor_enable) [data-visibility-id="${visibilityId}"]`;return acc+(i!==a.length-1?',':'');},'');selectors+=selector+', ';}
selectors=selectors.slice(0,-2);if(selectors){this.$target[0].dataset.visibilitySelectors=selectors;}else{delete this.$target[0].dataset.visibilitySelectors;}
if(visibilityId){this.$target[0].dataset.visibilityId=visibilityId;}else{delete this.$target[0].dataset.visibilityId;}},});options.registry.WebsiteAnimate=options.Class.extend({async start(){await this._super(...arguments);this.limitedAnimations=['o_anim_flash','o_anim_pulse','o_anim_shake','o_anim_tada','o_anim_flip_in_x','o_anim_flip_in_y'];this.isAnimatedText=this.$target.hasClass('o_animated_text');this.$optionsSection=this.$overlay.data('$optionsSection');this.$scrollingElement=$().getScrollingElement(this.ownerDocument);},async onBuilt(){this.$target[0].classList.toggle('o_animate_preview',this.$target[0].classList.contains('o_animate'));},onFocus(){if(this.isAnimatedText){const $toolbar=this.options.wysiwyg.toolbar.$el;$toolbar.append(this.$el);this.$optionsSection.addClass('d-none');}},onBlur(){if(this.isAnimatedText){this.$optionsSection.append(this.$el);}},cleanForSave(){if(this.$target[0].closest('.o_animate')){this._toggleImagesLazyLoading(false);}},async selectClass(previewMode,widgetValue,params){await this._super(...arguments);if(params.forceAnimation&&params.name!=='o_anim_no_effect_opt'&&previewMode!=='reset'){this._forceAnimation();}
if(params.isAnimationTypeSelection){this.$target[0].classList.toggle('o_animate_preview',!!widgetValue);}},async selectDataAttribute(previewMode,widgetValue,params){await this._super(...arguments);if(params.forceAnimation){this._forceAnimation();}},animationMode(previewMode,widgetValue,params){const targetClassList=this.$target[0].classList;this.$scrollingElement[0].classList.remove('o_wanim_overflow_xy_hidden');targetClassList.remove('o_animating','o_animate_both_scroll','o_visible','o_animated','o_animate_out');this.$target[0].style.animationDelay='';this.$target[0].style.animationPlayState='';this.$target[0].style.animationName='';this.$target[0].style.visibility='';if(widgetValue==='onScroll'){this.$target[0].dataset.scrollZoneStart=0;this.$target[0].dataset.scrollZoneEnd=100;}else{delete this.$target[0].dataset.scrollZoneStart;delete this.$target[0].dataset.scrollZoneEnd;}
if(!params.activeValue&&widgetValue){targetClassList.add('o_anim_fade_in');this._toggleImagesLazyLoading(false);}
if(!widgetValue){const possibleEffects=this._requestUserValueWidgets('animation_effect_opt')[0].getMethodsParams('selectClass').possibleValues;const possibleDirections=this._requestUserValueWidgets('animation_direction_opt')[0].getMethodsParams('selectClass').possibleValues;const possibleEffectsAndDirections=possibleEffects.concat(possibleDirections);for(const targetClass of targetClassList.value.split(/\s+/g)){if(possibleEffectsAndDirections.indexOf(targetClass)>=0){targetClassList.remove(targetClass);}}
this.$target[0].style.setProperty('--wanim-intensity','');this.$target[0].style.animationDuration='';this._toggleImagesLazyLoading(true);}},animationIntensity(previewMode,widgetValue,params){this.$target[0].style.setProperty('--wanim-intensity',widgetValue);this._forceAnimation();},async _forceAnimation(){this.$target.css('animation-name','dummy');if(this.$target[0].classList.contains('o_animate_on_scroll')){void this.$target[0].offsetWidth;this.$target.css('animation-name','');this.ownerDocument.defaultView.dispatchEvent(new Event('resize'));}else{await new Promise(resolve=>setTimeout(resolve));this.$target.addClass('o_animating');this.trigger_up('cover_update',{overlayVisible:true,});this.$scrollingElement[0].classList.add('o_wanim_overflow_xy_hidden');this.$target.css('animation-name','');this.$target.one('webkitAnimationEnd oanimationend msAnimationEnd animationend',()=>{this.$scrollingElement[0].classList.remove('o_wanim_overflow_xy_hidden');this.$target.removeClass('o_animating');});}},_computeWidgetVisibility(widgetName,params){switch(widgetName){case'no_animation_opt':{return!this.isAnimatedText;}
case'animation_trigger_opt':{return!this.$target[0].closest('.dropdown');}
case'animation_on_scroll_opt':case'animation_direction_opt':{return!this.limitedAnimations.some(className=>this.$target[0].classList.contains(className));}
case'animation_intensity_opt':{const possibleDirections=this._requestUserValueWidgets('animation_direction_opt')[0].getMethodsParams('selectClass').possibleValues;if(this.$target[0].classList.contains('o_anim_fade_in')){for(const targetClass of this.$target[0].classList){if(possibleDirections.indexOf(targetClass)>=0){return true;}}
return false;}
return true;}}
return this._super(...arguments);},_computeVisibility(methodName,params){if(this.$target[0].matches('img')){return isImageSupportedForStyle(this.$target[0]);}
return this._super(...arguments);},_computeWidgetState(methodName,params){if(methodName==='animationIntensity'){return window.getComputedStyle(this.$target[0]).getPropertyValue('--wanim-intensity');}
return this._super(...arguments);},_toggleImagesLazyLoading(lazy){const imgEls=this.$target[0].matches('img')?[this.$target[0]]:this.$target[0].querySelectorAll('img');for(const imgEl of imgEls){if(lazy){imgEl.removeAttribute('loading');}else{imgEl.loading='eager';}}},});options.registry.MegaMenuLayout=options.registry.SelectTemplate.extend({init(){this._super(...arguments);this.selectTemplateWidgetName='mega_menu_template_opt';},notify(name,data){if(name==='reset_template'){const xmlid=this._getCurrentTemplateXMLID();this._getTemplate(xmlid).then(template=>{this.containerEl.insertAdjacentHTML('beforeend',template);data.onSuccess();});}else{this._super(...arguments);}},_computeWidgetState:function(methodName,params){if(methodName==='selectTemplate'){return this._getCurrentTemplateXMLID();}
return this._super(...arguments);},_getCurrentTemplateXMLID:function(){const templateDefiningClass=this.containerEl.querySelector('section').classList.value.split(' ').filter(cl=>cl.startsWith('s_mega_menu'))[0];return`website.${templateDefiningClass}`;},});options.registry.MegaMenuNoDelete=options.Class.extend({forceNoDeleteButton:true,async onRemove(){await new Promise(resolve=>{this.trigger_up('option_update',{optionName:'MegaMenuLayout',name:'reset_template',data:{onSuccess:()=>resolve(),}});});},});options.registry.sizing.include({start(){const defs=this._super(...arguments);const self=this;this.$handles.on('mousedown',function(ev){const $body=$(this.ownerDocument.body);if(!self.divEl){self.divEl=document.createElement('div');self.divEl.style.position='absolute';self.divEl.style.height='100%';self.divEl.style.width='100%';self.divEl.setAttribute('id','iframeEventOverlay');$body.append(self.divEl);}
const documentMouseUp=()=>{if(self.divEl){self.divEl.remove();self.divEl=undefined;}
$body.off('mouseup',documentMouseUp);};$body.on('mouseup',documentMouseUp);});return defs;}});options.registry.SwitchableViews=options.Class.extend({async willStart(){const _super=this._super.bind(this);this.switchableRelatedViews=await new Promise((resolve,reject)=>{this.trigger_up('get_switchable_related_views',{onSuccess:resolve,onFailure:reject,});});return _super(...arguments);},_renderCustomXML(uiFragment){for(const view of this.switchableRelatedViews){const weCheckboxEl=document.createElement('we-checkbox');weCheckboxEl.setAttribute('string',view.name);weCheckboxEl.setAttribute('data-customize-website-views',view.key);weCheckboxEl.setAttribute('data-no-preview','true');weCheckboxEl.setAttribute('data-reload','/');uiFragment.appendChild(weCheckboxEl);}},_computeVisibility(){return!!this.switchableRelatedViews.length;},_checkIfWidgetsUpdateNeedReload(){return true;}});options.registry.GridImage=options.Class.extend({changeGridImageMode(previewMode,widgetValue,params){const imageGridItemEl=this._getImageGridItem();if(imageGridItemEl){imageGridItemEl.classList.toggle('o_grid_item_image_contain',widgetValue==='contain');}},_getImageGridItem(){const parentEl=this.$target[0].parentNode;if(parentEl&&parentEl.classList.contains('o_grid_item_image')){return parentEl;}
return null;},_computeVisibility(){return this._super(...arguments)&&!!this._getImageGridItem()&&!('shape'in this.$target[0].dataset);},_computeWidgetState(methodName,params){if(methodName==='changeGridImageMode'){const imageGridItemEl=this._getImageGridItem();return imageGridItemEl&&imageGridItemEl.classList.contains('o_grid_item_image_contain')?'contain':'cover';}
return this._super(...arguments);},});options.registry.layout_column.include({_computeVisibility(){return!this.$target[0].closest('[data-snippet="s_images_wall"]');},});return{UrlPickerUserValueWidget:UrlPickerUserValueWidget,FontFamilyPickerUserValueWidget:FontFamilyPickerUserValueWidget,};});;

/* /website/static/src/snippets/s_facebook_page/options.js */
odoo.define('website.s_facebook_page_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.facebookPage=options.Class.extend({willStart:function(){var defs=[this._super.apply(this,arguments)];var defaults={href:'',height:215,width:350,tabs:'',small_header:true,hide_cover:true,};this.fbData=_.defaults(_.pick(this.$target[0].dataset,_.keys(defaults)),defaults);if(!this.fbData.href){var self=this;defs.push(this._rpc({model:'website',method:'search_read',args:[[],['social_facebook']],limit:1,}).then(function(res){if(res){self.fbData.href=res[0].social_facebook||'';}}));}
return Promise.all(defs).then(()=>this._markFbElement()).then(()=>this._refreshPublicWidgets());},toggleOption:function(previewMode,widgetValue,params){let optionName=params.optionName;if(optionName.startsWith('tab.')){optionName=optionName.replace('tab.','');if(widgetValue){this.fbData.tabs=this.fbData.tabs.split(',').filter(t=>t!=='').concat([optionName]).join(',');}else{this.fbData.tabs=this.fbData.tabs.split(',').filter(t=>t!==optionName).join(',');}}else{if(optionName==='show_cover'){this.fbData.hide_cover=!widgetValue;}else{this.fbData[optionName]=widgetValue;}}
return this._markFbElement();},pageUrl:function(previewMode,widgetValue,params){this.fbData.href=widgetValue;return this._markFbElement();},_markFbElement:function(){return this._checkURL().then(()=>{if(this.fbData.tabs){this.fbData.height=this.fbData.tabs==='events'?300:500;}else if(this.fbData.small_header){this.fbData.height=70;}else{this.fbData.height=150;}
_.each(this.fbData,(value,key)=>{this.$target[0].dataset[key]=value;});});},_computeWidgetState:function(methodName,params){const optionName=params.optionName;switch(methodName){case'toggleOption':{if(optionName.startsWith('tab.')){return this.fbData.tabs.split(',').includes(optionName.replace(/^tab./,''));}else{if(optionName==='show_cover'){return!this.fbData.hide_cover;}
return this.fbData[optionName];}}
case'pageUrl':{return this._checkURL().then(()=>this.fbData.href);}}
return this._super(...arguments);},_checkURL:function(){const defaultURL='https://www.facebook.com/Odoo';const match=this.fbData.href.match(/^(?:https?:\/\/)?(?:www\.)?(?:fb|facebook)\.com\/(?:([\w.]+)|[^/?#]+-([0-9]{15,16}))(?:$|[/?# ])/);if(match){return new Promise((resolve,reject)=>$.ajax({url:'https://graph.facebook.com/'+(match[2]||match[1])+'/picture',success:()=>resolve(),error:()=>{this.fbData.href=defaultURL;resolve();},}));}
this.fbData.href=defaultURL;return Promise.resolve();},});});;

/* /website/static/src/snippets/s_image_gallery/options.js */
odoo.define('website.s_image_gallery_options',function(require){'use strict';const{MediaDialogWrapper}=require('@web_editor/components/media_dialog/media_dialog');const{ComponentWrapper}=require('web.OwlCompatibility');var core=require('web.core');var options=require('web_editor.snippets.options');var _t=core._t;var qweb=core.qweb;options.registry.gallery=options.Class.extend({start:function(){var self=this;this.$target.on('image_changed.gallery','img',function(ev){var $img=$(ev.currentTarget);var index=self.$target.find('.carousel-item.active').index();self.$('.carousel:first li[data-bs-target]:eq('+index+')').css('background-image','url('+$img.attr('src')+')');});this.$target.on('click.gallery','.o_add_images',function(e){e.stopImmediatePropagation();self.addImages(false);});this.$target.on('dropped.gallery','img',function(ev){self.mode(null,self.getMode());if(!ev.target.height){$(ev.target).one('load',function(){setTimeout(function(){self.trigger_up('cover_update');});});}});const $container=this.$('> .container, > .container-fluid, > .o_container_small');if($container.find('> *:not(div)').length){self.mode(null,self.getMode());}
return this._super.apply(this,arguments);},onBuilt:function(){if(this.$target.find('.o_add_images').length){this.addImages(false);}
this._adaptNavigationIDs();},onClone:function(){this._adaptNavigationIDs();},cleanForSave:function(){if(this.$target.hasClass('slideshow')){this.$target.removeAttr('style');}},destroy(){this._super(...arguments);this.$target.off('.gallery');},addImages:function(previewMode){const $images=this.$('img');var $container=this.$('> .container, > .container-fluid, > .o_container_small');const lastImage=_.last(this._getImages());let index=lastImage?this._getIndex(lastImage):-1;const dialog=new ComponentWrapper(this,MediaDialogWrapper,{multiImages:true,onlyImages:true,save:images=>{let $newImageToSelect;for(const image of images){const $img=$('<img/>',{class:$images.length>0?$images[0].className:'img img-fluid d-block ',src:image.src,'data-index':++index,alt:image.alt||'','data-name':_t('Image'),style:$images.length>0?$images[0].style.cssText:'',}).appendTo($container);if(!$newImageToSelect){$newImageToSelect=$img;}}
if(images.length>0){this.mode('reset',this.getMode());this.trigger_up('cover_update');$newImageToSelect.trigger('image_changed');}},});dialog.mount(this.el);},columns:function(previewMode,widgetValue,params){const nbColumns=parseInt(widgetValue||'1');this.$target.attr('data-columns',nbColumns);this.mode(previewMode,this.getMode(),{});},getMode:function(){var mode='slideshow';if(this.$target.hasClass('o_masonry')){mode='masonry';}
if(this.$target.hasClass('o_grid')){mode='grid';}
if(this.$target.hasClass('o_nomode')){mode='nomode';}
return mode;},grid:function(){var imgs=this._getImages();var $row=$('<div/>',{class:'row s_nb_column_fixed'});var columns=this._getColumns();var colClass='col-lg-'+(12/columns);var $container=this._replaceContent($row);_.each(imgs,function(img,index){var $img=$(img);var $col=$('<div/>',{class:colClass});$col.append($img).appendTo($row);if((index+1)%columns===0){$row=$('<div/>',{class:'row s_nb_column_fixed'});$row.appendTo($container);}});this.$target.css('height','');},masonry:function(){var self=this;var imgs=this._getImages();var columns=this._getColumns();var colClass='col-lg-'+(12/columns);var cols=[];var $row=$('<div/>',{class:'row s_nb_column_fixed'});this._replaceContent($row);for(var c=0;c<columns;c++){var $col=$('<div/>',{class:'o_masonry_col o_snippet_not_selectable '+colClass});$row.append($col);cols.push($col[0]);}
while(imgs.length){var min=Infinity;var $lowest;_.each(cols,function(col){var $col=$(col);var height=$col.is(':empty')?0:$col.find('img').last().offset().top+$col.find('img').last().height()-self.$target.offset().top;if(height<min){min=height;$lowest=$col;}});$lowest.append(imgs.shift().cloneNode());}},mode:function(previewMode,widgetValue,params){widgetValue=widgetValue||'slideshow';this.$target.css('height','');this.$target.removeClass('o_nomode o_masonry o_grid o_slideshow').addClass('o_'+widgetValue);if(this.options.wysiwyg){this.options.wysiwyg.odooEditor.unbreakableStepUnactive();}
this[widgetValue]();this.trigger_up('cover_update');this._refreshPublicWidgets();},nomode:function(){var $row=$('<div/>',{class:'row s_nb_column_fixed'});var imgs=this._getImages();this._replaceContent($row);_.each(imgs,function(img){var wrapClass='col-lg-3';if(img.width>=img.height*2||img.width>600){wrapClass='col-lg-6';}
var $wrap=$('<div/>',{class:wrapClass}).append(img);$row.append($wrap);});},removeAllImages:function(previewMode){var $addImg=$('<div>',{class:'alert alert-info css_non_editable_mode_hidden text-center',});var $text=$('<span>',{class:'o_add_images',style:'cursor: pointer;',text:_t(" Add Images"),});var $icon=$('<i>',{class:' fa fa-plus-circle',});this._replaceContent($addImg.append($icon).append($text));},slideshow:function(){const imageEls=this._getImages();const images=_.map(imageEls,img=>({src:img.getAttribute('src'),alt:img.getAttribute('alt'),}));var currentInterval=this.$target.find('.carousel:first').attr('data-bs-interval');var params={images:images,index:0,title:"",interval:currentInterval||0,id:'slideshow_'+new Date().getTime(),attrClass:imageEls.length>0?imageEls[0].className:'',attrStyle:imageEls.length>0?imageEls[0].style.cssText:'',},$slideshow=$(qweb.render('website.gallery.slideshow',params));this._replaceContent($slideshow);_.each(this.$('img'),function(img,index){$(img).attr({contenteditable:true,'data-index':index});});this.$target.css('height',Math.round(window.innerHeight*0.7));this.$target.off('slide.bs.carousel').off('slid.bs.carousel');this.$('li.fa').off('click');},notify:function(name,data){this._super(...arguments);if(name==='image_removed'){data.$image.remove();this.mode('reset',this.getMode());}else if(name==='image_index_request'){var imgs=this._getImages();var position=_.indexOf(imgs,data.$image[0]);imgs.splice(position,1);switch(data.position){case'first':imgs.unshift(data.$image[0]);break;case'prev':imgs.splice(position-1,0,data.$image[0]);break;case'next':imgs.splice(position+1,0,data.$image[0]);break;case'last':imgs.push(data.$image[0]);break;}
position=imgs.indexOf(data.$image[0]);_.each(imgs,function(img,index){$(img).attr('data-index',index);});const currentMode=this.getMode();this.mode('reset',currentMode);if(currentMode==='slideshow'){const $carousel=this.$target.find('.carousel');$carousel.removeClass('slide');$carousel.carousel(position);this.$target.find('.carousel-indicators li').removeClass('active');this.$target.find('.carousel-indicators li[data-bs-slide-to="'+position+'"]').addClass('active');this.trigger_up('activate_snippet',{$snippet:this.$target.find('.carousel-item.active img'),ifInactiveOptions:true,});$carousel.addClass('slide');}else{this.trigger_up('activate_snippet',{$snippet:data.$image,ifInactiveOptions:true,});}}},_adaptNavigationIDs:function(){var uuid=new Date().getTime();this.$target.find('.carousel').attr('id','slideshow_'+uuid);_.each(this.$target.find('[data-bs-slide], [data-bs-slide-to]'),function(el){var $el=$(el);if($el.attr('data-bs-target')){$el.attr('data-bs-target','#slideshow_'+uuid);}else if($el.attr('href')){$el.attr('href','#slideshow_'+uuid);}});},_computeWidgetState:function(methodName,params){switch(methodName){case'mode':{let activeModeName='slideshow';for(const modeName of params.possibleValues){if(this.$target.hasClass(`o_${modeName}`)){activeModeName=modeName;break;}}
this.activeMode=activeModeName;return activeModeName;}
case'columns':{return`${this._getColumns()}`;}}
return this._super(...arguments);},async _computeWidgetVisibility(widgetName,params){if(widgetName==='slideshow_mode_opt'){return false;}
return this._super(...arguments);},_getImages:function(){var imgs=this.$('img').get();var self=this;imgs.sort(function(a,b){return self._getIndex(a)-self._getIndex(b);});return imgs;},_getIndex:function(img){return img.dataset.index||0;},_getColumns:function(){return parseInt(this.$target.attr('data-columns'))||3;},_replaceContent:function($content){var $container=this.$('> .container, > .container-fluid, > .o_container_small');$container.empty().append($content);return $container;},});options.registry.gallery_img=options.Class.extend({onRemove:function(){this.trigger_up('option_update',{optionName:'gallery',name:'image_removed',data:{$image:this.$target,},});},position:function(previewMode,widgetValue,params){this.trigger_up('option_update',{optionName:'gallery',name:'image_index_request',data:{$image:this.$target,position:widgetValue,},});},});});;

/* /website/static/src/snippets/s_countdown/options.js */
odoo.define('website.s_countdown_options',function(require){'use strict';const core=require('web.core');const options=require('web_editor.snippets.options');const qweb=core.qweb;options.registry.countdown=options.Class.extend({events:_.extend({},options.Class.prototype.events||{},{'click .toggle-edit-message':'_onToggleEndMessageClick',}),cleanForSave:async function(){this.$target.find('.s_countdown_canvas_wrapper').removeClass("s_countdown_none");this.$target.find('.s_countdown_end_message').removeClass("s_countdown_enable_preview");},endAction:function(previewMode,widgetValue,params){this.$target[0].dataset.endAction=widgetValue;if(widgetValue==='message'||widgetValue==='message_no_countdown'){if(!this.$target.find('.s_countdown_end_message').length){const message=this.endMessage||qweb.render('website.s_countdown.end_message');this.$target.append(message);}
this.$target.toggleClass('hide-countdown',widgetValue==='message_no_countdown');}else{const $message=this.$target.find('.s_countdown_end_message').detach();if(this.showEndMessage){this._onToggleEndMessageClick();}
if($message.length){this.endMessage=$message[0].outerHTML;}}},layout:function(previewMode,widgetValue,params){switch(widgetValue){case'circle':this.$target[0].dataset.progressBarStyle='disappear';this.$target[0].dataset.progressBarWeight='thin';this.$target[0].dataset.layoutBackground='none';break;case'boxes':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='plain';break;case'clean':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='none';break;case'text':this.$target[0].dataset.progressBarStyle='none';this.$target[0].dataset.layoutBackground='none';break;}
this.$target[0].dataset.layout=widgetValue;},updateUIVisibility:async function(){await this._super(...arguments);const dataset=this.$target[0].dataset;this.$el.find('.toggle-edit-message').toggleClass('d-none',dataset.endAction==='nothing'||dataset.endAction==='redirect');this.updateUIEndMessage();},updateUIEndMessage:function(){this.$target.find('.s_countdown_canvas_wrapper').toggleClass("s_countdown_none",this.showEndMessage===true&&this.$target.hasClass("hide-countdown"));this.$target.find('.s_countdown_end_message').toggleClass("s_countdown_enable_preview",this.showEndMessage===true);},_computeWidgetState:function(methodName,params){switch(methodName){case'endAction':case'layout':return this.$target[0].dataset[methodName];case'selectDataAttribute':{if(params.colorNames){params.attributeDefaultValue='rgba(0, 0, 0, 255)';}
break;}}
return this._super(...arguments);},_onToggleEndMessageClick:function(){this.showEndMessage=!this.showEndMessage;this.$el.find(".toggle-edit-message").toggleClass('text-primary',this.showEndMessage);this.updateUIEndMessage();this.trigger_up('cover_update');},});});;

/* /website/static/src/snippets/s_masonry_block/options.js */
odoo.define('@website/snippets/s_masonry_block/options',async function(require){'use strict';let __exports={};const options=require('web_editor.snippets.options');options.registry.MasonryLayout=options.registry.SelectTemplate.extend({init(){this._super(...arguments);this.containerSelector='> .container, > .container-fluid, > .o_container_small';this.selectTemplateWidgetName='masonry_template_opt';},});return __exports;});;

/* /website/static/src/snippets/s_popup/options.js */
odoo.define('website.s_popup_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.SnippetPopup=options.Class.extend({start:function(){this.$bsTarget.on('click.SnippetPopup','.js_close_popup:not(a, .btn)',ev=>{ev.stopPropagation();this.onTargetHide();this.trigger_up('snippet_option_visibility_update',{show:false});});this.$bsTarget.on('shown.bs.modal.SnippetPopup',()=>{this.trigger_up('snippet_option_visibility_update',{show:true});this.$target[0].querySelectorAll('.media_iframe_video').forEach(media=>{const iframe=media.querySelector('iframe');iframe.src=media.dataset.oeExpression||media.dataset.src;});});this.$bsTarget.on('hide.bs.modal.SnippetPopup',()=>{this.trigger_up('snippet_option_visibility_update',{show:false});this._removeIframeSrc();});this._removeIframeSrc();return this._super(...arguments);},destroy:function(){this._super(...arguments);this._removeIframeSrc();this.$bsTarget.off('.SnippetPopup');},onBuilt:function(){this._assignUniqueID();const popup=this.$target.closest('.s_popup_middle');if(popup&&popup.attr('data-focus')){popup.attr('data-bs-focus',popup.attr('data-focus'));popup[0].removeAttribute('data-focus');}},onClone:function(){this._assignUniqueID();},onTargetShow:async function(){this.$bsTarget.modal('show');$(this.$target[0].ownerDocument.body).children('.modal-backdrop:last').addClass('d-none');},onTargetHide:async function(){return new Promise(resolve=>{const timeoutID=setTimeout(()=>{this.$bsTarget.off('hidden.bs.modal.popup_on_target_hide');resolve();},500);this.$bsTarget.one('hidden.bs.modal.popup_on_target_hide',()=>{clearTimeout(timeoutID);resolve();});this.$target[0].closest('.s_popup').classList.add('d-none');this.$bsTarget.modal('hide');});},moveBlock:function(previewMode,widgetValue,params){const containerEl=this.$target[0].ownerDocument.querySelector(widgetValue==='moveToFooter'?'footer':'main');const whereEl=$(containerEl).find('.oe_structure:o_editable')[0];const popupEl=this.$target[0].closest('.s_popup');whereEl.prepend(popupEl);},setBackdrop(previewMode,widgetValue,params){const color=widgetValue?'var(--black-50)':'';this.$target[0].style.setProperty('background-color',color,'important');},_assignUniqueID:function(){this.$target.closest('.s_popup').attr('id','sPopup'+Date.now());},_computeWidgetState:function(methodName,params){switch(methodName){case'moveBlock':return this.$target.closest('footer').length?'moveToFooter':'moveToBody';}
return this._super(...arguments);},_removeIframeSrc(){this.$target.find('.media_iframe_video iframe').each((i,iframe)=>{iframe.src='';});},});});;

/* /website/static/src/snippets/s_product_catalog/options.js */
odoo.define('website.s_product_catalog_options',function(require){'use strict';const core=require('web.core');const options=require('web_editor.snippets.options');const _t=core._t;options.registry.ProductCatalog=options.Class.extend({toggleDescription:function(previewMode,widgetValue,params){const $dishes=this.$('.s_product_catalog_dish');const $name=$dishes.find('.s_product_catalog_dish_name');$name.toggleClass('s_product_catalog_dish_dot_leaders',!widgetValue);if(widgetValue){_.each($dishes,el=>{const $description=$(el).find('.s_product_catalog_dish_description');if($description.length){$description.removeClass('d-none');}else{const descriptionEl=document.createElement('p');descriptionEl.classList.add('s_product_catalog_dish_description','border-top','text-muted','pt-1','o_default_snippet_text');const iEl=document.createElement('i');iEl.textContent=_t("Add a description here");descriptionEl.appendChild(iEl);el.appendChild(descriptionEl);}});}else{_.each($dishes,el=>{const $description=$(el).find('.s_product_catalog_dish_description');if($description.hasClass('o_default_snippet_text')||$description.find('.o_default_snippet_text').length){$description.remove();}else{$description.addClass('d-none');}});}},_computeWidgetState:function(methodName,params){if(methodName==='toggleDescription'){const $description=this.$('.s_product_catalog_dish_description');return $description.length&&!$description.hasClass('d-none');}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_chart/options.js */
odoo.define('website.s_chart_options',function(require){'use strict';var core=require('web.core');const{ColorpickerWidget}=require('web.Colorpicker');var options=require('web_editor.snippets.options');const weUtils=require('web_editor.utils');var _t=core._t;options.registry.InnerChart=options.Class.extend({custom_events:_.extend({},options.Class.prototype.custom_events,{'get_custom_colors':'_onGetCustomColors',}),events:_.extend({},options.Class.prototype.events,{'click we-button.add_column':'_onAddColumnClick','click we-button.add_row':'_onAddRowClick','click we-button.o_we_matrix_remove_col':'_onRemoveColumnClick','click we-button.o_we_matrix_remove_row':'_onRemoveRowClick','blur we-matrix input':'_onMatrixInputFocusOut','focus we-matrix input':'_onMatrixInputFocus',}),init:function(){this._super.apply(this,arguments);this.themeArray=['o-color-1','o-color-2','o-color-3','o-color-4','o-color-5'];this.style=window.getComputedStyle(this.$target[0].ownerDocument.documentElement);},start:function(){this.backSelectEl=this.el.querySelector('[data-name="chart_bg_color_opt"]');this.borderSelectEl=this.el.querySelector('[data-name="chart_border_color_opt"]');this.tableEl=this.el.querySelector('we-matrix table');const data=JSON.parse(this.$target[0].dataset.data);data.labels.forEach(el=>{this._addRow(el);});data.datasets.forEach((el,i)=>{if(this._isPieChart()){const headerBackgroundColor=this.themeArray[i]||this._randomColor();const headerBorderColor=this.themeArray[i]||this._randomColor();this._addColumn(el.label,el.data,headerBackgroundColor,headerBorderColor,el.backgroundColor,el.borderColor);}else{this._addColumn(el.label,el.data,el.backgroundColor,el.borderColor);}});this._displayRemoveColButton();this._displayRemoveRowButton();this._setDefaultSelectedInput();return this._super(...arguments);},updateUI:async function(){if(!this.lastEditableSelectedInput.closest('table')||this.colorPaletteSelectedInput&&!this.colorPaletteSelectedInput.closest('table')){this._setDefaultSelectedInput();}
await this._super(...arguments);this.backSelectEl.querySelector('we-title').textContent=this._isPieChart()?_t("Data Color"):_t("Dataset Color");this.borderSelectEl.querySelector('we-title').textContent=this._isPieChart()?_t("Data Border"):_t("Dataset Border");this.tableEl.querySelectorAll('input').forEach(el=>el.style.border='');const selector=this._isPieChart()?'td input':'tr:first-child input';this.tableEl.querySelectorAll(selector).forEach(el=>{const color=el.dataset.backgroundColor||el.dataset.borderColor;if(color){el.style.border='2px solid';el.style.borderColor=ColorpickerWidget.isCSSColor(color)?color:weUtils.getCSSVariableValue(color,this.style);}});},colorChange:async function(previewMode,widgetValue,params){if(widgetValue){this.colorPaletteSelectedInput.dataset[params.attributeName]=widgetValue;}else{delete this.colorPaletteSelectedInput.dataset[params.attributeName];}
await this._reloadGraph();await new Promise(resolve=>setTimeout(()=>{this.lastEditableSelectedInput.focus();resolve();}));},selectDataAttribute:async function(previewMode,widgetValue,params){await this._super(...arguments);if(params.attributeName==='type'){this._setDefaultSelectedInput();await this._reloadGraph();}
if(params.attributeName==='minValue'||params.attributeName==='maxValue'){this._computeTicksMinMaxValue();}},_computeWidgetState:function(methodName,params){if(methodName==='colorChange'){return this.colorPaletteSelectedInput&&this.colorPaletteSelectedInput.dataset[params.attributeName]||'';}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){switch(widgetName){case'stacked_chart_opt':{return this._getColumnCount()>1;}
case'chart_bg_color_opt':case'chart_border_color_opt':{return!!this.colorPaletteSelectedInput;}}
return this._super(...arguments);},_computeTicksMinMaxValue(){const dataset=this.$target[0].dataset;let minValue=parseInt(dataset.minValue);let maxValue=parseInt(dataset.maxValue);if(!isNaN(maxValue)){if(maxValue<minValue){maxValue=minValue;minValue=parseInt(dataset.maxValue);}else if(maxValue===minValue){minValue<0?(maxValue=0,minValue=2*minValue):(minValue=0,maxValue=2*maxValue);}}else{const datasets=JSON.parse(dataset.data).datasets||[];const dataValue=_.flatten(datasets.map(el=>el.data.map(data=>{return!isNaN(parseInt(data))?parseInt(data):0;})));if(minValue>=Math.max(...dataValue)){maxValue=minValue;minValue=0;}}
this.$target.attr({'data-ticks-min':minValue,'data-ticks-max':maxValue,});},_reloadGraph:async function(){const jsonValue=this._matrixToChartData();if(this.$target[0].dataset.data!==jsonValue){this.$target[0].dataset.data=jsonValue;await this._refreshPublicWidgets();}},_matrixToChartData:function(){const data={labels:[],datasets:[],};this.tableEl.querySelectorAll('tr:first-child input').forEach(el=>{data.datasets.push({label:el.value||'',data:[],backgroundColor:this._isPieChart()?[]:el.dataset.backgroundColor||'',borderColor:this._isPieChart()?[]:el.dataset.borderColor||'',});});this.tableEl.querySelectorAll('tr:not(:first-child):not(:last-child)').forEach((el)=>{const title=el.querySelector('th input').value||'';data.labels.push(title);el.querySelectorAll('td input').forEach((el,i)=>{data.datasets[i].data.push(el.value||0);if(this._isPieChart()){data.datasets[i].backgroundColor.push(el.dataset.backgroundColor||'');data.datasets[i].borderColor.push(el.dataset.borderColor||'');}});});return JSON.stringify(data);},_makeDeleteButton:function(...classes){const rmbuttonEl=options.buildElement('we-button',null,{classes:['o_we_text_danger','o_we_link','fa','fa-fw','fa-minus',...classes],});rmbuttonEl.title=classes.includes('o_we_matrix_remove_col')?_t("Remove Serie"):_t("Remove Row");const newEl=document.createElement('td');newEl.appendChild(rmbuttonEl);return newEl;},_addColumn:function(title,values,heardeBackgroundColor,headerBorderColor,cellBackgroundColors=[],cellBorderColors=[]){const firstRow=this.tableEl.querySelector('tr:first-child');const headerInput=this._makeCell('th',title,heardeBackgroundColor,headerBorderColor);firstRow.insertBefore(headerInput,firstRow.lastElementChild);this.tableEl.querySelectorAll('tr:not(:first-child):not(:last-child)').forEach((el,i)=>{const newCell=this._makeCell('td',values?values[i]:null,cellBackgroundColors[i]||this._randomColor(),cellBorderColors[i-1]);el.insertBefore(newCell,el.lastElementChild);});const lastRow=this.tableEl.querySelector('tr:last-child');const removeButton=this._makeDeleteButton('o_we_matrix_remove_col');lastRow.appendChild(removeButton);},_addRow:function(tilte){const trEl=document.createElement('tr');trEl.appendChild(this._makeCell('th',tilte));this.tableEl.querySelectorAll('tr:first-child input').forEach(()=>{trEl.appendChild(this._makeCell('td',null,this._randomColor()));});trEl.appendChild(this._makeDeleteButton('o_we_matrix_remove_row'));const tbody=this.tableEl.querySelector('tbody');tbody.insertBefore(trEl,tbody.lastElementChild);},_makeCell:function(tag,value,backgroundColor,borderColor){const newEl=document.createElement(tag);const contentEl=document.createElement('input');contentEl.type='text';contentEl.value=value||'';if(backgroundColor){contentEl.dataset.backgroundColor=backgroundColor;}
if(borderColor){contentEl.dataset.borderColor=borderColor;}
newEl.appendChild(contentEl);return newEl;},_displayRemoveColButton:function(colIndex){if(this._getColumnCount()>1){this._displayRemoveButton(colIndex,'o_we_matrix_remove_col');}},_displayRemoveRowButton:function(rowIndex){const rowCount=this.tableEl.rows.length-2;if(rowCount>1){this._displayRemoveButton(rowIndex,'o_we_matrix_remove_row');}},_displayRemoveButton:function(tdIndex,btnClass){const removeBtn=this.tableEl.querySelectorAll(`td we-button.${btnClass}`);removeBtn.forEach(el=>el.style.display='');const index=tdIndex<removeBtn.length?tdIndex:removeBtn.length-1;removeBtn[index].style.display='inline-block';},_isPieChart:function(){return['pie','doughnut'].includes(this.$target[0].dataset.type);},_getColumnCount:function(){return this.tableEl.rows[0].cells.length-2;},_setDefaultSelectedInput:function(){this.lastEditableSelectedInput=this.tableEl.querySelector('td input');if(this._isPieChart()){this.colorPaletteSelectedInput=this.lastEditableSelectedInput;}else{this.colorPaletteSelectedInput=this.tableEl.querySelector('th input');}},_randomColor:function(){return'#'+('00000'+(Math.random()*(1<<24)|0).toString(16)).slice(-6).toUpperCase();},_onGetCustomColors:function(ev){const data=JSON.parse(this.$target[0].dataset.data||'');let customColors=[];data.datasets.forEach(el=>{if(this._isPieChart()){customColors=customColors.concat(el.backgroundColor).concat(el.borderColor);}else{customColors.push(el.backgroundColor);customColors.push(el.borderColor);}});customColors=customColors.filter((el,i,array)=>{return!weUtils.getCSSVariableValue(el,this.style)&&array.indexOf(el)===i&&el!=='';});ev.data.onSuccess(customColors);},_onAddColumnClick:function(){const usedColor=Array.from(this.tableEl.querySelectorAll('tr:first-child input')).map(el=>el.dataset.backgroundColor);const color=this.themeArray.filter(el=>!usedColor.includes(el))[0]||this._randomColor();this._addColumn(null,null,color,color);this._reloadGraph().then(()=>{this._displayRemoveColButton();this.updateUI();});},_onAddRowClick:function(){this._addRow();this._reloadGraph().then(()=>{this._displayRemoveRowButton();this.updateUI();});},_onRemoveColumnClick:function(ev){const cell=ev.currentTarget.parentElement;const cellIndex=cell.cellIndex;this.tableEl.querySelectorAll('tr').forEach((el)=>{el.children[cellIndex].remove();});this._displayRemoveColButton(cellIndex-1);this._reloadGraph().then(()=>{this.updateUI();});},_onRemoveRowClick:function(ev){const row=ev.currentTarget.parentElement.parentElement;const rowIndex=row.rowIndex;row.remove();this._displayRemoveRowButton(rowIndex-1);this._reloadGraph().then(()=>{this.updateUI();});},_onMatrixInputFocusOut:function(ev){this._reloadGraph();},_onMatrixInputFocus:function(ev){this.lastEditableSelectedInput=ev.target;const col=ev.target.parentElement.cellIndex;const row=ev.target.parentElement.parentElement.rowIndex;if(this._isPieChart()){this.colorPaletteSelectedInput=ev.target.parentNode.tagName==='TD'?ev.target:null;}else{this.colorPaletteSelectedInput=this.tableEl.querySelector(`tr:first-child th:nth-of-type(${col + 1}) input`);}
if(col>0){this._displayRemoveColButton(col-1);}
if(row>0){this._displayRemoveRowButton(row-1);}
this.updateUI();},});});;

/* /website/static/src/snippets/s_rating/options.js */
odoo.define('website.s_rating_options',function(require){'use strict';const{ComponentWrapper}=require('web.OwlCompatibility');const{MediaDialogWrapper}=require('@web_editor/components/media_dialog/media_dialog');const options=require('web_editor.snippets.options');options.registry.Rating=options.Class.extend({start:function(){this.iconType=this.$target[0].dataset.icon;this.faClassActiveCustomIcons=this.$target[0].dataset.activeCustomIcon||'';this.faClassInactiveCustomIcons=this.$target[0].dataset.inactiveCustomIcon||'';return this._super.apply(this,arguments);},setIcons:function(previewMode,widgetValue,params){this.iconType=widgetValue;this._renderIcons();this.$target[0].dataset.icon=widgetValue;delete this.$target[0].dataset.activeCustomIcon;delete this.$target[0].dataset.inactiveCustomIcon;},customIcon:async function(previewMode,widgetValue,params){const media=document.createElement('i');media.className=params.customActiveIcon==='true'?this.faClassActiveCustomIcons:this.faClassInactiveCustomIcons;const dialog=new ComponentWrapper(this,MediaDialogWrapper,{noImages:true,noDocuments:true,noVideos:true,media,save:icon=>{const customClass=icon.className;const $activeIcons=this.$target.find('.s_rating_active_icons > i');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons > i');const $icons=params.customActiveIcon==='true'?$activeIcons:$inactiveIcons;$icons.removeClass().addClass(customClass);this.faClassActiveCustomIcons=$activeIcons.length>0?$activeIcons.attr('class'):customClass;this.faClassInactiveCustomIcons=$inactiveIcons.length>0?$inactiveIcons.attr('class'):customClass;this.$target[0].dataset.activeCustomIcon=this.faClassActiveCustomIcons;this.$target[0].dataset.inactiveCustomIcon=this.faClassInactiveCustomIcons;this.$target[0].dataset.icon='custom';this.iconType='custom';}});dialog.mount(this.el);},activeIconsNumber:function(previewMode,widgetValue,params){this.nbActiveIcons=parseInt(widgetValue);this._createIcons();},totalIconsNumber:function(previewMode,widgetValue,params){this.nbTotalIcons=Math.max(parseInt(widgetValue),1);this._createIcons();},_computeWidgetState:function(methodName,params){switch(methodName){case'setIcons':{return this.$target[0].dataset.icon;}
case'activeIconsNumber':{this.nbActiveIcons=this.$target.find('.s_rating_active_icons > i').length;return this.nbActiveIcons;}
case'totalIconsNumber':{this.nbTotalIcons=this.$target.find('.s_rating_icons i').length;return this.nbTotalIcons;}}
return this._super(...arguments);},_createIcons:function(){const $activeIcons=this.$target.find('.s_rating_active_icons');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons');this.$target.find('.s_rating_icons i').remove();for(let i=0;i<this.nbTotalIcons;i++){if(i<this.nbActiveIcons){$activeIcons.append('<i/> ');}else{$inactiveIcons.append('<i/> ');}}
this._renderIcons();},_renderIcons:function(){const icons={'fa-star':'fa-star-o','fa-thumbs-up':'fa-thumbs-o-up','fa-circle':'fa-circle-o','fa-square':'fa-square-o','fa-heart':'fa-heart-o'};const faClassActiveIcons=(this.iconType==="custom")?this.faClassActiveCustomIcons:'fa '+this.iconType;const faClassInactiveIcons=(this.iconType==="custom")?this.faClassInactiveCustomIcons:'fa '+icons[this.iconType];const $activeIcons=this.$target.find('.s_rating_active_icons > i');const $inactiveIcons=this.$target.find('.s_rating_inactive_icons > i');$activeIcons.removeClass().addClass(faClassActiveIcons);$inactiveIcons.removeClass().addClass(faClassInactiveIcons);},});});;

/* /website/static/src/snippets/s_tabs/options.js */
odoo.define('website.s_tabs_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.NavTabs=options.registry.MultipleItems.extend({isTopOption:true,start:function(){this._findLinksAndPanes();return this._super.apply(this,arguments);},onBuilt:function(){this._generateUniqueIDs();},onClone:function(){this._generateUniqueIDs();},_computeWidgetVisibility:async function(widgetName,params){if(widgetName==='remove_tab_opt'){return(this.$tabPanes.length>2);}
return this._super(...arguments);},_findLinksAndPanes:function(){this.$navLinks=this.$target.find('.nav:first .nav-link');this.$tabPanes=this.$target.find('.tab-content:first .tab-pane');},_generateUniqueIDs:function(){for(var i=0;i<this.$navLinks.length;i++){var id=_.now()+'_'+_.uniqueId();var idLink='nav_tabs_link_'+id;var idContent='nav_tabs_content_'+id;this.$navLinks.eq(i).attr({'id':idLink,'href':'#'+idContent,'aria-controls':idContent,});this.$tabPanes.eq(i).attr({'id':idContent,'aria-labelledby':idLink,});}},_addItemCallback($target){$target.removeClass('active show');const $targetNavItem=this.$(`.nav-item a[href="#${$target.attr('id')}"]`).removeClass('active show').parent();const $navLink=$targetNavItem.clone().insertAfter($targetNavItem).find('.nav-link');this._findLinksAndPanes();this._generateUniqueIDs();$navLink.tab('show');},_removeItemCallback($target){const $targetNavLink=this.$(`.nav-item a[href="#${$target.attr('id')}"]`);const $navLinkToShow=this.$navLinks.eq((this.$navLinks.index($targetNavLink)+1)%this.$navLinks.length);$targetNavLink.parent().remove();this._findLinksAndPanes();$navLinkToShow.tab('show');},});options.registry.NavTabsStyle=options.Class.extend({setStyle:function(previewMode,widgetValue,params){const $nav=this.$target.find('.s_tabs_nav:first .nav');const isPills=widgetValue==='pills';$nav.toggleClass('nav-tabs card-header-tabs',!isPills);$nav.toggleClass('nav-pills',isPills);this.$target.find('.s_tabs_nav:first').toggleClass('card-header',!isPills).toggleClass('mb-3',isPills);this.$target.toggleClass('card',!isPills);this.$target.find('.s_tabs_content:first').toggleClass('card-body',!isPills);},setDirection:function(previewMode,widgetValue,params){const isVertical=widgetValue==='vertical';this.$target.toggleClass('row s_col_no_resize s_col_no_bgcolor',isVertical);this.$target.find('.s_tabs_nav:first .nav').toggleClass('flex-column',isVertical);this.$target.find('.s_tabs_nav:first > .nav-link').toggleClass('py-2',isVertical);this.$target.find('.s_tabs_nav:first').toggleClass('col-md-3',isVertical);this.$target.find('.s_tabs_content:first').toggleClass('col-md-9',isVertical);},_computeWidgetState:function(methodName,params){switch(methodName){case'setStyle':return this.$target.find('.s_tabs_nav:first .nav').hasClass('nav-pills')?'pills':'tabs';case'setDirection':return this.$target.find('.s_tabs_nav:first .nav').hasClass('flex-column')?'vertical':'horizontal';}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_progress_bar/options.js */
odoo.define('website.s_progress_bar_options',function(require){'use strict';const core=require('web.core');const utils=require('web.utils');const options=require('web_editor.snippets.options');const _t=core._t;options.registry.progress=options.Class.extend({display:function(previewMode,widgetValue,params){if(this.$target.hasClass('progress')){this.$target.removeClass('progress');this.$target.find('.progress-bar').wrap($('<div/>',{class:'progress',}));this.$target.find('.progress-bar span').addClass('s_progress_bar_text');}
let $text=this.$target.find('.s_progress_bar_text');if(!$text.length){$text=$('<span/>').addClass('s_progress_bar_text').html(_t('80% Development'));}
if(widgetValue==='inline'){$text.appendTo(this.$target.find('.progress-bar'));}else{$text.insertBefore(this.$target.find('.progress'));}},progressBarValue:function(previewMode,widgetValue,params){let value=parseInt(widgetValue);value=utils.confine(value,0,100);const $progressBar=this.$target.find('.progress-bar');const $progressBarText=this.$target.find('.s_progress_bar_text');$progressBarText.text($progressBarText.text().replace(/[0-9]+%/,value+'%'));$progressBar.attr("aria-valuenow",value);$progressBar.css("width",value+"%");},_computeWidgetState:function(methodName,params){switch(methodName){case'display':{const isInline=this.$target.find('.s_progress_bar_text').parent('.progress-bar').length;return isInline?'inline':'below';}
case'progressBarValue':{return this.$target.find('.progress-bar').attr('aria-valuenow')+'%';}}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_blockquote/options.js */
odoo.define('website.s_blockquote_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Blockquote=options.Class.extend({display:function(previewMode,widgetValue,params){this.$target.find('.s_blockquote_avatar').toggleClass('d-none',widgetValue!=='classic');const $blockquote=this.$target.find('.s_blockquote_content');if(widgetValue==='cover'){$blockquote.css({"background-image":"url('/web/image/website.s_blockquote_cover_default_image')"});$blockquote.addClass('oe_img_bg o_bg_img_center');if(!$blockquote.find('.o_we_bg_filter').length){const bgFilterEl=document.createElement('div');bgFilterEl.classList.add('o_we_bg_filter','bg-white-50');$blockquote.prepend(bgFilterEl);}}else{$blockquote.css({"background-image":""});$blockquote.css({"background-position":""});$blockquote.removeClass('oe_img_bg o_bg_img_center');$blockquote.find('.o_we_bg_filter').remove();$blockquote.find('.s_blockquote_filter').contents().unwrap();}
this.$target.find('.s_blockquote_icon').toggleClass('d-none',widgetValue==='minimalist');this.$target.find('footer').toggleClass('d-none',widgetValue==='minimalist');},});});;

/* /website/static/src/snippets/s_showcase/options.js */
odoo.define('website.s_showcase_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Showcase=options.Class.extend({onMove:function(){const $showcaseCol=this.$target.parent().closest('.row > div');const isLeftCol=$showcaseCol.index()<=0;const $title=this.$target.children('.s_showcase_title');$title.toggleClass('flex-lg-row-reverse',isLeftCol);$showcaseCol.find('.s_showcase_icon.ms-3').removeClass('ms-3').addClass('ms-lg-3');$title.find('.s_showcase_icon').toggleClass('me-lg-0 ms-lg-3',isLeftCol);},});});;

/* /website/static/src/snippets/s_table_of_content/options.js */
odoo.define('website.s_table_of_content_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.TableOfContent=options.Class.extend({start:function(){this.targetedElements='h1, h2';this.oldHeadingsEls=[];const $headings=this.$target.find(this.targetedElements);if($headings.length>0){this._generateNav();}
const targetNode=this.$target.find('.s_table_of_content_main')[0];const config={attributes:false,childList:true,subtree:true,characterData:true};this.observer=new MutationObserver(()=>this._generateNav());this.observer.observe(targetNode,config);this.$target.on('content_changed',()=>this._generateNav());return this._super(...arguments);},destroy:function(){this.observer.disconnect();this._super(...arguments);},onRemove(){this._disposeScrollSpy();const exception=(tocEl)=>tocEl===this.$target[0];this._activateScrollSpy(exception);},onClone:function(){this._generateNav();},_activateScrollSpy(exception){for(const tocEl of this.ownerDocument.querySelectorAll('#wrapwrap .s_table_of_content')){if(exception(tocEl)){continue;}
this.trigger_up('widgets_start_request',{$target:$(tocEl),editableMode:true,});}},_disposeScrollSpy(){const scrollingEl=$().getScrollingElement(this.ownerDocument)[0];const scrollSpyInstance=this.$target[0].ownerDocument.defaultView.ScrollSpy.getInstance(scrollingEl);if(scrollSpyInstance){scrollSpyInstance.dispose();}},_generateNav:function(ev){this.options.wysiwyg&&this.options.wysiwyg.odooEditor.unbreakableStepUnactive();const headingsEls=this.$target.find(this.targetedElements).toArray().filter(el=>!el.closest('.o_snippet_desktop_invisible'));const areHeadingsEqual=this.oldHeadingsEls.length===headingsEls.length&&this.oldHeadingsEls.every((el,i)=>el.isEqualNode(headingsEls[i]));if(areHeadingsEqual){return;}
this._disposeScrollSpy();const $nav=this.$target.find('.s_table_of_content_navbar');$nav.empty();_.each(headingsEls,el=>{const $el=$(el);const id='table_of_content_heading_'+_.now()+'_'+_.uniqueId();$('<a>').attr('href',"#"+id).addClass('table_of_content_link list-group-item list-group-item-action py-2 border-0 rounded-0').text($el.text()).appendTo($nav);$el.attr('id',id);$el[0].dataset.anchor='true';});const exception=(tocEl)=>!tocEl.querySelector('.s_table_of_content_navbar a');this._activateScrollSpy(exception);this.oldHeadingsEls=[...headingsEls.map(el=>el.cloneNode(true))];},});options.registry.TableOfContentNavbar=options.Class.extend({navbarPosition:function(previewMode,widgetValue,params){const $navbar=this.$target;const $mainContent=this.$target.parent().find('.s_table_of_content_main');if(widgetValue==='top'||widgetValue==='left'){$navbar.prev().before($navbar);}
if(widgetValue==='left'||widgetValue==='right'){$navbar.removeClass('s_table_of_content_horizontal_navbar col-lg-12').addClass('s_table_of_content_vertical_navbar col-lg-3');$mainContent.removeClass('col-lg-12').addClass('col-lg-9');$navbar.find('.s_table_of_content_navbar').removeClass('list-group-horizontal-md');}
if(widgetValue==='right'){$navbar.next().after($navbar);}
if(widgetValue==='top'){$navbar.removeClass('s_table_of_content_vertical_navbar col-lg-3').addClass('s_table_of_content_horizontal_navbar col-lg-12');$navbar.find('.s_table_of_content_navbar').addClass('list-group-horizontal-md');$mainContent.removeClass('col-lg-9').addClass('col-lg-12');}},_computeWidgetState:function(methodName,params){switch(methodName){case'navbarPosition':{const $navbar=this.$target;if($navbar.hasClass('s_table_of_content_horizontal_navbar')){return'top';}else{const $mainContent=$navbar.parent().find('.s_table_of_content_main');return $navbar.prev().is($mainContent)===true?'right':'left';}}}
return this._super(...arguments);},});options.registry.TableOfContentMainColumns=options.Class.extend({forceNoDeleteButton:true,start:function(){const leftPanelEl=this.$overlay.data('$optionsSection')[0];leftPanelEl.querySelector('.oe_snippet_clone').classList.add('d-none');return this._super.apply(this,arguments);},});});;

/* /website/static/src/snippets/s_timeline/options.js */
odoo.define('website.s_timeline_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.Timeline=options.Class.extend({displayOverlayOptions:true,start:function(){var $buttons=this.$el.find('we-button.o_we_overlay_opt');var $overlayArea=this.$overlay.find('.o_overlay_options_wrap');$overlayArea.append($buttons);return this._super(...arguments);},timelineCard:function(previewMode,widgetValue,params){const $timelineRow=this.$target.closest('.s_timeline_row');$timelineRow.toggleClass('flex-row-reverse flex-row');},});});;

/* /website/static/src/snippets/s_media_list/options.js */
odoo.define('website.s_media_list_options',function(require){'use strict';const options=require('web_editor.snippets.options');options.registry.MediaItemLayout=options.Class.extend({layout:function(previewMode,widgetValue,params){const $image=this.$target.find('.s_media_list_img_wrapper');const $content=this.$target.find('.s_media_list_body');for(const possibleValue of params.possibleValues){$image.removeClass(`col-lg-${possibleValue}`);$content.removeClass(`col-lg-${12 - possibleValue}`);}
$image.addClass(`col-lg-${widgetValue}`);$content.addClass(`col-lg-${12 - widgetValue}`);},_computeWidgetState(methodName,params){switch(methodName){case'layout':{const $image=this.$target.find('.s_media_list_img_wrapper');for(const possibleValue of params.possibleValues){if($image.hasClass(`col-lg-${possibleValue}`)){return possibleValue;}}}}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_google_map/options.js */
odoo.define('options.s_google_map_options',function(require){'use strict';const{_t}=require('web.core');const options=require('web_editor.snippets.options');options.registry.GoogleMap=options.Class.extend({resetMapColor(previewMode,widgetValue,params){this.$target[0].dataset.mapColor='';},setFormattedAddress(previewMode,widgetValue,params){this.$target[0].dataset.pinAddress=params.gmapPlace.formatted_address;},async showDescription(previewMode,widgetValue,params){const descriptionEl=this.$target[0].querySelector('.description');if(widgetValue&&!descriptionEl){this.$target.append($(`
                <div class="description">
                    <font>${_t('Visit us:')}</font>
                    <span>${_t('Our office is located in the northeast of Brussels. TEL (*************')}</span>
                </div>`));}else if(!widgetValue&&descriptionEl){descriptionEl.remove();}},_computeWidgetState(methodName,params){if(methodName==='showDescription'){return this.$target[0].querySelector('.description')?'true':'';}
return this._super(...arguments);},});});;

/* /website/static/src/snippets/s_map/options.js */
odoo.define('@website/snippets/s_map/options',async function(require){'use strict';let __exports={};const{_t}=require('web.core');const options=require('web_editor.snippets.options');const{generateGMapIframe,generateGMapLink}=require('website.utils');options.registry.Map=options.Class.extend({onBuilt(){const iframeEl=generateGMapIframe();this.$target[0].querySelector('.s_map_color_filter').before(iframeEl);this._updateSource();},async selectDataAttribute(previewMode,widgetValue,params){await this._super(...arguments);if(['mapAddress','mapType','mapZoom'].includes(params.attributeName)){this._updateSource();}},async showDescription(previewMode,widgetValue,params){const descriptionEl=this.$target[0].querySelector('.description');if(widgetValue&&!descriptionEl){this.$target.append($(`
                <div class="description">
                    <font>${_t('Visit us:')}</font>
                    <span>${_t('Our office is open Monday – Friday 8:30 a.m. – 4:00 p.m.')}</span>
                </div>`));}else if(!widgetValue&&descriptionEl){descriptionEl.remove();}},_computeWidgetState(methodName,params){if(methodName==='showDescription'){return!!this.$target[0].querySelector('.description');}
return this._super(...arguments);},_updateSource(){const dataset=this.$target[0].dataset;const $embedded=this.$target.find('.s_map_embedded');const $info=this.$target.find('.missing_option_warning');if(dataset.mapAddress){const url=generateGMapLink(dataset);if(url!==$embedded.attr('src')){$embedded.attr('src',url);}
$embedded.removeClass('d-none');$info.addClass('d-none');}else{$embedded.attr('src','about:blank');$embedded.addClass('d-none');$info.removeClass('d-none');}},});__exports[Symbol.for("default")]={Map:options.registry.Map,};return __exports;});;

/* /website/static/src/snippets/s_dynamic_snippet/options.js */
odoo.define('website.s_dynamic_snippet_options',function(require){'use strict';const options=require('web_editor.snippets.options');const dynamicSnippetOptions=options.Class.extend({init:function(){this._super.apply(this,arguments);this.modelNameFilter=undefined;this.contextualFilterDomain=[];this.dynamicFilters={};this.currentModelName=undefined;this.dynamicFilterTemplates={};this.isOptionDefault={};},async willStart(){const _super=this._super.bind(this);await this._fetchDynamicFilters();await this._fetchDynamicFilterTemplates();return _super(...arguments);},async onBuilt(){this.$target[0].dataset['snippet']='s_dynamic_snippet';await this._setOptionsDefaultValues();const classList=[...this.$target[0].classList];if(classList.includes('d-none')&&!classList.some(className=>className.match(/^d-(md|lg)-(?!none)/))){this.$target[0].classList.remove('d-none');}
return this._refreshPublicWidgets();},selectDataAttribute:function(previewMode,widgetValue,params){this._super.apply(this,arguments);if(params.attributeName==='filterId'&&previewMode===false){const filter=this.dynamicFilters[parseInt(widgetValue)];this.$target.get(0).dataset.numberOfRecords=filter.limit;return this._filterUpdated(filter);}
if(params.attributeName==='templateKey'&&previewMode===false){this._templateUpdated(widgetValue,params.activeValue);}
if(params.attributeName==='numberOfRecords'&&previewMode===false){this.$target.get(0).dataset.forceMinimumMaxLimitTo16='1';}},async updateUI(){if(this.rerender){this.rerender=false;await this._rerenderXML();return;}
await this._super(...arguments);},_getCurrentTemplate:function(){return this.dynamicFilterTemplates[this.$target.get(0).dataset['templateKey']];},_getTemplateClass:function(templateKey){return templateKey.replace(/.*\.dynamic_filter_template_/,"s_");},_computeWidgetVisibility:function(widgetName,params){if(widgetName==='filter_opt'){return Object.keys(this.dynamicFilters).length!==1;}
if(widgetName==='number_of_records_opt'){const template=this._getCurrentTemplate();return template&&!template.numOfElFetch;}
return this._super.apply(this,arguments);},_refreshPublicWidgets:function(){return this._super.apply(this,arguments).then(()=>{const template=this._getCurrentTemplate();this.$target.find('.missing_option_warning').toggleClass('d-none',!!template);});},async _fetchDynamicFilters(){const dynamicFilters=await this._rpc({route:'/website/snippet/options_filters',params:{model_name:this.modelNameFilter,search_domain:this.contextualFilterDomain,}});if(!dynamicFilters.length){return;}
for(let index in dynamicFilters){this.dynamicFilters[dynamicFilters[index].id]=dynamicFilters[index];}
this._defaultFilterId=dynamicFilters[0].id;},async _fetchDynamicFilterTemplates(){const filter=this.dynamicFilters[this.$target.get(0).dataset['filterId']]||this.dynamicFilters[this._defaultFilterId];this.dynamicFilterTemplates={};if(!filter){return[];}
const dynamicFilterTemplates=await this._rpc({route:'/website/snippet/filter_templates',params:{filter_name:filter.model_name.replaceAll('.','_'),}});for(let index in dynamicFilterTemplates){this.dynamicFilterTemplates[dynamicFilterTemplates[index].key]=dynamicFilterTemplates[index];}
this._defaultTemplateKey=dynamicFilterTemplates[0].key;},_renderCustomXML:async function(uiFragment){await this._renderDynamicFiltersSelector(uiFragment);await this._renderDynamicFilterTemplatesSelector(uiFragment);},_renderDynamicFiltersSelector:async function(uiFragment){const filtersSelectorEl=uiFragment.querySelector('[data-name="filter_opt"]');return this._renderSelectUserValueWidgetButtons(filtersSelectorEl,this.dynamicFilters);},_renderSelectUserValueWidgetButtons:async function(selectUserValueWidgetElement,data){for(let id in data){const button=document.createElement('we-button');button.dataset.selectDataAttribute=id;if(data[id].thumb){button.dataset.img=data[id].thumb;}else{button.innerText=data[id].name;}
selectUserValueWidgetElement.appendChild(button);}},_renderDynamicFilterTemplatesSelector:async function(uiFragment){const templatesSelectorEl=uiFragment.querySelector('[data-name="template_opt"]');return this._renderSelectUserValueWidgetButtons(templatesSelectorEl,this.dynamicFilterTemplates);},async _setOptionsDefaultValues(){this.options.wysiwyg.odooEditor.observerUnactive();const filterKeys=this.$el.find("we-select[data-attribute-name='filterId'] we-selection-items we-button");if(filterKeys.length>0){this._setOptionValue('numberOfRecords',this.dynamicFilters[Object.keys(this.dynamicFilters)[0]].limit);}
let selectedFilterId=this.$target.get(0).dataset['filterId'];if(Object.keys(this.dynamicFilters).length>0){if(!this.dynamicFilters[selectedFilterId]){this.$target.get(0).dataset['filterId']=this._defaultFilterId;this.isOptionDefault['filterId']=true;selectedFilterId=this._defaultFilterId;}}
if(this.dynamicFilters[selectedFilterId]&&!this.dynamicFilterTemplates[this.$target.get(0).dataset['templateKey']]){this._setDefaultTemplate();}
this.options.wysiwyg.odooEditor.observerActive();},async _filterUpdated(filter){if(filter&&this.currentModelName!==filter.model_name){this.currentModelName=filter.model_name;await this._fetchDynamicFilterTemplates();if(Object.keys(this.dynamicFilterTemplates).length>0){const selectedTemplateId=this.$target.get(0).dataset['templateKey'];if(!this.dynamicFilterTemplates[selectedTemplateId]){this._setDefaultTemplate();}}
this.rerender=true;}},_setDefaultTemplate(){if(Object.keys(this.dynamicFilterTemplates).length){this.$target.get(0).dataset['templateKey']=this._defaultTemplateKey;this.isOptionDefault['templateKey']=true;this._templateUpdated(this._defaultTemplateKey);}},_templateUpdated(newTemplateKey,oldTemplateKey){if(oldTemplateKey){this.$target.removeClass(this._getTemplateClass(oldTemplateKey));}
this.$target.addClass(this._getTemplateClass(newTemplateKey));const template=this.dynamicFilterTemplates[newTemplateKey];if(template.numOfEl){this.$target[0].dataset.numberOfElements=template.numOfEl;}else{delete this.$target[0].dataset.numberOfElements;}
if(template.numOfElSm){this.$target[0].dataset.numberOfElementsSmallDevices=template.numOfElSm;}else{delete this.$target[0].dataset.numberOfElementsSmallDevices;}
if(template.numOfElFetch){this.$target[0].dataset.numberOfRecords=template.numOfElFetch;}
if(template.extraClasses){this.$target[0].dataset.extraClasses=template.extraClasses;}else{delete this.$target[0].dataset.extraClasses;}},_setOptionValue:function(optionName,value){const selectedTemplateId=this.$target.get(0).dataset['templateKey'];if(this.$target.get(0).dataset[optionName]===undefined||this.isOptionDefault[optionName]){this.$target.get(0).dataset[optionName]=value;this.isOptionDefault[optionName]=false;}
if(optionName==='templateKey'){this._templateUpdated(value,selectedTemplateId);}},});options.registry.dynamic_snippet=dynamicSnippetOptions;return dynamicSnippetOptions;});;

/* /website/static/src/snippets/s_dynamic_snippet_carousel/options.js */
odoo.define('website.s_dynamic_snippet_carousel_options',function(require){'use strict';const options=require('web_editor.snippets.options');const s_dynamic_snippet_options=require('website.s_dynamic_snippet_options');const dynamicSnippetCarouselOptions=s_dynamic_snippet_options.extend({onBuilt(){this._super(...arguments);this.$target[0].dataset['snippet']='s_dynamic_snippet_carousel';},_setOptionsDefaultValues:function(){this._super.apply(this,arguments);this._setOptionValue('carouselInterval','5000');},_templateUpdated(newTemplate,oldTemplate){this._super(...arguments);const template=this.dynamicFilterTemplates[newTemplate];if(template.rowPerSlide){this.$target[0].dataset.rowPerSlide=template.rowPerSlide;}else{delete this.$target[0].dataset.rowPerSlide;}
if(template.arrowPosition){this.$target[0].dataset.arrowPosition=template.arrowPosition;}else{delete this.$target[0].dataset.arrowPosition;}},});options.registry.dynamic_snippet_carousel=dynamicSnippetCarouselOptions;return dynamicSnippetCarouselOptions;});;

/* /website/static/src/snippets/s_embed_code/options.js */
odoo.define('@website/snippets/s_embed_code/options',async function(require){'use strict';let __exports={};const Dialog=require('web.Dialog');const core=require('web.core');const options=require('web_editor.snippets.options');const{loadBundle}=require("@web/core/assets");const _t=core._t;options.registry.EmbedCode=options.Class.extend({async editCode(){const $container=this.$target.find('.s_embed_code_embedded');const code=$container.html().trim();await loadBundle({jsLibs:['/web/static/lib/ace/ace.js','/web/static/lib/ace/mode-xml.js','/web/static/lib/ace/mode-qweb.js',],});await new Promise(resolve=>{const $content=$(core.qweb.render('website.custom_code_dialog_content'));const aceEditor=this._renderAceEditor($content.find('.o_ace_editor_container')[0],code||'');const dialog=new Dialog(this,{title:_t("Edit embedded code"),$content,buttons:[{text:_t("Save"),classes:'btn-primary',click:async()=>{$container[0].innerHTML=aceEditor.getValue();},close:true,},{text:_t("Discard"),close:true,},],});dialog.on('closed',this,resolve);dialog.open();});},_renderAceEditor(node,content){const aceEditor=window.ace.edit(node);aceEditor.setTheme('ace/theme/monokai');aceEditor.setValue(content,1);aceEditor.setOptions({minLines:20,maxLines:Infinity,showPrintMargin:false,});aceEditor.renderer.setOptions({highlightGutterLine:true,showInvisibles:true,fontSize:14,});const aceSession=aceEditor.getSession();aceSession.setOptions({mode:"ace/mode/xml",useWorker:false,});return aceEditor;},});__exports[Symbol.for("default")]={EmbedCode:options.registry.EmbedCode,};return __exports;});;

/* /website/static/src/snippets/s_website_form/options.js */
odoo.define('website.form_editor',function(require){'use strict';const core=require('web.core');const FormEditorRegistry=require('website.form_editor_registry');const options=require('web_editor.snippets.options');const Dialog=require('web.Dialog');const dom=require('web.dom');const{generateHTMLId}=require('web_editor.utils');require('website.editor.snippets.options');const qweb=core.qweb;const _t=core._t;const FormEditor=options.Class.extend({_fetchFieldRecords:async function(field){field.required=field.required?1:null;if(field.records){return field.records;}
if(field.type==='selection'){field.records=field.selection.map(el=>({id:el[0],display_name:el[1],}));}else if(field.relation&&field.relation!=='ir.attachment'){field.records=await this._rpc({model:field.relation,method:'search_read',args:[field.domain,['display_name']],});}
return field.records;},_getCustomField:function(type,name){return{name:name,string:name,custom:true,type:type,records:[{id:_t('Option 1'),display_name:_t('Option 1'),},{id:_t('Option 2'),display_name:_t('Option 2'),},{id:_t('Option 3'),display_name:_t('Option 3'),}],};},_getDefaultFormat:function(){return{labelWidth:this.$target[0].querySelector('.s_website_form_label').style.width,labelPosition:'left',multiPosition:'horizontal',requiredMark:this._isRequiredMark(),optionalMark:this._isOptionalMark(),mark:this._getMark(),};},_getMark:function(){return this.$target[0].dataset.mark;},_isOptionalMark:function(){return this.$target[0].classList.contains('o_mark_optional');},_isRequiredMark:function(){return this.$target[0].classList.contains('o_mark_required');},_renderField:function(field,resetId=false){if(!field.id){field.id=generateHTMLId();}
const template=document.createElement('template');template.innerHTML=qweb.render("website.form_field_"+field.type,{field:field}).trim();if(field.description&&field.description!==true){$(template.content.querySelector('.s_website_form_field_description')).replaceWith(field.description);}
template.content.querySelectorAll('input.datetimepicker-input').forEach(el=>el.value=field.propertyValue);return template.content.firstElementChild;},});const FieldEditor=FormEditor.extend({VISIBILITY_DATASET:['visibilityDependency','visibilityCondition','visibilityComparator','visibilityBetween'],init:function(){this._super.apply(this,arguments);this.formEl=this.$target[0].closest('form');},_getActiveField:function(noRecords){let field;const labelText=this.$target.find('.s_website_form_label_content').text();if(this._isFieldCustom()){field=this._getCustomField(this.$target[0].dataset.type,labelText);}else{field=Object.assign({},this.fields[this._getFieldName()]);field.string=labelText;}
if(!noRecords){field.records=this._getListItems();}
this._setActiveProperties(field);return field;},_getFieldFormat:function(){let requiredMark,optionalMark;const mark=this.$target[0].querySelector('.s_website_form_mark');if(mark){requiredMark=this._isFieldRequired();optionalMark=!requiredMark;}
const multipleInput=this._getMultipleInputs();const format={labelPosition:this._getLabelPosition(),labelWidth:this.$target[0].querySelector('.s_website_form_label').style.width,multiPosition:multipleInput&&multipleInput.dataset.display||'horizontal',col:[...this.$target[0].classList].filter(el=>el.match(/^col-/g)).join(' '),requiredMark:requiredMark,optionalMark:optionalMark,mark:mark&&mark.textContent,};return format;},_getFieldName:function(){const multipleName=this.$target[0].querySelector('.s_website_form_multiple');return multipleName?multipleName.dataset.name:this.$target[0].querySelector('.s_website_form_input').name;},_getFieldType:function(){return this.$target[0].dataset.type;},_getLabelPosition:function(){const label=this.$target[0].querySelector('.s_website_form_label');if(this.$target[0].querySelector('.row:not(.s_website_form_multiple)')){return label.classList.contains('text-end')?'right':'left';}else{return label.classList.contains('d-none')?'none':'top';}},_getMultipleInputs:function(){return this.$target[0].querySelector('.s_website_form_multiple');},_isFieldCustom:function(){return!!this.$target[0].classList.contains('s_website_form_custom');},_isFieldRequired:function(){const classList=this.$target[0].classList;return classList.contains('s_website_form_required')||classList.contains('s_website_form_model_required');},_setActiveProperties(field){const classList=this.$target[0].classList;const textarea=this.$target[0].querySelector('textarea');const input=this.$target[0].querySelector('input[type="text"], input[type="email"], input[type="number"], input[type="tel"], input[type="url"], textarea');const description=this.$target[0].querySelector('.s_website_form_field_description');field.placeholder=input&&input.placeholder;if(input){field.value=input.getAttribute('value')||input.value;}else if(field.type==='boolean'){field.value=!!this.$target[0].querySelector('input[type="checkbox"][checked]');}
field.propertyValue=input&&input.value;field.description=description&&description.outerHTML;field.rows=textarea&&textarea.rows;field.required=classList.contains('s_website_form_required');field.modelRequired=classList.contains('s_website_form_model_required');field.hidden=classList.contains('s_website_form_field_hidden');field.formatInfo=this._getFieldFormat();},});options.registry.WebsiteFormEditor=FormEditor.extend({events:_.extend({},options.Class.prototype.events||{},{'click .toggle-edit-message':'_onToggleEndMessageClick',}),willStart:async function(){const _super=this._super.bind(this);this.modelCantChange=this.$target.attr('hide-change-model')!==undefined;if(this.modelCantChange){return _super(...arguments);}
this.models=await this._rpc({model:'ir.model',method:'get_compatible_form_models',});const targetModelName=this.$target[0].dataset.model_name||'mail.mail';this.activeForm=_.findWhere(this.models,{model:targetModelName});this.selectActionEl=document.createElement('we-select');this.selectActionEl.setAttribute('string','Action');this.selectActionEl.dataset.noPreview='true';this.models.forEach(el=>{const option=document.createElement('we-button');option.textContent=el.website_form_label;option.dataset.selectAction=el.id;this.selectActionEl.append(option);});return _super(...arguments);},start:function(){const proms=[this._super(...arguments)];this.$target.attr('contentEditable',false);this.$target.find('.s_website_form_send, .s_website_form_field_description, .s_website_form_recaptcha').attr('contentEditable',true);this.$message=this.$target.parent().find('.s_website_form_end_message');this.showEndMessage=false;if(!this.$target[0].dataset.model_name){proms.push(this._applyFormModel());}
return Promise.all(proms);},cleanForSave:function(){const model=this.$target[0].dataset.model_name;if(model){const fields=[...this.$target[0].querySelectorAll('.s_website_form_field:not(.s_website_form_custom) .s_website_form_input')].map(el=>el.name);if(fields.length){this._rpc({model:'ir.model.fields',method:'formbuilder_whitelist',args:[model,_.uniq(fields)],});}}
if(this.$message.length){this.$target.removeClass('d-none');this.$message.addClass("d-none");}},updateUI:async function(){if(this.rerender){this.rerender=false;await this._rerenderXML();return;}
await this._super.apply(this,arguments);this.updateUIEndMessage();},updateUIEndMessage:function(){this.$target.toggleClass("d-none",this.showEndMessage);this.$message.toggleClass("d-none",!this.showEndMessage);this.$el.find(".toggle-edit-message").toggleClass('text-primary',this.showEndMessage);},notify:function(name,data){this._super(...arguments);if(name==='field_mark'){this._setLabelsMark();}else if(name==='add_field'){const field=this._getCustomField('char','Custom Text');field.formatInfo=data.formatInfo;field.formatInfo.requiredMark=this._isRequiredMark();field.formatInfo.optionalMark=this._isOptionalMark();field.formatInfo.mark=this._getMark();const fieldEl=this._renderField(field);data.$target.after(fieldEl);this.trigger_up('activate_snippet',{$snippet:$(fieldEl),});}},addActionField:function(previewMode,value,params){const fieldName=params.fieldName;if(params.isSelect==='true'){value=parseInt(value);}
this._addHiddenField(value,fieldName);},promptSaveRedirect:function(name,value,widgetValue){return new Promise((resolve,reject)=>{const message=_t("Would you like to save before being redirected? Unsaved changes will be discarded.");Dialog.confirm(this,message,{cancel_callback:()=>resolve(),buttons:[{text:_t("Save"),classes:'btn-primary',click:(ev)=>{const restore=dom.addButtonLoadingEffect(ev.currentTarget);this.trigger_up('request_save',{reload:false,onSuccess:()=>{this._redirectToAction(value);},onFailure:()=>{restore();this.displayNotification({message:_t("Something went wrong."),type:'danger',sticky:true,});reject();},});resolve();},},{text:_t("Discard"),click:(ev)=>{dom.addButtonLoadingEffect(ev.currentTarget);this._redirectToAction(value);},},{text:_t("Cancel"),close:true,click:()=>resolve(),},],});});},onSuccess:function(previewMode,value,params){this.$target[0].dataset.successMode=value;if(value==='message'){if(!this.$message.length){this.$message=$(qweb.render('website.s_website_form_end_message'));}
this.$target.after(this.$message);}else{this.showEndMessage=false;this.$message.remove();}},selectAction:async function(previewMode,value,params){if(this.modelCantChange){return;}
await this._applyFormModel(parseInt(value));this.rerender=true;},selectClass:function(previewMode,value,params){this._super(...arguments);if(params.name==='field_mark_select'){this._setLabelsMark();}},setMark:function(previewMode,value,params){this.$target[0].dataset.mark=value.trim();this._setLabelsMark();},toggleRecaptchaLegal:function(previewMode,value,params){const recaptchaLegalEl=this.$target[0].querySelector('.s_website_form_recaptcha');if(recaptchaLegalEl){recaptchaLegalEl.remove();}else{const template=document.createElement('template');const labelWidth=this.$target[0].querySelector('.s_website_form_label').style.width;$(template).html(qweb.render("website.s_website_form_recaptcha_legal",{labelWidth:labelWidth}));const legal=template.content.firstElementChild;legal.setAttribute('contentEditable',true);this.$target.find('.s_website_form_submit').before(legal);}},_computeWidgetState:function(methodName,params){switch(methodName){case'selectAction':return this.activeForm.id;case'addActionField':{const value=this.$target.find(`.s_website_form_dnone input[name="${params.fieldName}"]`).val();if(value){return value;}else{return params.isSelect?'0':'';}}
case'onSuccess':return this.$target[0].dataset.successMode;case'setMark':return this._getMark();case'toggleRecaptchaLegal':return!this.$target[0].querySelector('.s_website_form_recaptcha')||'';}
return this._super(...arguments);},_renderCustomXML:function(uiFragment){if(this.modelCantChange){return;}
const firstOption=uiFragment.childNodes[0];uiFragment.insertBefore(this.selectActionEl.cloneNode(true),firstOption);const formKey=this.activeForm.website_form_key;const formInfo=FormEditorRegistry.get(formKey);if(!formInfo||!formInfo.fields){return;}
const proms=formInfo.fields.map(field=>this._fetchFieldRecords(field));return Promise.all(proms).then(()=>{formInfo.fields.forEach(field=>{let option;switch(field.type){case'many2one':option=this._buildSelect(field);break;case'char':option=this._buildInput(field);break;}
if(field.required){const currentValue=this.$target.find(`.s_website_form_dnone input[name="${field.name}"]`).val();const defaultValue=field.defaultValue||field.records[0].id;this._addHiddenField(currentValue||defaultValue,field.name);}
uiFragment.insertBefore(option,firstOption);});});},_addHiddenField:function(value,fieldName){this.$target.find(`.s_website_form_dnone:has(input[name="${fieldName}"])`).remove();if(value){const hiddenField=qweb.render('website.form_field_hidden',{field:{name:fieldName,value:value,},});this.$target.find('.s_website_form_submit').before(hiddenField);}},_buildInput:function(field){const inputEl=document.createElement('we-input');inputEl.dataset.noPreview='true';inputEl.dataset.fieldName=field.name;inputEl.dataset.addActionField='';inputEl.setAttribute('string',field.string);inputEl.classList.add('o_we_large');return inputEl;},_buildSelect:function(field){const selectEl=document.createElement('we-select');selectEl.dataset.noPreview='true';selectEl.dataset.fieldName=field.name;selectEl.dataset.isSelect='true';selectEl.setAttribute('string',field.string);if(!field.required){const noneButton=document.createElement('we-button');noneButton.textContent='None';noneButton.dataset.addActionField=0;selectEl.append(noneButton);}
field.records.forEach(el=>{const button=document.createElement('we-button');button.textContent=el.display_name;button.dataset.addActionField=el.id;selectEl.append(button);});if(field.createAction){return this._addCreateButton(selectEl,field.createAction);}
return selectEl;},_addCreateButton:function(element,action){const linkButtonEl=document.createElement('we-button');linkButtonEl.title=_t("Create new");linkButtonEl.dataset.noPreview='true';linkButtonEl.dataset.promptSaveRedirect=action;linkButtonEl.classList.add('fa','fa-fw','fa-plus');const projectRowEl=document.createElement('we-row');projectRowEl.append(element);projectRowEl.append(linkButtonEl);return projectRowEl;},_applyFormModel:async function(modelId){let oldFormInfo;if(modelId){const oldFormKey=this.activeForm.website_form_key;if(oldFormKey){oldFormInfo=FormEditorRegistry.get(oldFormKey);}
this.$target.find('.s_website_form_field').remove();this.activeForm=_.findWhere(this.models,{id:modelId});}
const formKey=this.activeForm.website_form_key;const formInfo=FormEditorRegistry.get(formKey);if(!this.$target[0].dataset.successMode){this.$target[0].dataset.successMode='redirect';}
if(this.$target[0].dataset.successMode==='redirect'){const currentSuccessPage=this.$target[0].dataset.successPage;if(formInfo&&formInfo.successPage){this.$target[0].dataset.successPage=formInfo.successPage;}else if(!oldFormInfo||(oldFormInfo!==formInfo&&oldFormInfo.successPage&&currentSuccessPage===oldFormInfo.successPage)){this.$target[0].dataset.successPage='/contactus-thank-you';}}
this.$target[0].dataset.model_name=this.activeForm.model;if(formInfo){const formatInfo=this._getDefaultFormat();await formInfo.formFields.forEach(async field=>{field.formatInfo=formatInfo;await this._fetchFieldRecords(field);this.$target.find('.s_website_form_submit, .s_website_form_recaptcha').first().before(this._renderField(field));});}},_setLabelsMark:function(){this.$target[0].querySelectorAll('.s_website_form_mark').forEach(el=>el.remove());const mark=this._getMark();if(!mark){return;}
let fieldsToMark=[];const requiredSelector='.s_website_form_model_required, .s_website_form_required';const fields=Array.from(this.$target[0].querySelectorAll('.s_website_form_field'));if(this._isRequiredMark()){fieldsToMark=fields.filter(el=>el.matches(requiredSelector));}else if(this._isOptionalMark()){fieldsToMark=fields.filter(el=>!el.matches(requiredSelector));}
fieldsToMark.forEach(field=>{let span=document.createElement('span');span.classList.add('s_website_form_mark');span.textContent=` ${mark}`;field.querySelector('.s_website_form_label').appendChild(span);});},_redirectToAction:function(action){window.location.replace(`/web#action=${encodeURIComponent(action)}`);},_onToggleEndMessageClick:function(){this.showEndMessage=!this.showEndMessage;this.updateUIEndMessage();this.trigger_up('activate_snippet',{$snippet:this.showEndMessage?this.$message:this.$target,});},});const authorizedFieldsCache={};options.registry.WebsiteFieldEditor=FieldEditor.extend({init:function(){this._super.apply(this,arguments);this.rerender=true;},willStart:async function(){const _super=this._super.bind(this);const model=this.formEl.dataset.model_name;let getFields;if(model in authorizedFieldsCache){getFields=authorizedFieldsCache[model];}else{getFields=this._rpc({model:"ir.model",method:"get_authorized_fields",args:[model],});authorizedFieldsCache[model]=getFields;}
this.existingFields=await getFields.then(fields=>{this.fields=_.each(fields,function(field,fieldName){field.name=fieldName;field.domain=field.domain||[];});return Object.keys(fields).map(key=>{const field=fields[key];const button=document.createElement('we-button');button.textContent=field.string;button.dataset.existingField=field.name;return button;}).sort((a,b)=>(a.textContent>b.textContent)?1:(a.textContent<b.textContent)?-1:0);});return _super(...arguments);},start:async function(){const _super=this._super.bind(this);const select=this._getSelect();if(select){const field=this._getActiveField();await this._replaceField(field);}
return _super(...arguments);},cleanForSave:function(){this.$target[0].querySelectorAll('#editable_select').forEach(el=>el.remove());const select=this._getSelect();if(select){select.style.display='';}},updateUI:async function(){if(this.rerender){this.rerender=false;await this._rerenderXML();return;}
await this._super.apply(this,arguments);},onFocus:function(){this.rerender=true;},onClone(){const field=this._getActiveField();delete field.id;const fieldEl=this._renderField(field);this._replaceFieldElement(fieldEl);},onRemove(){const fieldName=this.$target[0].querySelector('.s_website_form_input').name;const isMultipleField=this.formEl.querySelectorAll(`.s_website_form_input[name="${fieldName}"]`).length>1;if(isMultipleField){return;}
const dependentFieldContainerEl=this.formEl.querySelectorAll(`[data-visibility-dependency="${fieldName}"]`);for(const fieldContainerEl of dependentFieldContainerEl){this._deleteConditionalVisibility(fieldContainerEl);}},toggleDescription:async function(previewMode,value,params){const field=this._getActiveField();field.description=!!value;await this._replaceField(field);},customField:async function(previewMode,value,params){if(!value){return;}
const oldLabelText=this.$target[0].querySelector('.s_website_form_label_content').textContent;const field=this._getCustomField(value,oldLabelText);this._setActiveProperties(field);await this._replaceField(field);this.rerender=true;},existingField:async function(previewMode,value,params){if(!value){return;}
const field=Object.assign({},this.fields[value]);this._setActiveProperties(field);await this._replaceField(field);this.rerender=true;},setLabelText:function(previewMode,value,params){this.$target.find('.s_website_form_label_content').text(value);if(this._isFieldCustom()){const multiple=this.$target[0].querySelector('.s_website_form_multiple');if(multiple){multiple.dataset.name=value;}
const inputEls=this.$target[0].querySelectorAll('.s_website_form_input');const previousInputName=inputEls[0].name;inputEls.forEach(el=>el.name=value);const dependentEls=this.formEl.querySelectorAll(`.s_website_form_field[data-visibility-dependency="${previousInputName}"]`);for(const dependentEl of dependentEls){dependentEl.dataset.visibilityDependency=value;}}},selectLabelPosition:async function(previewMode,value,params){const field=this._getActiveField();field.formatInfo.labelPosition=value;await this._replaceField(field);},selectType:async function(previewMode,value,params){const field=this._getActiveField();field.type=value;await this._replaceField(field);},selectTextareaValue:function(previewMode,value,params){this.$target[0].textContent=value;this.$target[0].value=value;},selectValueProperty:function(previewMode,value,params){this.$target[0].value=value?moment.unix(value).format(params.format):'';},multiCheckboxDisplay:function(previewMode,value,params){const target=this._getMultipleInputs();target.querySelectorAll('.checkbox, .radio').forEach(el=>{if(value==='horizontal'){el.classList.add('col-lg-4','col-md-6');}else{el.classList.remove('col-lg-4','col-md-6');}});target.dataset.display=value;},toggleRequired:function(previewMode,value,params){const isRequired=this.$target[0].classList.contains(params.activeValue);this.$target[0].classList.toggle(params.activeValue,!isRequired);this.$target[0].querySelectorAll('input, select, textarea').forEach(el=>el.toggleAttribute('required',!isRequired));this.trigger_up('option_update',{optionName:'WebsiteFormEditor',name:'field_mark',});},renderListItems:async function(previewMode,value,params){const valueList=JSON.parse(value);const newValuesText=valueList.map(value=>value.name);const inputEls=this.$target[0].querySelectorAll('.s_website_form_input, option');const inputName=this.$target[0].querySelector('.s_website_form_input').name;for(let i=0;i<inputEls.length;i++){const input=inputEls[i];if(newValuesText[i]&&input.value&&!newValuesText.includes(input.value)){for(const dependentEl of this.formEl.querySelectorAll(`[data-visibility-condition="${input.value}"][data-visibility-dependency="${inputName}"]`)){dependentEl.dataset.visibilityCondition=newValuesText[i];}
break;}}
const field=this._getActiveField(true);field.records=valueList;await this._replaceField(field);},setVisibility(previewMode,widgetValue,params){if(widgetValue==='conditional'){const widget=this.findWidget('hidden_condition_opt');const firstValue=widget.getMethodsParams('setVisibilityDependency').possibleValues.find(el=>el!=='');if(firstValue){this._setVisibilityDependency(firstValue);return;}
Dialog.confirm(this,_t("There is no field available for this option."));}
this._deleteConditionalVisibility(this.$target[0]);},setVisibilityDependency(previewMode,widgetValue,params){this._setVisibilityDependency(widgetValue);},_computeWidgetState:function(methodName,params){switch(methodName){case'toggleDescription':{const description=this.$target[0].querySelector('.s_website_form_field_description');return!!description;}
case'customField':return this._isFieldCustom()?this._getFieldType():'';case'existingField':return this._isFieldCustom()?'':this._getFieldName();case'setLabelText':return this.$target.find('.s_website_form_label_content').text();case'selectLabelPosition':return this._getLabelPosition();case'selectType':return this._getFieldType();case'selectTextareaValue':return this.$target[0].textContent;case'selectValueProperty':return this.$target[0].getAttribute('value')||'';case'multiCheckboxDisplay':{const target=this._getMultipleInputs();return target?target.dataset.display:'';}
case'toggleRequired':return this.$target[0].classList.contains(params.activeValue)?params.activeValue:'false';case'renderListItems':return JSON.stringify(this._getListItems());case'setVisibilityDependency':return this.$target[0].dataset.visibilityDependency||'';}
return this._super(...arguments);},_computeWidgetVisibility:function(widgetName,params){const dependencyEl=this._getDependencyEl();switch(widgetName){case'hidden_condition_time_comparators_opt':return dependencyEl&&dependencyEl.dataset.target;case'hidden_condition_date_between':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datepicker')&&['between','!between'].includes(this.$target[0].getAttribute('data-visibility-comparator'));case'hidden_condition_datetime_between':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datetimepicker')&&['between','!between'].includes(this.$target[0].dataset.visibilityComparator);case'hidden_condition_additional_datetime':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datetimepicker')&&!['set','!set'].includes(this.$target[0].dataset.visibilityComparator);case'hidden_condition_additional_date':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datepicker')&&!['set','!set'].includes(this.$target[0].dataset.visibilityComparator);case'hidden_condition_additional_text':if(!this.$target[0].classList.contains('s_website_form_field_hidden_if')||(dependencyEl&&(['checkbox','radio'].includes(dependencyEl.type)||dependencyEl.nodeName==='SELECT'))){return false;}
if(!dependencyEl){return true;}
if(dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#date')){return false;}
return(['text','email','tel','url','search','password','number'].includes(dependencyEl.type)||dependencyEl.nodeName==='TEXTAREA')&&!['set','!set'].includes(this.$target[0].dataset.visibilityComparator);case'hidden_condition_no_text_opt':return dependencyEl&&(dependencyEl.type==='checkbox'||dependencyEl.type==='radio'||dependencyEl.nodeName==='SELECT');case'hidden_condition_num_opt':return dependencyEl&&dependencyEl.type==='number';case'hidden_condition_text_opt':if(!this.$target[0].classList.contains('s_website_form_field_hidden_if')||(dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#date'))){return false;}
return!dependencyEl||(['text','email','tel','url','search','password'].includes(dependencyEl.type)||dependencyEl.nodeName==='TEXTAREA');case'hidden_condition_date_opt':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datepicker');case'hidden_condition_datetime_opt':return dependencyEl&&dependencyEl.dataset.target&&dependencyEl.dataset.target.includes('#datetimepicker');case'hidden_condition_file_opt':return dependencyEl&&dependencyEl.type==='file';case'hidden_condition_opt':return this.$target[0].classList.contains('s_website_form_field_hidden_if');case'char_input_type_opt':return!this.$target[0].classList.contains('s_website_form_custom')&&['char','email','tel','url'].includes(this.$target[0].dataset.type);case'multi_check_display_opt':return!!this._getMultipleInputs();case'required_opt':case'hidden_opt':case'type_opt':return!this.$target[0].classList.contains('s_website_form_model_required');}
return this._super(...arguments);},_deleteConditionalVisibility(fieldEl){for(const name of this.VISIBILITY_DATASET){delete fieldEl.dataset[name];}
fieldEl.classList.remove('s_website_form_field_hidden_if','d-none');},_getDependencyEl(fieldEl=this.$target[0]){const dependencyName=fieldEl.dataset.visibilityDependency;return this.formEl.querySelector(`.s_website_form_input[name="${dependencyName}"]`);},_renderCustomXML:async function(uiFragment){const recursiveFindCircular=(el)=>{if(el.dataset.visibilityDependency===this._getFieldName()){return true;}
const dependencyInputEl=this._getDependencyEl(el);if(!dependencyInputEl){return false;}
return recursiveFindCircular(dependencyInputEl.closest('.s_website_form_field'));};const selectDependencyEl=uiFragment.querySelector('we-select[data-name="hidden_condition_opt"]');const existingDependencyNames=[];for(const el of this.formEl.querySelectorAll('.s_website_form_field:not(.s_website_form_dnone)')){const inputEl=el.querySelector('.s_website_form_input');if(el.querySelector('.s_website_form_label_content')&&inputEl&&inputEl.name&&inputEl.name!==this.$target[0].querySelector('.s_website_form_input').name&&!existingDependencyNames.includes(inputEl.name)&&!recursiveFindCircular(el)){const button=document.createElement('we-button');button.textContent=el.querySelector('.s_website_form_label_content').textContent;button.dataset.setVisibilityDependency=inputEl.name;selectDependencyEl.append(button);existingDependencyNames.push(inputEl.name);}}
const comparator=this.$target[0].dataset.visibilityComparator;const dependencyEl=this._getDependencyEl();if(dependencyEl){if((['radio','checkbox'].includes(dependencyEl.type)||dependencyEl.nodeName==='SELECT')){const selectOptEl=uiFragment.querySelectorAll('we-select[data-name="hidden_condition_no_text_opt"]')[1];const inputContainerEl=this.$target[0];const dependencyEl=this._getDependencyEl();if(dependencyEl.nodeName==='SELECT'){for(const option of dependencyEl.querySelectorAll('option')){const button=document.createElement('we-button');button.textContent=option.value||`<${_t("no value")}>`;button.dataset.selectDataAttribute=option.value;selectOptEl.append(button);}
if(!inputContainerEl.dataset.visibilityCondition){inputContainerEl.dataset.visibilityCondition=dependencyEl.querySelector('option').value;}}else{const dependencyContainerEl=dependencyEl.closest('.s_website_form_field');const inputsInDependencyContainer=dependencyContainerEl.querySelectorAll('.s_website_form_input');for(const el of inputsInDependencyContainer){const button=document.createElement('we-button');button.textContent=el.value;button.dataset.selectDataAttribute=el.value;selectOptEl.append(button);}
if(!inputContainerEl.dataset.visibilityCondition){inputContainerEl.dataset.visibilityCondition=inputsInDependencyContainer[0].value;}}
if(!inputContainerEl.dataset.visibilityComparator){inputContainerEl.dataset.visibilityComparator='selected';}
this.rerender=comparator?this.rerender:true;}
if(!comparator){if(dependencyEl.dataset.target){this.$target[0].dataset.visibilityComparator='after';}else if(['text','email','tel','url','search','password','number'].includes(dependencyEl.type)||dependencyEl.nodeName==='TEXTAREA'){this.$target[0].dataset.visibilityComparator='equal';}else if(dependencyEl.type==='file'){this.$target[0].dataset.visibilityComparator='fileSet';}}}
const selectEl=uiFragment.querySelector('we-select[data-name="type_opt"]');const currentFieldName=this._getFieldName();const fieldsInForm=Array.from(this.formEl.querySelectorAll('.s_website_form_field:not(.s_website_form_custom) .s_website_form_input')).map(el=>el.name).filter(el=>el!==currentFieldName);const availableFields=this.existingFields.filter(el=>!fieldsInForm.includes(el.dataset.existingField));if(availableFields.length){const title=document.createElement('we-title');title.textContent='Existing fields';availableFields.unshift(title);availableFields.forEach(option=>selectEl.append(option.cloneNode(true)));}
const select=this._getSelect();const multipleInputs=this._getMultipleInputs();if(!select&&!multipleInputs){return;}
const field=Object.assign({},this.fields[this._getFieldName()]);const type=this._getFieldType();const list=document.createElement('we-list');const optionText=select?'Option':type==='selection'?'Radio':'Checkbox';list.setAttribute('string',`${optionText} List`);list.dataset.addItemTitle=_.str.sprintf(_t("Add new %s"),optionText);list.dataset.renderListItems='';list.dataset.hasDefault=['one2many','many2many'].includes(type)?'multiple':'unique';const defaults=[...this.$target[0].querySelectorAll('[checked], [selected]')].map(el=>{return/^-?[0-9]{1,15}$/.test(el.value)?parseInt(el.value):el.value;});list.dataset.defaults=JSON.stringify(defaults);if(!this._isFieldCustom()){await this._fetchFieldRecords(field);list.dataset.availableRecords=JSON.stringify(field.records);}
uiFragment.insertBefore(list,uiFragment.querySelector('we-select[string="Visibility"]'));},_replaceField:async function(field){await this._fetchFieldRecords(field);const activeField=this._getActiveField();if(activeField.type!==field.type){field.value='';}
const fieldEl=this._renderField(field);this._replaceFieldElement(fieldEl);},_replaceFieldElement(fieldEl){const inputEl=this.$target[0].querySelector('input');const dataFillWith=inputEl?inputEl.dataset.fillWith:undefined;const hasConditionalVisibility=this.$target[0].classList.contains('s_website_form_field_hidden_if');const previousName=this.$target[0].querySelector('.s_website_form_input').name;[...this.$target[0].childNodes].forEach(node=>node.remove());[...fieldEl.childNodes].forEach(node=>this.$target[0].appendChild(node));[...fieldEl.attributes].forEach(el=>this.$target[0].removeAttribute(el.nodeName));[...fieldEl.attributes].forEach(el=>this.$target[0].setAttribute(el.nodeName,el.nodeValue));if(hasConditionalVisibility){this.$target[0].classList.add('s_website_form_field_hidden_if','d-none');}
const dependentFieldEls=this.formEl.querySelectorAll(`.s_website_form_field[data-visibility-dependency="${previousName}"]`);const newName=this.$target[0].querySelector('.s_website_form_input').name;if(previousName!==newName&&dependentFieldEls){for(const fieldEl of dependentFieldEls){this._deleteConditionalVisibility(fieldEl);}}
const newInputEl=this.$target[0].querySelector('input');if(newInputEl){newInputEl.dataset.fillWith=dataFillWith;}},_setVisibilityDependency(value){delete this.$target[0].dataset.visibilityCondition;delete this.$target[0].dataset.visibilityComparator;const previousDependency=this._getDependencyEl();if(this.formEl.querySelector(`.s_website_form_input[name="${value}"]`).type!==(previousDependency&&previousDependency.type)){this.rerender=true;}
this.$target[0].dataset.visibilityDependency=value;},_getListItems:function(){const select=this._getSelect();const multipleInputs=this._getMultipleInputs();let options=[];if(select){options=[...select.querySelectorAll('option')];}else if(multipleInputs){options=[...multipleInputs.querySelectorAll('.checkbox input, .radio input')];}
return options.map(opt=>{const name=select?opt:opt.nextElementSibling;return{id:/^-?[0-9]{1,15}$/.test(opt.value)?parseInt(opt.value):opt.value,display_name:name.textContent.trim(),selected:select?opt.selected:opt.checked,};});},_getSelect:function(){return this.$target[0].querySelector('select');},});options.registry.AddFieldForm=FormEditor.extend({isTopOption:true,isTopFirstOption:true,addField:async function(previewMode,value,params){const field=this._getCustomField('char','Custom Text');field.formatInfo=this._getDefaultFormat();const fieldEl=this._renderField(field);this.$target.find('.s_website_form_submit, .s_website_form_recaptcha').first().before(fieldEl);this.trigger_up('activate_snippet',{$snippet:$(fieldEl),});},});options.registry.AddField=FieldEditor.extend({isTopOption:true,isTopFirstOption:true,addField:async function(previewMode,value,params){this.trigger_up('option_update',{optionName:'WebsiteFormEditor',name:'add_field',data:{formatInfo:this._getFieldFormat(),$target:this.$target,},});},});const DisableOverlayButtonOption=options.Class.extend({disableButton:function(buttonName,message){const className='oe_snippet_'+buttonName;this.$overlay.add(this.$overlay.data('$optionsSection')).on('click','.'+className,this.preventButton);const $button=this.$overlay.add(this.$overlay.data('$optionsSection')).find('.'+className);$button.attr('title',message).tooltip({delay:0});$button.removeClass(className);},preventButton:function(event){event.preventDefault();event.stopImmediatePropagation();}});options.registry.WebsiteFormFieldModel=DisableOverlayButtonOption.extend({start:function(){this.disableButton('clone',_t('You can\'t duplicate a model field.'));return this._super.apply(this,arguments);}});options.registry.WebsiteFormFieldRequired=DisableOverlayButtonOption.extend({start:function(){this.disableButton('remove',_t('You can\'t remove a field that is required by the model itself.'));return this._super.apply(this,arguments);}});options.registry.WebsiteFormSubmitRequired=DisableOverlayButtonOption.extend({start:function(){this.disableButton('remove',_t('You can\'t remove the submit button of the form'));this.disableButton('clone',_t('You can\'t duplicate the submit button of the form.'));return this._super.apply(this,arguments);}});options.registry.DeviceVisibility.include({async _computeVisibility(){return await this._super(...arguments)&&!this.$target.hasClass('s_website_form_field_hidden');},});});;

/* /website/static/src/snippets/s_searchbar/options.js */
odoo.define('@website/snippets/s_searchbar/options',async function(require){'use strict';let __exports={};const options=require('web_editor.snippets.options');options.registry.SearchBar=options.Class.extend({setSearchType:function(previewMode,widgetValue,params){const form=this.$target.parents('form');form.attr('action',params.formAction);if(!previewMode){this.trigger_up('snippet_edition_request',{exec:()=>{const widget=this._requestUserValueWidgets('order_opt')[0];const orderBy=widget.getValue("selectDataAttribute");const order=widget.$el.find("we-button[data-select-data-attribute='"+orderBy+"']")[0];if(order.classList.contains("d-none")){const defaultOrder=widget.$el.find("we-button[data-name='order_name_asc_opt']")[0];defaultOrder.click();defaultOrder.click();}}});const displayOptions=new Set();for(const optionEl of this.$el[0].querySelectorAll('[data-dependencies="limit_opt"] [data-attribute-name^="display"]')){displayOptions.add(optionEl.dataset.attributeName);}
const scopeName=this.$el[0].querySelector(`[data-set-search-type="${widgetValue}"]`).dataset.name;for(const displayOption of displayOptions){this.$target[0].dataset[displayOption]=this.$el[0].querySelector(`[data-attribute-name="${displayOption}"][data-dependencies="${scopeName}"]`)?'true':'';}}},setOrderBy:function(previewMode,widgetValue,params){const form=this.$target.parents('form');form.find(".o_search_order_by").attr("value",widgetValue);},});__exports[Symbol.for("default")]={SearchBar:options.registry.SearchBar,};return __exports;});;

/* /website/static/src/snippets/s_social_media/options.js */
odoo.define('@website/snippets/s_social_media/options',async function(require){'use strict';let __exports={};const fonts=require('wysiwyg.fonts');const{generateHTMLId}=require('web_editor.utils');const options=require('web_editor.snippets.options');const{_t}=require('web.core');let dbSocialValues;let dbSocialValuesProm;const clearDbSocialValuesCache=()=>{dbSocialValuesProm=undefined;dbSocialValues=undefined;};options.registry.SocialMedia=options.Class.extend({start(){this.__onSetupBannerClick=this._onSetupBannerClick.bind(this);this.$target[0].addEventListener('click',this.__onSetupBannerClick);this.entriesNotInDom=[];return this._super(...arguments);},async onBuilt(){await this._fetchSocialMedia();for(const anchorEl of this.$target[0].querySelectorAll(':scope > a')){const mediaName=anchorEl.href.split('/website/social/').pop();if(mediaName&&!dbSocialValues[`social_${mediaName}`]){anchorEl.remove();}}
this._handleNoMediaAlert();},async cleanForSave(){if(!dbSocialValues){return;}
let websiteId;this.trigger_up('context_get',{callback:function(ctx){websiteId=ctx['website_id'];},});await this._rpc({model:'website',method:'write',args:[[websiteId],dbSocialValues],});},destroy(){this._super(...arguments);this.$target[0].removeEventListener('click',this.__onSetupBannerClick);},async renderListItems(previewMode,widgetValue,params){const anchorEls=this.$target[0].querySelectorAll(':scope > a');let entries=JSON.parse(widgetValue);const anchorsToRemoveEls=[];for(let i=0;i<anchorEls.length;i++){if(!entries.find(entry=>parseInt(entry.domPosition)===i)){anchorsToRemoveEls.push(anchorEls[i]);}}
for(const el of anchorsToRemoveEls){el.remove();}
this.entriesNotInDom=[];for(let listPosition=0;listPosition<entries.length;listPosition++){const entry=entries[listPosition];const url=entry.display_name;if(url&&!/^(([a-zA-Z]+):|\/)/.test(url)){entry.display_name=`https://${url}`;}
const isDbField=Boolean(entry.media);if(isDbField){dbSocialValues[`social_${entry.media}`]=entry.display_name;}
let anchorEl=anchorEls[entry.domPosition];if(entry.selected){if(!anchorEl){if(anchorEls.length===0){anchorEl=document.createElement('a');anchorEl.setAttribute('target','_blank');const iEl=document.createElement('i');iEl.classList.add('fa','rounded-circle','shadow-sm');anchorEl.appendChild(iEl);}else{anchorEl=this.$target[0].querySelector(':scope > a').cloneNode(true);this._removeSocialMediaClasses(anchorEl);}
const faIcon=isDbField?`fa-${entry.media}`:'fa-pencil';anchorEl.querySelector('i').classList.add(faIcon);if(isDbField){anchorEl.href=`/website/social/${encodeURIComponent(entry.media)}`;anchorEl.classList.add(`s_social_media_${entry.media}`);}}}else{if(anchorEl){delete entry.domPosition;anchorEl.remove();}
entry.listPosition=listPosition;this.entriesNotInDom.push(entry);continue;}
if(!isDbField){const href=anchorEl.getAttribute('href');if(href!==entry.display_name){if(this._isValidURL(entry.display_name)){const socialMedia=this._findRelevantSocialMedia(entry.display_name);if(socialMedia){const iEl=anchorEl.querySelector('i');this._removeSocialMediaClasses(anchorEl);anchorEl.classList.add(`s_social_media_${socialMedia}`);iEl.classList.add(`fa-${socialMedia}`);}}
anchorEl.setAttribute('href',entry.display_name);}}
this.$target[0].appendChild(anchorEl);}
this.$target[0].normalize();const finalLinkEls=this.$target[0].querySelectorAll(':scope > a');if(finalLinkEls.length){finalLinkEls[0].previousSibling.textContent='\n';for(const linkEl of finalLinkEls){linkEl.after(document.createTextNode('\n'));}}
this._handleNoMediaAlert();},async _computeWidgetState(methodName,params){if(methodName!=='renderListItems'){return this._super(methodName,params);}
await this._fetchSocialMedia();let listPosition=0;let domPosition=0;let entries=[...this.$target[0].querySelectorAll(':scope > a')].map(el=>{const media=el.href.split('/website/social/')[1];while(this.entriesNotInDom.find(entry=>entry.listPosition===listPosition)){listPosition++;}
return{id:generateHTMLId(),display_name:media?dbSocialValues[`social_${media}`]:el.getAttribute('href'),placeholder:`https://${encodeURIComponent(media) || 'example'}.com/yourPage`,undeletable:!!media,notToggleable:!media,selected:true,listPosition:listPosition++,domPosition:domPosition++,media:media,};});for(let[media,link]of Object.entries(dbSocialValues)){media=media.split('social_').pop();if(!this.$target[0].querySelector(`:scope > a[href="/website/social/${encodeURIComponent(media)}"]`)){const entryNotInDom=this.entriesNotInDom.find(entry=>entry.media===media);if(!entryNotInDom){this.entriesNotInDom.push({id:generateHTMLId(),display_name:link,placeholder:`https://${encodeURIComponent(media)}.com/yourPage`,undeletable:true,selected:false,listPosition:listPosition++,media:media,notToggleable:false,});}else{entryNotInDom.display_name=link;entryNotInDom.undeletable=true;entryNotInDom.notToggleable=false;}}}
entries=entries.concat(this.entriesNotInDom);entries.sort((a,b)=>{return a.listPosition-b.listPosition;});return JSON.stringify(entries);},async _fetchSocialMedia(){if(!dbSocialValuesProm){let websiteId;this.trigger_up('context_get',{callback:function(ctx){websiteId=ctx['website_id'];},});dbSocialValuesProm=this._rpc({model:'website',method:'read',args:[websiteId,['social_facebook','social_twitter','social_linkedin','social_youtube','social_instagram','social_github']],}).then(function(values){[dbSocialValues]=values;delete dbSocialValues.id;});}
await dbSocialValuesProm;},_findRelevantSocialMedia(url){const supportedSocialMedia=[['facebook',/^(https?:\/\/)(www\.)?(facebook|fb|m\.facebook)\.(com|me).*$/],['twitter',/^(https?:\/\/)((www\.)?twitter\.com).*$/],['youtube',/^(https?:\/\/)(www\.)?(youtube.com|youtu.be).*$/],['instagram',/^(https?:\/\/)(www\.)?(instagram.com|instagr.am|instagr.com).*$/],['linkedin',/^(https?:\/\/)((www\.)?linkedin\.com).*$/],['github',/^(https?:\/\/)((www\.)?github\.com).*$/],];for(const[socialMedia,regex]of supportedSocialMedia){if(regex.test(url)){return socialMedia;}}
try{const domain=new URL(url).hostname.split('.').slice(-2)[0];fonts.computeFonts();const iconNames=fonts.fontIcons[0].alias;const exactIcon=iconNames.find(el=>el===`fa-${domain}`);return(exactIcon||iconNames.find(el=>el.includes(domain))).split('fa-').pop();}catch(_error){return false;}},_handleNoMediaAlert(){const alertEl=this.$target[0].querySelector('div.css_non_editable_mode_hidden');if(this.$target[0].querySelector(':scope > a:not(.d-none)')){if(alertEl){alertEl.remove();}}else{if(!alertEl){const divEl=document.createElement('div');const classes=['alert','alert-info','css_non_editable_mode_hidden','text-center'];divEl.classList.add(...classes);const spanEl=document.createElement('span');spanEl.textContent=_t("Click here to setup your social networks");this.$target[0].appendChild(divEl).append(spanEl);}}},_isValidURL(str){let url;try{url=new URL(str);}catch(_error){return false;}
return url.protocol.startsWith('http');},_removeSocialMediaClasses(anchorEl){let regx=new RegExp('\\b'+'s_social_media_'+'[^1-9][^ ]*[ ]?\\b');anchorEl.className=anchorEl.className.replace(regx,'');const iEl=anchorEl.querySelector('i');regx=new RegExp('\\b'+'fa-'+'[^1-9][^ ]*[ ]?\\b');iEl.className=iEl.className.replace(regx,'');},_onSetupBannerClick(ev){if(ev.target.closest('div.css_non_editable_mode_hidden')){this._requestUserValueWidgets('social_media_list')[0].focus();}},});__exports[Symbol.for("default")]={SocialMedia:options.registry.SocialMedia,clearDbSocialValuesCache,};return __exports;});;

/* /website/static/src/snippets/s_process_steps/options.js */
odoo.define('@website/snippets/s_process_steps/options',async function(require){'use strict';let __exports={};const options=require('web_editor.snippets.options');const weUtils=require('web_editor.utils');const{SIZES,MEDIAS_BREAKPOINTS}=require('@web/core/ui/ui_service');options.registry.StepsConnector=options.Class.extend({start(){this.$target.on('content_changed.StepsConnector',()=>this._reloadConnectors());return this._super(...arguments);},destroy(){this._super(...arguments);this.$target.off('.StepsConnector');},selectClass:function(previewMode,value,params){this._super(...arguments);if(params.name==='connector_type'){this._reloadConnectors();let markerEnd='';if(['s_process_steps_connector_arrow','s_process_steps_connector_curved_arrow'].includes(value)){const arrowHeadEl=this.$target[0].querySelector('.s_process_steps_arrow_head');if(!arrowHeadEl.id){arrowHeadEl.id='s_process_steps_arrow_head'+Date.now();}
markerEnd=`url(#${arrowHeadEl.id})`;}
this.$target[0].querySelectorAll('.s_process_step_connector path').forEach(path=>path.setAttribute('marker-end',markerEnd));}},changeColor(previewMode,widgetValue,params){const htmlPropColor=weUtils.getCSSVariableValue(widgetValue);const arrowHeadEl=this.$target[0].closest('.s_process_steps').querySelector('.s_process_steps_arrow_head');arrowHeadEl.querySelector('path').style.fill=htmlPropColor||widgetValue;},notify(name){if(['change_column_size','change_container_width','change_columns','move_snippet'].includes(name)){this._reloadConnectors();}else{this._super(...arguments);}},_computeVisibility(){const mobileViewThreshold=MEDIAS_BREAKPOINTS[SIZES.LG].minWidth;const isMobileView=this.$target[0].ownerDocument.documentElement.clientWidth<mobileViewThreshold;return!isMobileView&&this._super(...arguments);},_reloadConnectors(){const possibleTypes=this._requestUserValueWidgets('connector_type')[0].getMethodsParams().optionsPossibleValues.selectClass;const type=possibleTypes.find(possibleType=>possibleType&&this.$target[0].classList.contains(possibleType))||'';const steps=this.$target[0].querySelectorAll('.s_process_step:not(.o_snippet_desktop_invisible)');const nbBootstrapCols=12;let colsInRow=0;for(let i=0;i<steps.length-1;i++){const connectorEl=steps[i].querySelector('.s_process_step_connector');const stepMainElementRect=this._getStepMainElementRect(steps[i]);const nextStepMainElementRect=this._getStepMainElementRect(steps[i+1]);const stepSize=this._getClassSuffixedInteger(steps[i],'col-lg-');const nextStepSize=this._getClassSuffixedInteger(steps[i+1],'col-lg-');const stepOffset=this._getClassSuffixedInteger(steps[i],'offset-lg-');const nextStepOffset=this._getClassSuffixedInteger(steps[i+1],'offset-lg-');const stepPaddingTop=this._getClassSuffixedInteger(steps[i],'pt');const nextStepPaddingTop=this._getClassSuffixedInteger(steps[i+1],'pt');connectorEl.style.left=`calc(50% + ${stepMainElementRect.width / 2}px)`;connectorEl.style.height=`${stepMainElementRect.height}px`;connectorEl.style.width=`calc(${100 * (stepSize / 2 + nextStepOffset + nextStepSize / 2) / stepSize}% - ${stepMainElementRect.width / 2}px - ${nextStepMainElementRect.width / 2}px)`;const isTheLastColOfRow=nbBootstrapCols<colsInRow+stepSize+stepOffset+nextStepSize+nextStepOffset;const isNextStepTooLow=stepMainElementRect.height+stepPaddingTop<nextStepPaddingTop;connectorEl.classList.toggle('d-none',isTheLastColOfRow||isNextStepTooLow);colsInRow=isTheLastColOfRow?0:colsInRow+stepSize+stepOffset;connectorEl.style.display='block';const{height,width}=connectorEl.getBoundingClientRect();connectorEl.style.removeProperty('display');connectorEl.setAttribute('viewBox',`0 0 ${width} ${height}`);connectorEl.querySelector('path').setAttribute('d',this._getPath(type,width,height));}},_getClassSuffixedInteger(el,classNamePrefix){const className=[...el.classList].find(cl=>cl.startsWith(classNamePrefix));return className?parseInt(className.replace(classNamePrefix,'')):0;},_getStepMainElementRect(stepEl){const iconEl=stepEl.querySelector('i');if(iconEl){return iconEl.getBoundingClientRect();}
const contentEls=stepEl.querySelectorAll('.s_process_step_content > *');if(contentEls.length){const contentRects=[...contentEls].map(contentEl=>{const range=document.createRange();range.selectNodeContents(contentEl);return range.getBoundingClientRect();});return contentRects.reduce((previous,current)=>{return current.width>previous.width?current:previous;});}
return{};},_getStepColSize(stepEl){const classPrefix='col-lg-';const colClass=stepEl.className.split(' ').find(cl=>cl.startsWith(classPrefix));return parseInt(colClass.replace(classPrefix,''));},_getStepColPadding(stepEl){const classPrefix='offset-lg-';const paddingClass=stepEl.className.split(' ').find(cl=>cl.startsWith(classPrefix));return paddingClass?parseInt(paddingClass.replace(classPrefix,'')):0;},_getPath(type,width,height){const hHeight=height/2;switch(type){case's_process_steps_connector_line':{return`M 0 ${hHeight} L ${width} ${hHeight}`;}
case's_process_steps_connector_arrow':{return`M ${0.05 * width} ${hHeight} L ${0.95 * width - 6} ${hHeight}`;}
case's_process_steps_connector_curved_arrow':{return`M ${0.05 * width} ${hHeight * 1.2} Q ${width / 2} ${hHeight * 1.8}, ${0.95 * width - 6} ${hHeight * 1.2}`;}}
return'';},});return __exports;});;

/* /website/static/src/js/editor/wysiwyg.js */
odoo.define('website.wysiwyg',function(require){'use strict';var Wysiwyg=require('web_editor.wysiwyg');var snippetsEditor=require('website.snippet.editor');let socialMediaOptions=require('@website/snippets/s_social_media/options')[Symbol.for("default")];function toggleDropdown($toggles,show){return Promise.all(_.map($toggles,toggle=>{const $toggle=toggle.ownerDocument.defaultView.$(toggle);const shown=toggle.classList.contains('show');if(shown===show){return;}
const toShow=!shown;return new Promise(resolve=>{$toggle.parent().one(toShow?'shown.bs.dropdown':'hidden.bs.dropdown',()=>resolve());$toggle.dropdown(toShow?'show':'hide');});})).then(()=>$toggles);}
const WebsiteWysiwyg=Wysiwyg.extend({start:function(){this.options.toolbarHandler=$('#web_editor-top-edit');this.options.insertParagraphAfterColumns=false;const $editableWindow=this.$editable[0].ownerDocument.defaultView;var $dropdownMenuToggles=$editableWindow.$('.o_mega_menu_toggle, #top_menu_container .dropdown-toggle');$dropdownMenuToggles.removeAttr('data-bs-toggle').dropdown('dispose');$dropdownMenuToggles.on('click.wysiwyg_megamenu',ev=>{this.odooEditor.observerUnactive();var $toggle=$(ev.currentTarget);var dispose=($els=>$els.dropdown('dispose'));toggleDropdown($dropdownMenuToggles.not($toggle),false).then(dispose);toggleDropdown($toggle).then(dispose).then(()=>{if(!this.options.enableTranslation){this._toggleMegaMenu($toggle[0]);}}).then(()=>this.odooEditor.observerActive());});for(const el of this.$('.oe_structure')){if(!el.innerHTML.trim()){$(el).empty();}}
return this._super.apply(this,arguments);},_saveViewBlocks:async function(){await this._super.apply(this,arguments);if(this.isDirty()){return this._restoreMegaMenus();}},destroy:function(){socialMediaOptions.clearDbSocialValuesCache();this._restoreMegaMenus();this._super.apply(this,arguments);},_saveCoverProperties:function($elementToSave){var el=$elementToSave.closest('.o_record_cover_container')[0];if(!el){return;}
var resModel=el.dataset.resModel;var resID=parseInt(el.dataset.resId);if(!resModel||!resID){throw new Error('There should be a model and id associated to the cover');}
if(!('coverClass'in el.dataset)){return;}
this.__savedCovers=this.__savedCovers||{};this.__savedCovers[resModel]=this.__savedCovers[resModel]||[];if(this.__savedCovers[resModel].includes(resID)){return;}
this.__savedCovers[resModel].push(resID);var cssBgImage=$(el.querySelector('.o_record_cover_image')).css('background-image');var coverProps={'background-image':cssBgImage.replace(/"/g,'').replace(window.location.protocol+"//"+window.location.host,''),'background_color_class':el.dataset.bgColorClass,'background_color_style':el.dataset.bgColorStyle,'opacity':el.dataset.filterValue,'resize_class':el.dataset.coverClass,'text_align_class':el.dataset.textAlignClass,};return this._rpc({model:resModel,method:'write',args:[resID,{'cover_properties':JSON.stringify(coverProps)}],});},_rpc(options){let context;this.trigger_up('context_get',{callback:cxt=>context=cxt,});context=Object.assign(context,options.context);options.context=context;return this._super(options);},_createSnippetsMenuInstance(options={}){return new snippetsEditor.SnippetsMenu(this,Object.assign({wysiwyg:this,selectorEditableArea:'.o_editable',},options));},_insertSnippetMenu(){return this.snippetsMenu.appendTo(this.$el);},_saveElement:async function($el,context,withLang){var promises=[];await this._super.apply(this,arguments);if($el.data('oe-field')==='mega_menu_content'){var classes=_.without($el.attr('class').split(' '),'dropdown-menu','o_mega_menu','show');promises.push(this._rpc({model:'website.menu',method:'write',args:[[parseInt($el.data('oe-id'))],{'mega_menu_classes':classes.join(' '),},],}));}
var prom=this._saveCoverProperties($el);if(prom){promises.push(prom);}
return Promise.all(promises);},_restoreMegaMenus:function(){var $megaMenuToggles=this.$('.o_mega_menu_toggle');$megaMenuToggles.off('.wysiwyg_megamenu').attr('data-bs-toggle','dropdown').dropdown({});return toggleDropdown($megaMenuToggles,false);},_toggleMegaMenu:function(toggleEl){const megaMenuEl=toggleEl.parentElement.querySelector('.o_mega_menu');if(!megaMenuEl||!megaMenuEl.classList.contains('show')){return this.snippetsMenu.activateSnippet(false);}
megaMenuEl.classList.add('o_no_parent_editor');return this.snippetsMenu.activateSnippet($(megaMenuEl));},});snippetsEditor.SnippetsMenu.include({init:function(){this._super(...arguments);this._notActivableElementsSelector+=', .o_mega_menu_toggle';},start(){const _super=this._super(...arguments);if(this.options.enableTranslation){return _super;}
if(this.$body[0].ownerDocument!==this.ownerDocument){this.$body.on('click.snippets_menu','*',this._onClick);}
return _super;},destroy(){if(this.$body[0].ownerDocument!==this.ownerDocument){this.$body.off('.snippets_menu');}
return this._super(...arguments);},async cleanForSave(){return this._super(...arguments).then(()=>{for(const el of this.options.editable[0].querySelectorAll('.o_translation_without_style')){el.classList.remove('o_translation_without_style');if(el.dataset.oeTranslationSaveSha){el.dataset.oeTranslationInitialSha=el.dataset.oeTranslationSaveSha;delete el.dataset.oeTranslationSaveSha;}}});},_insertDropzone:function($hook){var $hookParent=$hook.parent();var $dropzone=this._super(...arguments);$dropzone.attr('data-editor-message-default',$hookParent.attr('data-editor-message-default'));$dropzone.attr('data-editor-message',$hookParent.attr('data-editor-message'));$dropzone.attr('data-editor-sub-message',$hookParent.attr('data-editor-sub-message'));return $dropzone;},});return WebsiteWysiwyg;});;

/* /website/static/src/js/editor/widget_link.js */
odoo.define('website.editor.link',function(require){'use strict';var weWidgets=require('wysiwyg.widgets');var wUtils=require('website.utils');weWidgets.LinkTools.include({custom_events:_.extend({},weWidgets.LinkTools.prototype.custom_events||{},{website_url_chosen:'_onAutocompleteClose',}),LINK_DEBOUNCE:1000,init:function(){this._super.apply(this,arguments);this._adaptPageAnchor=_.debounce(this._adaptPageAnchor,this.LINK_DEBOUNCE);},start:async function(){var def=await this._super.apply(this,arguments);const options={position:{collision:'flip flipfit',},classes:{"ui-autocomplete":'o_website_ui_autocomplete'},body:this.$editable[0].ownerDocument.body,};wUtils.autocompleteWithPages(this,this.$('input[name="url"]'),options);this._adaptPageAnchor();return def;},_adaptPageAnchor:function(){const urlInputValue=this.$('input[name="url"]').val();const $pageAnchor=this.$('.o_link_dialog_page_anchor');const isFromWebsite=urlInputValue[0]==='/';const $selectMenu=this.$('we-selection-items[name="link_anchor"]');if($selectMenu.data("anchor-for")!==urlInputValue){$pageAnchor.toggleClass('d-none',!isFromWebsite);$selectMenu.empty();const always=()=>$pageAnchor.find('we-toggler').text('\u00A0');wUtils.loadAnchors(urlInputValue,this.$editable[0].ownerDocument.body).then(anchors=>{for(const anchor of anchors){const $option=$('<we-button class="dropdown-item">');$option.text(anchor);$option.data('value',anchor);$selectMenu.append($option);}
always();}).guardedCatch(always);}
$selectMenu.data("anchor-for",urlInputValue);},_onAutocompleteClose:function(){this._onURLInput();},_onAnchorChange:function(ev){const anchorValue=$(ev.currentTarget).data('value');const $urlInput=this.$('[name="url"]');let urlInputValue=$urlInput.val();if(urlInputValue.indexOf('#')>-1){urlInputValue=urlInputValue.substr(0,urlInputValue.indexOf('#'));}
$urlInput.val(urlInputValue+anchorValue);},_onURLInput:function(){this._super.apply(this,arguments);this._adaptPageAnchor();},_onPickSelectOption(ev){if(ev.currentTarget.closest('[name="link_anchor"]')){this._onAnchorChange(ev);}
this._super(...arguments);},});});;

/* /website/static/src/js/widgets/link_popover_widget.js */
odoo.define('@website/js/widgets/link_popover_widget',async function(require){'use strict';let __exports={};const weWidgets=require('wysiwyg.widgets');const{_t}=require('web.core');weWidgets.LinkPopoverWidget.include({start(){if(this.target.closest('.o_mega_menu')){let timeoutID=undefined;this.$target.on('keydown.link_popover',()=>{this.$target.popover('hide');clearTimeout(timeoutID);timeoutID=setTimeout(()=>this.$target.popover('show'),1500);});}
return this._super(...arguments);},});const NavbarLinkPopoverWidget=weWidgets.LinkPopoverWidget.extend({events:_.extend({},weWidgets.LinkPopoverWidget.prototype.events,{'click .js_edit_menu':'_onEditMenuClick',}),async start(){const _super=this._super.bind(this);this.isWebsiteDesigner=await this._rpc({'model':'res.users','method':'has_group','args':['website.group_website_designer'],});const $removeLink=this.$('.o_we_remove_link');if(this.isWebsiteDesigner){const $anchor=$('<a/>',{href:'#',class:'ms-2 js_edit_menu',title:_t('Edit Menu'),'data-bs-placement':'top','data-bs-toggle':'tooltip',}).append($('<i/>',{class:'fa fa-sitemap'}));$removeLink.replaceWith($anchor);}else{this.$('.o_we_edit_link').remove();$removeLink.remove();}
return _super(...arguments);},_onEditLinkClick(ev){var self=this;var $menu=this.$target.find('[data-oe-id]');this.trigger_up('menu_dialog',{name:$menu.text(),url:$menu.parent().attr('href'),save:(name,url)=>{let websiteId;this.trigger_up('context_get',{callback:ctx=>websiteId=ctx['website_id'],});const data={id:$menu.data('oe-id'),name,url,};return this._rpc({model:'website.menu',method:'save',args:[websiteId,{'data':[data]}],}).then(function(){self.options.wysiwyg.odooEditor.observerUnactive();self.$target.attr('href',url);$menu.text(name);self.options.wysiwyg.odooEditor.observerActive();});},});this.popover.hide();},_onEditMenuClick(ev){const contentMenu=this.target.closest('[data-content_menu_id]');const rootID=contentMenu?parseInt(contentMenu.dataset.content_menu_id,10):undefined;this.trigger_up('action_demand',{actionName:'edit_menu',params:[rootID],});},});NavbarLinkPopoverWidget.createFor=weWidgets.LinkPopoverWidget.createFor;__exports[Symbol.for("default")]=NavbarLinkPopoverWidget;return __exports;});

                    /*******************************************
                    *  Templates                               *
                    *******************************************/

                    odoo.define('website.assets_wysiwyg.bundle.xml', function(require){
                        'use strict';
                        const { loadXML } = require('@web/core/assets');
                        const templates = `<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
<t t-name="website.gallery.slideshow">
        <div t-attf-id="#{id}" class="carousel slide" data-bs-ride="carousel" t-attf-data-bs-interval="#{interval}" style="margin: 0 12px;">
            <div class="carousel-inner" style="padding: 0;">
                 <t t-foreach="images" t-as="image">
                    <div t-attf-class="carousel-item #{image_index == index and 'active' or None}">
                        <img t-attf-class="#{attrClass || 'img img-fluid d-block'}" t-att-src="image.src" t-att-style="attrStyle" t-att-alt="image.alt" data-name="Image"/>
                    </div>
                 </t>
            </div>

            <ul class="carousel-indicators">
                <li class="o_indicators_left text-center d-none" aria-label="Previous" title="Previous">
                    <i class="fa fa-chevron-left"/>
                </li>
                <t t-foreach="images" t-as="image">
                    <li t-attf-data-bs-target="##{id}" t-att-data-bs-slide-to="image_index" t-att-class="image_index == index and 'active' or None" t-attf-style="background-image: url(#{image.src})"/>
                </t>
                <li class="o_indicators_right text-center d-none" aria-label="Next" title="Next">
                    <i class="fa fa-chevron-right"/>
                </li>
            </ul>

            <a class="carousel-control-prev o_we_no_overlay" t-attf-href="##{id}" data-bs-slide="prev" aria-label="Previous" title="Previous">
                <span class="fa fa-chevron-left fa-2x text-white"/>
                <span class="visually-hidden">Previous</span>
            </a>
            <a class="carousel-control-next o_we_no_overlay" t-attf-href="##{id}" data-bs-slide="next" aria-label="Next" title="Next">
                <span class="fa fa-chevron-right fa-2x text-white"/>
                <span class="visually-hidden">Next</span>
            </a>
        </div>
    </t>


    <t t-name="website.gallery.slideshow.lightbox">
        <div role="dialog" class="modal o_technical_modal fade s_gallery_lightbox p-0" aria-label="Image Gallery Dialog">
            <div class="modal-dialog m-0" role="Picture Gallery" t-attf-style="">
                <div class="modal-content bg-transparent modal-fullscreen">
                    <main class="modal-body o_slideshow bg-transparent">
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" style="position: absolute; right: 10px; top: 10px;"/>
                        <t t-call="website.gallery.slideshow"/>
                    </main>
                </div>
            </div>
        </div>
    </t>
<t t-name="website.s_countdown.end_message">
    <div class="s_countdown_end_message d-none">
        <div class="oe_structure">
            <section class="s_picture pt48 pb24" data-snippet="s_picture">
                <div class="container">
                    <h2 style="text-align: center;">Happy Odoo Anniversary!</h2>
                    <p style="text-align: center;">As promised, we will offer 4 free tickets to our next summit.<br/>Visit our Facebook page to know if you are one of the lucky winners.</p>
                    <p><br/></p>
                    <div class="row s_nb_column_fixed">
                        <div class="col-lg-12 pb24">
                            <figure class="figure">
                                <img src="/web/image/website.library_image_18" class="figure-img img-thumbnail mx-auto padding-large" style="width: 50%;" alt="Countdown is over - Firework"/>
                            </figure>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</t>

<t t-name="website.s_website_form_status_success">
        <span id="s_website_form_result" class="text-success ml8">
            <i class="fa fa-check mr4" role="img" aria-label="Success" title="Success"/>The form has been sent successfully.
        </span>
    </t>


    <t t-name="website.s_website_form_status_error">
        <span id="s_website_form_result" class="text-danger ml8">
            <i class="fa fa-close mr4" role="img" aria-label="Error" title="Error"/>
            <t t-esc="message"/>
        </span>
    </t>
<t t-extend="wysiwyg.widgets.link">
        <t t-jquery="#o_link_dialog_url_input" t-operation="after">
            <small class="form-text text-muted">Hint: Type '/' to search an existing page and '#' to link to an anchor.</small>
        </t>
    </t>

    <t t-extend="wysiwyg.widgets.linkTools">
        <t t-jquery="#url_row" t-operation="after">
            <div style="text-align: center" t-attf-class="#{widget.isButton ? ' d-none' : ''}">
                <small>
                    Type '<span class="highlighted-text">/</span>' to search a page.
                    '<span class="highlighted-text">#</span>' to link to an anchor.
                </small>
            </div>
            <we-row class="o_link_dialog_page_anchor d-none" t-attf-class="#{widget.isButton ? ' d-none' : ''}">
                <we-select class="o_we_user_value_widget o_we_sublevel_1">
                    <we-title>Page Anchor</we-title>
                    <div>
                        <div class="dropdown">
                            <button class="dropdown-toggle" data-bs-toggle="dropdown" title="" tabindex="-1" data-bs-original-title="Link Size" aria-expanded="false">
                                <we-toggler>
                                    Loading...
                                </we-toggler>
                            </button>
                            <we-selection-items name="link_anchor" class="dropdown-menu link-style">
                                <we-button class="dropdown-item o_anchors_loading">Loading...</we-button>
                            </we-selection-items>
                            <span class="o_we_dropdown_caret"/>
                        </div>
                    </div>
                </we-select>
            </we-row>
        </t>
    </t>

    <div t-name="website.dialog.anchorName">
        <div class="mb-3 row">
            <label class="col-form-label col-md-3" for="anchorName">Choose an anchor name</label>
            <div class="col-md-9">
                <input type="text" class="form-control o_input_anchor_name" id="anchorName" t-attf-value="#{currentAnchor}" placeholder="Anchor name"/>
                <div class="invalid-feedback">
                    <p class="d-none o_anchor_already_exists">The chosen name already exists</p>
                </div>
            </div>
        </div>
    </div>


    <div t-name="website.dialog.addGoogleFont">
        <div class="mb-3 row">
            <label class="col-form-label col-md-3" for="google_font_html">Google Font address</label>
            <div class="col-md-9">
                <textarea id="google_font_html" class="form-control o_input_google_font" placeholder="https://fonts.google.com/specimen/Roboto" style="height: 100px;"/>
                <span class="float-end text-muted">
                    Select one font on <a target="_blank" href="https://fonts.google.com">fonts.google.com</a> and copy paste the address of the font page here.
                </span>
            </div>
            <label class="col-form-label col-md-3" for="google_font_serve">Serve font from Google servers</label>
            <label class="o_switch col-form-label col-md-9" for="google_font_serve">
                <input type="checkbox" checked="checked" id="google_font_serve"/>
                <span/>
            </label>
        </div>
    </div>
    <t t-name="website.delete_google_font_btn">
        <i t-if="!local" role="button" class="text-info ms-2 fa fa-cloud" title="This font is hosted and served to your visitors by Google servers"/>
        <t t-set="delete_font_title">Delete this font</t>
        <i role="button" class="text-danger ms-2 fa fa-trash-o o_we_delete_google_font_btn" t-att-aria-label="delete_font_title" t-att-title="delete_font_title" t-att-data-local-font="local" t-att-data-font-index="index"/>
    </t>
    <t t-name="website.add_google_font_btn">
        <we-button href="#" class="o_we_add_google_font_btn" t-att-data-variable="variable">
            Add a Google Font
        </we-button>
    </t>

    <t t-name="website.color_combination_edition">
        <we-colorpicker string="Background" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-bg"/>
        <we-colorpicker string="Text" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-text"/>
        <we-collapse>
            <we-colorpicker string="Headings" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-headings"/>
            <we-colorpicker string="Headings 2" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-h2"/>
            <we-colorpicker string="Headings 3" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-h3"/>
            <we-colorpicker string="Headings 4" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-h4"/>
            <we-colorpicker string="Headings 5" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-h5"/>
            <we-colorpicker string="Headings 6" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-h6"/>
        </we-collapse>
        <we-colorpicker string="Links" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-link"/>
        <we-row string="Primary Buttons">
            <we-colorpicker title="Background" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-btn-primary"/>
            <we-colorpicker title="Border" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-btn-primary-border"/>
        </we-row>
        <we-row string="Secondary Buttons">
            <we-colorpicker title="Background" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-btn-secondary"/>
            <we-colorpicker title="Border" data-customize-website-color="null" t-attf-data-color="o-cc#{number}-btn-secondary-border"/>
        </we-row>
    </t>
    <div t-name="website.s_google_map_modal">
        <p>Use Google Map on your website (Contact Us page, snippets, etc).</p>
        <div class="row mb-0">
            <label class="col-sm-2 col-form-label" for="pin_address">API Key</label>
            <div class="col">
                <div class="input-group">
                    <div class="input-group-text"><i class="fa fa-key"/></div>
                    <input type="text" class="form-control" id="api_key_input" t-att-value="apiKey or ''" placeholder="BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"/>
                </div>
                <small id="api_key_help" class="text-danger">
                </small>
                <div class="small form-text text-muted">
                    Hint: How to use Google Map on your website (Contact Us page and as a snippet)
                    <br/>
                    <a target="_blank" href="https://console.developers.google.com/flows/enableapi?apiid=maps_backend,static_maps_backend&amp;keyType=CLIENT_SIDE&amp;reusekey=true">
                        <i class="fa fa-arrow-right"/>
                        Create a Google Project and Get a Key
                    </a>
                    <br/>
                    <a target="_blank" href="https://cloud.google.com/maps-platform/pricing">
                        <i class="fa fa-arrow-right"/>
                        Enable billing on your Google Project
                    </a>
                </div>
                <div class="alert alert-info mb-0 mt-3">
                    Make sure your settings are properly configured:
                    <ul class="mb-0">
                        <li>
                            Enable the right google map APIs in your google account
                            <ul>
                                <li>Maps Static API</li>
                                <li>Maps JavaScript API</li>
                                <li>Places API</li>
                            </ul>
                        </li>
                        <li>
                            Make sure billing is enabled
                        </li>
                        <li>
                            Make sure to wait if errors keep being shown: sometimes enabling an API allows to use it immediately but Google keeps triggering errors for a while
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>


    <div t-name="website.custom_code_dialog_content">
        <div class="mb-2" t-esc="contentText"/>
        <div class="o_ace_editor_container"/>
    </div>


    <t t-extend="web_editor.toolbar">
        <t t-jquery="#list .oe-toggle-checklist" t-operation="replace"/>
        <t t-jquery="#list" t-operation="append">
            <div title="Animate text" class="btn fa fa-play fa-fw o_we_animate_text"/>
        </t>
    </t>
<t t-name="website.s_website_form_end_message">
        <div class="s_website_form_end_message d-none">
            <div class="oe_structure">
                <section class="s_text_block pt64 pb64 o_colored_level o_cc o_cc2" data-snippet="s_text_block">
                    <div class="container">
                        <h2 class="text-center">
                            <span class="fa fa-check-circle"/>
                                Thank You For Your Feedback
                        </h2>
                        <p class="text-center">
                            Our team will message you back as soon as possible.<br/>
                            In the meantime we invite you to visit our <a href="/">website</a>.<br/>
                        </p>
                    </div>
                </section>
            </div>
        </div>
    </t>

    <t t-name="website.s_website_form_recaptcha_legal">
        <div class="col-12 s_website_form_recaptcha" data-name="Recaptcha Legal">
            <div t-attf-style="width: #{labelWidth or '200px'}" class="s_website_form_label"/>
            <div class="col-sm">
                <t t-call="google_recaptcha.recaptcha_legal_terms"/>
            </div>
        </div>
    </t>



    <t t-name="website.form_field">
        <div t-attf-class="s_website_form_field mb-3 #{field.formatInfo.col or 'col-12'} #{field.custom and 's_website_form_custom' or ''} #{(field.required and 's_website_form_required' or '') or (field.modelRequired and 's_website_form_model_required' or '')} #{field.hidden and 's_website_form_field_hidden' or ''} #{field.dnone and 's_website_form_dnone' or ''}" t-att-data-type="field.type" data-name="Field">
            <div t-if="field.formatInfo.labelPosition != 'none' and field.formatInfo.labelPosition != 'top'" class="row s_col_no_resize s_col_no_bgcolor">
                <label t-attf-class="#{!field.isCheck and 'col-form-label' or ''} col-sm-auto s_website_form_label #{field.formatInfo.labelPosition == 'right' and 'text-end' or ''}" t-attf-style="width: #{field.formatInfo.labelWidth or '200px'}" t-att-for="field.id">
                     <t t-call="website.form_label_content"/>
                </label>
                <div class="col-sm">
                    <t t-out="0"/>
                    <t t-call="website.form_field_description"/>
                </div>
            </div>
            <t t-else="">
                <label t-attf-class="s_website_form_label #{field.formatInfo.labelPosition == 'none' and 'd-none' or ''}" t-attf-style="width: #{field.formatInfo.labelWidth or '200px'}" t-att-for="field.id">
                     <t t-call="website.form_label_content"/>
                </label>
                <t t-out="0"/>
                <t t-call="website.form_field_description"/>
            </t>
        </div>
    </t>

    <t t-name="website.form_label_content">
        <t t-if="field.custom and !field.string" t-set="field.string" t-value="field.name"/>
        <span class="s_website_form_label_content" t-esc="field.string"/>
        <t t-if="field.required or field.modelRequired">
            <span class="s_website_form_mark" t-if="field.formatInfo.requiredMark" t-esc="' ' + field.formatInfo.mark"/>
        </t>
        <t t-else="">
            <span class="s_website_form_mark" t-if="field.formatInfo.optionalMark" t-esc="' ' + field.formatInfo.mark"/>
        </t>
    </t>

    <t t-name="website.form_field_description">


        <t t-set="default_description">
            <t t-if="field.description">
                Describe your field here.
            </t>
            <t t-elif="['email_cc', 'email_to'].includes(field.name)">
                Separate email addresses with a comma.
            </t>
        </t>
        <t t-set="default_description" t-value="default_description and default_description.trim()"/>
        <div t-if="default_description" class="s_website_form_field_description small form-text text-muted" contenteditable="true">
            <t t-esc="default_description"/>
        </div>
    </t>


    <t t-name="website.form_field_hidden">
        <t t-set="field.dnone" t-value="true"/>
        <t t-set="field.formatInfo" t-value="{}"/>
        <t t-call="website.form_field">
            <input type="hidden" class="form-control s_website_form_input" t-att-name="field.name" t-att-value="field.value" t-att-id="field.id"/>
        </t>
    </t>


    <t t-name="website.form_field_char">
        <t t-call="website.form_field">
            <input t-att-type="field.inputType || 'text'" class="form-control s_website_form_input" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-value="field.value" t-att-data-fill-with="field.fillWith" t-att-placeholder="field.placeholder" t-att-id="field.id"/>
        </t>
    </t>


    <t t-name="website.form_field_email">
        <t t-set="field.inputType" t-value="'email'"/>
        <t t-call="website.form_field_char"/>
    </t>


    <t t-name="website.form_field_tel">
        <t t-set="field.inputType" t-value="'tel'"/>
        <t t-call="website.form_field_char"/>
    </t>


    <t t-name="website.form_field_url">
        <t t-set="field.inputType" t-value="'url'"/>
        <t t-call="website.form_field_char"/>
    </t>


    <t t-name="website.form_field_text">
        <t t-call="website.form_field">
            <textarea class="form-control s_website_form_input" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-placeholder="field.placeholder" t-att-id="field.id" t-att-rows="field.rows || 3" t-esc="field.value"/>
        </t>
    </t>


    <t t-name="website.form_field_html">

        <t t-call="website.form_field_text"/>
    </t>


    <t t-name="website.form_field_integer">
        <t t-call="website.form_field">
            <input type="number" class="form-control s_website_form_input" t-att-name="field.name" step="1" t-att-required="field.required || field.modelRequired || None" t-att-value="field.value" t-att-placeholder="field.placeholder" t-att-id="field.id"/>
        </t>
    </t>


    <t t-name="website.form_field_float">
        <t t-call="website.form_field">
            <input type="number" class="form-control s_website_form_input" t-att-name="field.name" step="any" t-att-required="field.required || field.modelRequired || None" t-att-value="field.value" t-att-placeholder="field.placeholder" t-att-id="field.id"/>
        </t>
    </t>


    <t t-name="website.form_field_date">
        <t t-call="website.form_field">
            <t t-set="datepickerID" t-value="'datepicker' + Math.random().toString().substring(2)"/>
            <div class="s_website_form_date input-group date" t-att-id="datepickerID" data-target-input="nearest">
                <input type="text" class="form-control datetimepicker-input s_website_form_input" t-attf-data-target="##{datepickerID}" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-value="field.value" t-att-placeholder="field.placeholder" t-att-id="field.id"/>
                <div class="input-group-text" t-attf-data-target="##{datepickerID}" data-toggle="datetimepicker"><i class="fa fa-calendar"/></div>
            </div>
        </t>
    </t>


    <t t-name="website.form_field_datetime">
        <t t-call="website.form_field">
            <t t-set="datetimepickerID" t-value="'datetimepicker' + Math.random().toString().substring(2)"/>
            <div class="s_website_form_datetime input-group date" t-att-id="datetimepickerID" data-target-input="nearest">
                <input type="text" class="form-control datetimepicker-input s_website_form_input" t-attf-data-target="##{datetimepickerID}" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-value="field.value" t-att-placeholder="field.placeholder" t-att-id="field.id"/>
                <div class="input-group-text" t-attf-data-target="##{datetimepickerID}" data-toggle="datetimepicker"><i class="fa fa-calendar"/></div>
            </div>
        </t>
    </t>


    <t t-name="website.form_field_boolean">
        <t t-set="field.isCheck" t-value="true"/>
        <t t-call="website.form_field">
            <div class="form-check">
                <input type="checkbox" value="Yes" class="s_website_form_input form-check-input" t-att-name="field.name" t-att-checked="field.value and 'checked' or None" t-att-required="field.required || field.modelRequired || None" t-att-id="field.id"/>
            </div>
        </t>
    </t>


    <t t-name="website.form_field_selection">
        <t t-set="field.isCheck" t-value="true"/>
        <t t-call="website.form_field">
            <t t-if="!field.records">
                <input class="s_website_form_input" t-att-name="field.name" t-att-value="undefined" t-att-required="field.required || field.modelRequired || None" placeholder="No matching record !" disabled=""/>
            </t>
            <div class="row s_col_no_resize s_col_no_bgcolor s_website_form_multiple" t-att-data-name="field.name" t-att-data-display="field.formatInfo.multiPosition">
                <t t-foreach="field.records" t-as="record">
                    <t t-call="website.form_radio"/>
                </t>
            </div>
        </t>
    </t>


    <t t-name="website.form_radio">
        <t t-set="recordId" t-value="field.id + record_index"/>
        <div t-attf-class="radio col-12 #{field.formatInfo.multiPosition === 'horizontal' and 'col-lg-4 col-md-6' or ''}">
            <div class="form-check">
                <input type="radio" class="s_website_form_input form-check-input" t-att-id="recordId" t-att-name="field.name" t-att-checked="record.selected and 'checked' or None" t-att-value="record.id" t-att-required="field.required || field.modelRequired || None"/>
                <label class="form-check-label s_website_form_check_label" t-att-for="recordId">
                    <t t-esc="record.display_name"/>
                </label>
            </div>
        </div>
    </t>


    <t t-name="website.form_field_many2one">

        <t t-if="field.relation == 'ir.attachment'">
            <t t-call="website.form_field_binary"/>
        </t>

        <t t-if="field.relation != 'ir.attachment'">
            <t t-call="website.form_field">
                <select class="form-select s_website_form_input" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-id="field.id" style="display: none">
                    <t t-foreach="field.records" t-as="record">
                        <option t-esc="record.display_name" t-att-value="record.id" t-att-selected="record.selected and 'selected' or None"/>
                    </t>
                </select>
                <div id="editable_select" class="form-control s_website_form_input">
                    <t t-foreach="field.records" t-as="record">
                        <t t-set="noValueLabel">no value</t>
                        <div t-esc="record.display_name" t-attf-data-empty-value="&lt;#{noValueLabel}&gt;" t-att-id="record.id" t-attf-class="s_website_form_select_item #{record.selected and 'selected' or ''}"/>
                    </t>
                </div>
            </t>
        </t>
    </t>


    <t t-name="website.form_field_one2many">

        <t t-if="field.relation == 'ir.attachment'">
            <t t-call="website.form_field_binary">
                <t t-set="multiple" t-value="1"/>
            </t>
        </t>

        <t t-if="field.relation != 'ir.attachment'">
            <t t-set="field.isCheck" t-value="true"/>
            <t t-call="website.form_field">
                <t t-if="!field.records || field.records.length == 0">
                    <input class="form-control s_website_form_input" t-att-name="field.name" t-att-value="undefined" t-att-required="field.required || field.modelRequired || None" placeholder="No matching record !" disabled=""/>
                </t>
                <div class="row s_col_no_resize s_col_no_bgcolor s_website_form_multiple" t-att-data-name="field.name" t-att-data-display="field.formatInfo.multiPosition">
                    <t t-foreach="field.records" t-as="record">
                        <t t-call="website.form_checkbox"/>
                    </t>
                </div>
            </t>
        </t>
    </t>


    <t t-name="website.form_checkbox">
        <t t-set="recordId" t-value="field.id + record_index"/>
        <div t-attf-class="checkbox col-12 #{field.formatInfo.multiPosition === 'horizontal' and 'col-lg-4 col-md-6' or ''}">
            <div class="form-check">
                <input type="checkbox" class="s_website_form_input form-check-input" t-att-id="recordId" t-att-name="field.name" t-att-checked="record.selected and 'checked' or None" t-att-value="record.id" t-att-required="field.required || field.modelRequired || None"/>
                <label class="form-check-label s_website_form_check_label" t-att-for="recordId">
                    <t t-esc="record.display_name"/>
                </label>
            </div>
        </div>
    </t>


    <t t-name="website.form_field_many2many">
        <t t-call="website.form_field_one2many"/>
    </t>


    <t t-name="website.form_field_binary">
        <t t-set="field.isCheck" t-value="true"/>
        <t t-call="website.form_field">
            <input type="file" class="form-control s_website_form_input" t-att-name="field.name" t-att-required="field.required || field.modelRequired || None" t-att-multiple="multiple" t-att-id="field.id"/>
        </t>
    </t>


    <t t-name="website.form_field_monetary">
        <t t-call="website.form_field_float"/>
    </t>
<t t-name="website.cookies_bar.text_title">
        <h3 class="o_cookies_bar_text_title">
            Respecting your privacy is our priority.
        </h3>
    </t>
    <t t-name="website.cookies_bar.text_primary">
        <p class="o_cookies_bar_text_primary">
            Allow the use of cookies from this website on this browser?
        </p>
    </t>
    <t t-name="website.cookies_bar.text_secondary">
        <p class="o_cookies_bar_text_secondary">
            We use cookies to provide improved experience on this website. You can learn more about our cookies and how we use them in our <a href="/cookie-policy" class="o_cookies_bar_text_policy">Cookie Policy</a>.
        </p>
    </t>
    <t t-name="website.cookies_bar.text_button_all">
        <a href="#" id="cookies-consent-all" role="button" class="js_close_popup o_cookies_bar_accept_all o_cookies_bar_text_button btn btn-outline-primary rounded-circle mb-1 px-2 py-1">Allow all cookies</a>
    </t>
    <t t-name="website.cookies_bar.text_button_essential">
        <a href="#" id="cookies-consent-essential" role="button" class="js_close_popup o_cookies_bar_accept_essential o_cookies_bar_text_button_essential btn btn-outline-primary rounded-circle mt-1 mb-2 px-2 py-1">Only allow essential cookies</a>
    </t>
    <t t-name="website.cookies_bar.discrete">

        <section class="o_colored_level o_cc o_cc1">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 pt16">
                        <p>
                            <span class="pe-1">We use cookies to provide you a better user experience on this website.</span>
                            <a href="/cookie-policy" class="o_cookies_bar_text_policy btn btn-link btn-sm px-0">Cookie Policy</a>
                        </p>
                    </div>
                    <div class="col-lg-4 text-end pt16 pb16">
                        <a href="#" id="cookies-consent-essential" role="button" class="js_close_popup btn btn-outline-primary rounded-circle btn-sm px-2">Only essentials</a>
                        <a href="#" id="cookies-consent-all" role="button" class="js_close_popup btn btn-outline-primary rounded-circle btn-sm">I agree</a>
                    </div>
                </div>
            </div>
        </section>
    </t>
    <t t-name="website.cookies_bar.classic">
        <section class="o_colored_level o_cc o_cc1 pt32 pb16">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <t t-call="website.cookies_bar.text_title"/>
                        <t t-call="website.cookies_bar.text_primary"/>
                        <t t-call="website.cookies_bar.text_secondary"/>
                    </div>
                    <div class="col-lg-3 d-flex align-items-center">
                        <div class="row">
                            <div class="col-lg-12 d-flex align-items-center">
                                <t t-call="website.cookies_bar.text_button_all"/>
                            </div>
                            <div class="col-lg-12 d-flex align-items-center">
                                <t t-call="website.cookies_bar.text_button_essential"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </t>
    <t t-name="website.cookies_bar.popup">
        <section class="o_colored_level o_cc o_cc1 p-5">
            <div class="container text-center">
                <div class="row">
                    <div class="col-lg-12">
                        <img t-attf-src="/web/image/website/#{websiteId}/logo/250x250" class="img img-fluid mb-4" alt="Website Logo"/>
                        <t t-call="website.cookies_bar.text_title"/>
                        <t t-call="website.cookies_bar.text_primary"/>
                        <t t-call="website.cookies_bar.text_secondary"/>
                        <t t-call="website.cookies_bar.text_button_all"/>
                        <t t-call="website.cookies_bar.text_button_essential"/>
                    </div>
                </div>
            </div>
        </section>
    </t>

</templates>`;
                        return loadXML(templates);
                    });