{"version": 12, "sheets": [{"id": "Sheet1", "name": "Dashboard", "colNumber": 9, "rowNumber": 38, "rows": {"6": {"size": 40}}, "cols": {"0": {"size": 175}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 100}, "4": {"size": 50}, "5": {"size": 175}, "6": {"size": 100}, "7": {"size": 100}, "8": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Invoiced](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[\"&\",[\"state\",\"not in\",[\"draft\",\"cancel\"]],\"|\",[\"move_type\",\"=\",\"out_invoice\"],[\"move_type\",\"=\",\"out_refund\"]],\"context\":{\"group_by\":[\"invoice_date\"],\"graph_measure\":\"price_subtotal\",\"graph_mode\":\"line\",\"graph_groupbys\":[\"invoice_date:month\"]},\"modelName\":\"account.invoice.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Invoices Analysis\",\"positional\":true})", "border": 1}, "A19": {"style": 2, "content": "=_t(\"Cash\")", "border": 1}, "A20": {"style": 3, "content": "=_t(\"Cash received\")", "border": 2}, "A21": {"content": "=_t(\"Cash spent\")"}, "A22": {"style": 3, "content": "=_t(\"Cash surplus\")"}, "A23": {"content": "=_t(\"Closing bank balance\")"}, "A25": {"style": 2, "content": "=_t(\"Performance\")", "border": 1}, "A26": {"style": 3, "content": "=_t(\"Gross profit margin\")", "border": 2}, "A27": {"content": "=_t(\"Net profit margin\")"}, "A28": {"style": 3, "content": "=_t(\"Return on investments\")"}, "A29": {"content": "=_t(\"Working capital\")"}, "A30": {"style": 3, "content": "=_t(\"Financial independence\")"}, "A31": {"content": "=_t(\"Quick ratio\")"}, "A33": {"style": 2, "content": "=_t(\"Position\")", "border": 1}, "A34": {"style": 3, "content": "=_t(\"Average debtors days\")", "border": 2}, "A35": {"content": "=_t(\"Average creditors days\")"}, "A36": {"style": 3, "content": "=_t(\"Short term cash forecast\")"}, "A37": {"content": "=_t(\"Current assets to liabilities\")"}, "B1": {"style": 4}, "B2": {"style": 4}, "B3": {"style": 4}, "B4": {"style": 4}, "B5": {"style": 4}, "B6": {"style": 4}, "B7": {"style": 4, "border": 1}, "B9": {"style": 4}, "B10": {"style": 4}, "B11": {"style": 4}, "B12": {"style": 4}, "B13": {"style": 4}, "B14": {"style": 4}, "B15": {"style": 4}, "B16": {"style": 4}, "B17": {"style": 4}, "B18": {"style": 4}, "B19": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")", "border": 1}, "B20": {"style": 3, "content": "=Data!C11", "border": 2}, "B21": {"content": "=Data!C12"}, "B22": {"style": 3, "content": "=Data!C13"}, "B23": {"content": "=Data!C14"}, "B24": {"style": 4}, "B25": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")", "border": 1}, "B26": {"style": 3, "content": "=Data!C16", "border": 2}, "B27": {"content": "=Data!C17"}, "B28": {"style": 3, "content": "=Data!C18"}, "B29": {"content": "=Data!C19"}, "B30": {"style": 3, "content": "=Data!C20"}, "B31": {"content": "=Data!C21"}, "B33": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")", "border": 1}, "B34": {"style": 3, "format": 1, "content": "=Data!C23", "border": 2}, "B35": {"format": 1, "content": "=Data!C24"}, "B36": {"style": 3, "content": "=Data!C25"}, "B37": {"content": "=Data!C26"}, "C1": {"style": 4}, "C2": {"style": 4}, "C3": {"style": 4}, "C4": {"style": 4}, "C5": {"style": 4}, "C6": {"style": 4}, "C7": {"style": 4, "border": 1}, "C9": {"style": 4}, "C10": {"style": 4}, "C11": {"style": 4}, "C12": {"style": 4}, "C13": {"style": 4}, "C14": {"style": 4}, "C15": {"style": 4}, "C16": {"style": 4}, "C17": {"style": 4}, "C18": {"style": 4}, "C19": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")-1", "border": 1}, "C20": {"style": 3, "content": "=Data!D11", "border": 2}, "C21": {"content": "=Data!D12"}, "C22": {"style": 3, "content": "=Data!D13"}, "C23": {"content": "=Data!D14"}, "C24": {"style": 4}, "C25": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")-1", "border": 1}, "C26": {"style": 3, "content": "=Data!D16", "border": 2}, "C27": {"content": "=Data!D17"}, "C28": {"style": 3, "content": "=Data!D18"}, "C29": {"content": "=Data!D19"}, "C30": {"style": 3, "content": "=Data!D20"}, "C31": {"content": "=Data!D21"}, "C33": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")-1", "border": 1}, "C34": {"style": 3, "format": 1, "content": "=Data!D23", "border": 2}, "C35": {"format": 1, "content": "=Data!D24"}, "C36": {"style": 3, "content": "=Data!D25"}, "C37": {"content": "=Data!D26"}, "D1": {"style": 4}, "D2": {"style": 4}, "D3": {"style": 4}, "D4": {"style": 4}, "D5": {"style": 4}, "D6": {"style": 4}, "D7": {"style": 4, "border": 1}, "D9": {"style": 4}, "D10": {"style": 4}, "D11": {"style": 4}, "D12": {"style": 4}, "D13": {"style": 4}, "D14": {"style": 4}, "D15": {"style": 4}, "D16": {"style": 4}, "D17": {"style": 4}, "D18": {"style": 4}, "D19": {"style": 4, "content": "Δ", "border": 1}, "D20": {"style": 3, "format": 2, "content": "=iferror(B20/C20,0)", "border": 2}, "D21": {"format": 2, "content": "=iferror(B21/C21,0)"}, "D22": {"style": 3, "format": 2, "content": "=iferror(B22/C22,0)"}, "D23": {"format": 2, "content": "=iferror(B23/C23,0)"}, "D24": {"style": 4}, "D25": {"style": 4, "content": "Δ", "border": 1}, "D26": {"style": 3, "format": 2, "content": "=B26-C26", "border": 2}, "D27": {"format": 2, "content": "=B27-C27"}, "D28": {"style": 3, "format": 2, "content": "=B28-C28"}, "D29": {"content": "=B29-C29"}, "D30": {"style": 3, "content": "=B30-C30"}, "D31": {"content": "=B31-C31"}, "D33": {"style": 4, "content": "Δ", "border": 1}, "D34": {"style": 3, "format": 3, "content": "=iferror(B34/C34,0)", "border": 2}, "D35": {"format": 3, "content": "=iferror(B35/C35,0)"}, "D36": {"style": 3, "format": 3, "content": "=iferror(B36/C36,0)"}, "D37": {"format": 3, "content": "=iferror(B37/C37,0)"}, "F19": {"style": 2, "content": "=_t(\"Profitability\")", "border": 1}, "F20": {"style": 3, "content": "=_t(\"Income\")", "border": 2}, "F21": {"content": "=_t(\"Cost of revenue\")"}, "F22": {"style": 3, "content": "=_t(\"Gross profit\")"}, "F23": {"content": "=_t(\"Expenses\")"}, "F24": {"style": 3, "content": "=_t(\"Net profit\")"}, "F26": {"style": 2, "content": "=_t(\"Balance sheet\")", "border": 1}, "F27": {"style": 3, "content": "=_t(\"Receivable\")", "border": 2}, "F28": {"content": "=_t(\"Payables\")"}, "F29": {"style": 3, "content": "=_t(\"Net assets\")"}, "G19": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")", "border": 1}, "G20": {"style": 3, "content": "=Data!C28", "border": 2}, "G21": {"content": "=Data!C29"}, "G22": {"style": 3, "content": "=Data!C30"}, "G23": {"content": "=Data!C31"}, "G24": {"style": 3, "content": "=Data!C32"}, "G26": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")", "border": 1}, "G27": {"style": 3, "content": "=Data!C34", "border": 2}, "G28": {"content": "=Data!C35"}, "G29": {"style": 3, "content": "=Data!C36"}, "H19": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")-1", "border": 1}, "H20": {"style": 3, "content": "=Data!D28", "border": 2}, "H21": {"content": "=Data!D29"}, "H22": {"style": 3, "content": "=Data!D30"}, "H23": {"content": "=Data!D31"}, "H24": {"style": 3, "content": "=Data!D32"}, "H26": {"style": 4, "content": "=ODOO.FILTER.VALUE(\"Year\")-1", "border": 1}, "H27": {"style": 3, "content": "=Data!D34", "border": 2}, "H28": {"content": "=Data!D35"}, "H29": {"style": 3, "content": "=Data!D36"}, "I19": {"style": 4, "content": "Δ", "border": 1}, "I20": {"style": 3, "format": 2, "content": "=iferror(G20/H20,0)", "border": 2}, "I21": {"format": 2, "content": "=iferror(G21/H21,0)"}, "I22": {"style": 3, "format": 2, "content": "=iferror(G22/H22,0)"}, "I23": {"format": 2, "content": "=iferror(G23/H23,0)"}, "I24": {"style": 3, "format": 2, "content": "=iferror(G24/H24,0)"}, "I26": {"style": 4, "content": "Δ", "border": 1}, "I27": {"style": 3, "format": 2, "content": "=iferror(G27/H27,0)", "border": 2}, "I28": {"format": 2, "content": "=iferror(G28/H28,0)"}, "I29": {"style": 3, "format": 2, "content": "=iferror(G29/H29,0)"}, "A8": {"border": 2}, "B8": {"border": 2}, "C8": {"border": 2}, "D8": {"border": 2}, "E7": {"border": 1}, "E8": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "G7": {"border": 1}, "G8": {"border": 2}, "H7": {"border": 1}, "H8": {"border": 2}, "I7": {"border": 1}, "I8": {"border": 2}}, "conditionalFormats": [], "figures": [{"id": "151b0f60-aa89-457e-9456-9807ab779be6", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["invoice_date:month"], "measure": "price_subtotal", "order": null, "resModel": "account.invoice.report"}, "searchParams": {"comparison": null, "context": {"group_by": ["invoice_date"], "group_by_no_leaf": 1}, "domain": ["&", ["state", "not in", ["draft", "cancel"]], "|", ["move_type", "=", "out_invoice"], ["move_type", "=", "out_refund"]], "groupBy": ["invoice_date:month"], "orderBy": []}, "type": "odoo_line", "verticalAxisPosition": "left", "fieldMatching": {"7785b753-917c-4640-a937-4b6226222ca8": {"chain": "invoice_date", "type": "date", "offset": 0}}}}, {"id": "86aac7b2-f3a5-46e1-a097-916aa4b8f8d0", "x": 0, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Current income", "type": "scorecard", "baseline": "Data!C3", "baselineDescr": "last period", "keyValue": "Data!C2"}}, {"id": "3586cbc4-6ca3-46af-951f-94d4773f94dc", "x": 210, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Receivables", "type": "scorecard", "baselineDescr": "to receive", "keyValue": "Data!C4"}}, {"id": "57f18f9a-55b7-4b84-b927-480c4c56c026", "x": 420, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Current expense", "type": "scorecard", "baseline": "Data!C6", "baselineDescr": "last period", "keyValue": "Data!C5"}}, {"id": "8f1fb053-5700-4bb3-b93d-55ceb2083d31", "x": 630, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Payables", "type": "scorecard", "baselineDescr": "to pay", "keyValue": "Data!C7"}}], "filterTables": [], "areGridLinesVisible": true, "isVisible": true}, {"id": "08b80fa9-158a-44a6-ad6c-c29245a6e31a", "name": "Data", "colNumber": 31, "rowNumber": 149, "rows": {"40": {"size": 23}}, "cols": {"0": {"size": 239}, "1": {"size": 214}, "2": {"size": 234}, "3": {"size": 234}, "4": {"size": 234}, "5": {"size": 515}, "6": {"size": 101}}, "merges": ["A10:F10", "A15:F15", "A22:F22", "A27:F27", "A33:F33", "A37:F37", "A1:C1"], "cells": {"A1": {"style": 2, "content": "=_t(\"KPIS\")"}, "A2": {"content": "=_t(\"Current income\")"}, "A3": {"content": "=_t(\"Previous income\")"}, "A4": {"content": "=_t(\"Receivables\")"}, "A5": {"content": "=_t(\"Current expense\")"}, "A6": {"content": "=_t(\"Previous expense\")"}, "A7": {"content": "=_t(\"Payables\")"}, "A9": {"style": 5, "content": "=_t(\"Year\")"}, "A10": {"style": 2, "content": "=_t(\"Cash\")"}, "A11": {"content": "=_t(\"Cash received\")"}, "A12": {"content": "=_t(\"Cash spent\")"}, "A13": {"content": "=_t(\"Cash surplus\")"}, "A14": {"content": "=_t(\"Closing bank balance\")"}, "A15": {"style": 2, "content": "=_t(\"Performance\")"}, "A16": {"content": "=_t(\"Gross profit margin\")"}, "A17": {"content": "=_t(\"Net profit margin\")"}, "A18": {"content": "=_t(\"Return on investments\")"}, "A19": {"content": "=_t(\"Working capital\")"}, "A20": {"content": "=_t(\"Financial independence\")"}, "A21": {"content": "=_t(\"Quick ratio\")"}, "A22": {"style": 2, "content": "=_t(\"Position\")"}, "A23": {"content": "=_t(\"Average debtors days\")"}, "A24": {"content": "=_t(\"Average creditors days\")"}, "A25": {"content": "=_t(\"Short term cash forecast\")"}, "A26": {"content": "=_t(\"Current assets to liabilities\")"}, "A27": {"style": 2, "content": "=_t(\"Profitability\")"}, "A28": {"content": "=_t(\"Income\")"}, "A29": {"content": "=_t(\"Cost of revenue\")"}, "A30": {"content": "=_t(\"Gross profit\")"}, "A31": {"content": "=_t(\"Expenses\")"}, "A32": {"content": "=_t(\"Net profit\")"}, "A33": {"style": 2, "content": "=_t(\"Balance sheet\")"}, "A34": {"content": "=_t(\"Receivable\")"}, "A35": {"content": "=_t(\"Payables\")"}, "A36": {"content": "=_t(\"Net assets\")"}, "A37": {"style": 2, "content": "=_t(\"Other variables\")"}, "A38": {"style": 6, "content": "OPINC"}, "A39": {"style": 6, "content": "OIN"}, "A40": {"style": 6, "content": "TA"}, "A41": {"style": 6, "content": "CA"}, "A42": {"style": 6, "content": "BA"}, "A43": {"style": 6, "content": "REC"}, "A44": {"style": 6, "content": "CAS"}, "A45": {"style": 6, "content": "PRE"}, "A46": {"style": 6, "content": "FA"}, "A47": {"style": 6, "content": "PNCA"}, "A48": {"style": 6, "content": "L"}, "A49": {"style": 6, "content": "CL"}, "A50": {"style": 6, "content": "CL1"}, "A51": {"style": 6, "content": "CL2"}, "A52": {"style": 6, "content": "NL"}, "A53": {"style": 6, "content": "DEP"}, "A54": {"content": "EQ"}, "A55": {"content": "UNALLOCATED_EARNINGS"}, "A56": {"content": "CURR_YEAR_EARNINGS"}, "A57": {"content": "CURR_YEAR_EARNINGS_PNL"}, "A58": {"content": "CURR_YEAR_EARNINGS_ALLOC"}, "A59": {"content": "PREV_YEAR_EARNINGS"}, "A60": {"content": "RETAINED_EARNINGS"}, "B1": {"style": 2}, "B2": {"style": 7, "content": "=C38"}, "B3": {"style": 7, "content": "=D38"}, "B4": {"style": 7, "content": "=C34"}, "B5": {"style": 7, "content": "=C31"}, "B6": {"style": 7, "content": "=D31"}, "B7": {"content": "=C35"}, "B9": {"style": 5, "content": "=_t(\"Code\")"}, "B10": {"style": 2}, "B11": {"content": "CR"}, "B12": {"content": "CS"}, "B13": {"content": "CR + CS"}, "B15": {"style": 2}, "B16": {"style": 6, "content": "GRP/OPINC"}, "B17": {"style": 6, "content": "NEP/INC"}, "B18": {"style": 6, "content": "NEP/TA"}, "B19": {"style": 6, "content": "CA-CL"}, "B20": {"style": 8, "content": "EQ/CL"}, "B21": {"content": "(BA + REC +CA)/CL"}, "B22": {"style": 2}, "B23": {"style": 6, "content": "DEB/OPINC*365"}, "B24": {"content": "CRE/OPINC*365"}, "B25": {"content": "DEB-CRE"}, "B26": {"style": 6, "content": "CA/CL"}, "B27": {"style": 2}, "B28": {"style": 6, "content": "INC = OPINC + OIN"}, "B29": {"style": 6, "content": "COS"}, "B30": {"style": 6, "content": "GRP = OPINC - COS"}, "B31": {"style": 6, "content": "EXP"}, "B32": {"style": 6, "content": "NEP = OPINC + OIN - COS - EXP - DEP"}, "B33": {"style": 2}, "B34": {"style": 6, "content": "DEB"}, "B35": {"content": "CRE"}, "B36": {"style": 6, "content": "NA = TA - L"}, "B37": {"style": 2}, "B38": {"style": 6}, "B39": {"style": 6}, "B40": {"style": 6, "content": "CA + FA + PNCA"}, "B41": {"style": 6, "content": "BA + REC + CAS + PRE"}, "B42": {"style": 6}, "B43": {"style": 6}, "B44": {"style": 6}, "B45": {"style": 6}, "B46": {"style": 6}, "B47": {"style": 6}, "B48": {"style": 6, "content": "CL + NL"}, "B49": {"style": 6, "content": "CL1 + CL2"}, "B50": {"style": 6}, "B51": {"style": 6}, "B52": {"style": 6}, "B53": {"style": 2}, "B54": {"content": "UNALLOCATED EARNINGS + RETAINED EARNINGS"}, "B55": {"content": "CURR_YEAR_EARNINGS + PREV_YEAR_EARNINGS"}, "B56": {"content": "CURR_YEAR_EARNINGS_PNL + CURR_YEAR_EARNINGS_ALLOC"}, "C1": {"style": 2}, "C2": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "C3": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "C4": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "C5": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B5)"}, "C6": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B6)"}, "C7": {"style": 3, "content": "=FORMAT.LARGE.NUMBER(B7)"}, "C9": {"style": 5, "content": "=ODOO.FILTER.VALUE(\"Year\")"}, "C10": {"style": 2}, "C11": {"content": "=ODOO.DEBIT($F11,C$9)-ODOO.DEBIT($F11,C$9-1)"}, "C12": {"content": "=-ODOO.CREDIT($F12,C$9)-ODOO.CREDIT($F12,C$9-1)"}, "C13": {"content": "=C11+C12"}, "C14": {"content": "=ODOO.BALANCE($F14,C$9)"}, "C15": {"style": 2}, "C16": {"format": 3, "content": "=iferror(C30/C38,0)"}, "C17": {"format": 3, "content": "=iferror(C32/C28,0)"}, "C18": {"format": 3, "content": "=iferror(C32/C40,0)"}, "C19": {"content": "=C41-C49"}, "C20": {"style": 8, "format": 1, "content": "=iferror(C54/C49,0)"}, "C21": {"style": 8, "format": 1, "content": "=iferror((C42+C43+C41)/C49,0)"}, "C22": {"style": 2}, "C23": {"format": 4, "content": "=iferror(C34/C38*365,0)"}, "C24": {"format": 4, "content": "=iferror(C35/C38*365,0)"}, "C25": {"content": "=C34-C35"}, "C26": {"format": 4, "content": "=iferror(C41/C49,0)"}, "C27": {"style": 2}, "C28": {"content": "=C38+C39"}, "C29": {"content": "=ODOO.BALANCE($F29,C$9)"}, "C30": {"content": "=C38-C29"}, "C31": {"content": "=ODOO.BALANCE($F31,C$9)"}, "C32": {"content": "=C38+C39-C29-C31-C53"}, "C33": {"style": 2}, "C34": {"content": "=ODOO.BALANCE($F34,C$9)"}, "C35": {"content": "=-ODOO.BALANCE($F35,C$9)"}, "C36": {"content": "=C40-C48"}, "C37": {"style": 2}, "C38": {"content": "=-ODOO.BALANCE($F38,C$9)"}, "C39": {"content": "=-ODOO.BALANCE($F39,C$9)"}, "C40": {"style": 6, "content": "=C41+C46+C47"}, "C41": {"style": 6, "content": "=C42+C43+C44+C45"}, "C42": {"style": 6, "content": "=ODOO.BALANCE($F42,C$9)"}, "C43": {"style": 6, "content": "=ODOO.BALANCE($F43,C$9)"}, "C44": {"style": 6, "content": "=ODOO.BALANCE($F44,C$9)"}, "C45": {"style": 6, "content": "=ODOO.BALANCE($F45,C$9)"}, "C46": {"style": 6, "content": "=ODOO.BALANCE($F46,C$9)"}, "C47": {"style": 6, "content": "=ODOO.BALANCE($F47,C$9)"}, "C48": {"style": 6, "content": "=C49+C52"}, "C49": {"style": 6, "content": "=C50+C51"}, "C50": {"style": 6, "content": "=-ODOO.BALANCE($F50,C$9)"}, "C51": {"style": 6, "content": "=-ODOO.BALANCE($F51,C$9)"}, "C52": {"style": 6, "content": "=-ODOO.BALANCE($F52,C$9)"}, "C53": {"style": 6, "content": "=ODOO.BALANCE($F53,C$9)"}, "C54": {"content": "=C55+C60"}, "C55": {"content": "=C56+C59"}, "C56": {"content": "=C57+C58"}, "C57": {"content": "=-ODOO.BALANCE($F57,C$9)"}, "C58": {"content": "=-ODOO.BALANCE($F58,C$9)+ODOO.BALANCE($F58,C$9-1)"}, "C59": {"content": "=D55"}, "C60": {"content": "=-ODOO.BALANCE($F60,C$9)"}, "D9": {"style": 9, "content": "=C9-1"}, "D10": {"style": 2}, "D11": {"content": "=ODOO.DEBIT($F11,D$9)-ODOO.DEBIT($F11,D$9-1)"}, "D12": {"content": "=-ODOO.CREDIT($F12,D$9)-ODOO.CREDIT($F12,D$9-1)"}, "D13": {"content": "=D11+D12"}, "D14": {"content": "=ODOO.BALANCE($F14,D$9)"}, "D15": {"style": 2}, "D16": {"format": 3, "content": "=iferror(D30/D38,0)"}, "D17": {"format": 3, "content": "=iferror(D32/D28,0)"}, "D18": {"format": 3, "content": "=iferror(D32/D40,0)"}, "D19": {"content": "=D41-D49"}, "D20": {"style": 8, "format": 1, "content": "=iferror(D54/D49,0)"}, "D21": {"style": 8, "format": 1, "content": "=iferror((D42+D43+D41)/D49,0)"}, "D22": {"style": 2}, "D23": {"format": 4, "content": "=iferror(D34/D38*365,0)"}, "D24": {"format": 4, "content": "=iferror(D35/D38*365,0)"}, "D25": {"content": "=D34-D35"}, "D26": {"format": 4, "content": "=iferror(D41/D49,0)"}, "D27": {"style": 2}, "D28": {"content": "=D38+D39"}, "D29": {"content": "=ODOO.BALANCE($F29,D$9)"}, "D30": {"content": "=D38-D29"}, "D31": {"content": "=ODOO.BALANCE($F31,D$9)"}, "D32": {"content": "=D38+D39-D29-D31-D53"}, "D33": {"style": 2}, "D34": {"content": "=ODOO.BALANCE($F34,D$9)"}, "D35": {"content": "=-ODOO.BALANCE($F35,D$9)"}, "D36": {"content": "=D40-D48"}, "D37": {"style": 2}, "D38": {"style": 6, "content": "=-ODOO.BALANCE($F38,D$9)"}, "D39": {"style": 6, "content": "=-ODOO.BALANCE($F39,D$9)"}, "D40": {"style": 6, "content": "=D41+D46+D47"}, "D41": {"style": 6, "content": "=D42+D43+D44+D45"}, "D42": {"style": 6, "content": "=ODOO.BALANCE($F42,D$9)"}, "D43": {"style": 6, "content": "=ODOO.BALANCE($F43,D$9)"}, "D44": {"style": 6, "content": "=ODOO.BALANCE($F44,D$9)"}, "D45": {"style": 6, "content": "=ODOO.BALANCE($F45,D$9)"}, "D46": {"style": 6, "content": "=ODOO.BALANCE($F46,D$9)"}, "D47": {"style": 6, "content": "=ODOO.BALANCE($F47,D$9)"}, "D48": {"style": 6, "content": "=D49+D52"}, "D49": {"style": 6, "content": "=D50+D51"}, "D50": {"style": 6, "content": "=-ODOO.BALANCE($F50,D$9)"}, "D51": {"style": 6, "content": "=-ODOO.BALANCE($F51,D$9)"}, "D52": {"style": 6, "content": "=-ODOO.BALANCE($F52,D$9)"}, "D53": {"style": 6, "content": "=ODOO.BALANCE($F53,D$9)"}, "D54": {"content": "=D55+D60"}, "D55": {"content": "=D56+D59"}, "D56": {"content": "=D57+D58"}, "D57": {"content": "=-ODOO.BALANCE($F57,D$9)"}, "D58": {"content": "=-ODOO.BALANCE($F58,D$9)+ODOO.BALANCE($F58,D$9-1)"}, "D59": {"content": "=E55"}, "D60": {"content": "=-ODOO.BALANCE($F60,D$9)"}, "E9": {"style": 9, "content": "=D9-1"}, "E10": {"style": 2}, "E11": {"content": "=ODOO.DEBIT($F11,E$9)-ODOO.DEBIT($F11,E$9-1)"}, "E12": {"content": "=-ODOO.CREDIT($F12,E$9)-ODOO.CREDIT($F12,E$9-1)"}, "E13": {"content": "=E11+E12"}, "E14": {"content": "=ODOO.BALANCE($F14,E$9)"}, "E15": {"style": 2}, "E16": {"format": 3, "content": "=iferror(E30/E38,0)"}, "E17": {"format": 3, "content": "=iferror(E32/E28,0)"}, "E18": {"format": 3, "content": "=iferror(E32/E40,0)"}, "E19": {"content": "=E41-E49"}, "E20": {"style": 8, "format": 1, "content": "=iferror(E54/E49,0)"}, "E21": {"style": 8, "format": 1, "content": "=iferror((E42+E43+E41)/E49,0)"}, "E22": {"style": 2}, "E23": {"format": 4, "content": "=iferror(E34/E38*365,0)"}, "E24": {"format": 4, "content": "=iferror(E35/E38*365,0)"}, "E25": {"content": "=E34-E35"}, "E26": {"format": 4, "content": "=iferror(E41/E49,0)"}, "E27": {"style": 2}, "E28": {"content": "=E38+E39"}, "E29": {"content": "=ODOO.BALANCE($F29,E$9)"}, "E30": {"content": "=E38-E29"}, "E31": {"content": "=ODOO.BALANCE($F31,E$9)"}, "E32": {"content": "=E38+E39-E29-E31-E53"}, "E33": {"style": 2}, "E34": {"content": "=ODOO.BALANCE($F34,E$9)"}, "E35": {"content": "=-ODOO.BALANCE($F35,E$9)"}, "E36": {"content": "=E40-E48"}, "E37": {"style": 2}, "E38": {"style": 6, "content": "=-ODOO.BALANCE($F38,E$9)"}, "E39": {"style": 6, "content": "=-ODOO.BALANCE($F39,E$9)"}, "E40": {"style": 6, "content": "=E41+E46+E47"}, "E41": {"style": 6, "content": "=E42+E43+E44+E45"}, "E42": {"style": 6, "content": "=ODOO.BALANCE($F42,E$9)"}, "E43": {"style": 6, "content": "=ODOO.BALANCE($F43,E$9)"}, "E44": {"style": 6, "content": "=ODOO.BALANCE($F44,E$9)"}, "E45": {"style": 6, "content": "=ODOO.BALANCE($F45,E$9)"}, "E46": {"style": 6, "content": "=ODOO.BALANCE($F46,E$9)"}, "E47": {"style": 6, "content": "=ODOO.BALANCE($F47,E$9)"}, "E48": {"style": 6, "content": "=E49+E52"}, "E49": {"style": 6, "content": "=E50+E51"}, "E50": {"style": 6, "content": "=-ODOO.BALANCE($F50,E$9)"}, "E51": {"style": 6, "content": "=-ODOO.BALANCE($F51,E$9)"}, "E52": {"style": 6, "content": "=-ODOO.BALANCE($F52,E$9)"}, "E53": {"style": 6, "content": "=ODOO.BALANCE($F53,E$9)"}, "E54": {"content": "=E55+E60"}, "E55": {"content": "=E56+E59"}, "E56": {"content": "=E57+E58"}, "E57": {"content": "=-ODOO.BALANCE($F57,E$9)"}, "E58": {"content": "=-ODOO.BALANCE($F58,E$9)+ODOO.BALANCE($F58,E$9-1)"}, "E59": {"content": "=-ODOO.BALANCE($F58,E$9-1)-ODOO.BALANCE($F59,E9, -1)-ODOO.BALANCE($F59,E9, -2)-ODOO.BALANCE($F59,E9, -3)-ODOO.BALANCE($F59,E9, -4)-ODOO.BALANCE($F59,E9, -5)"}, "E60": {"content": "=-ODOO.BALANCE($F60,E$9)"}, "F9": {"style": 5, "content": "=_t(\"Accounts\")"}, "F10": {"style": 2}, "F11": {"content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"asset_cash\"),\", \",ODOO.ACCOUNT.GROUP(\"liability_credit_card\"))"}, "F12": {"content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"asset_cash\"),\", \",ODOO.ACCOUNT.GROUP(\"liability_credit_card\"))"}, "F14": {"content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"asset_cash\"),\", \",ODOO.ACCOUNT.GROUP(\"liability_credit_card\"))"}, "F15": {"style": 2}, "F16": {"style": 10}, "F17": {"style": 10}, "F18": {"style": 10}, "F19": {"style": 10}, "F20": {"style": 10}, "F21": {"style": 10}, "F22": {"style": 2}, "F23": {"style": 10}, "F24": {"style": 10}, "F25": {"style": 10}, "F26": {"style": 10}, "F27": {"style": 2}, "F28": {"style": 10}, "F29": {"content": "=ODOO.ACCOUNT.GROUP(\"expense_direct_cost\")"}, "F30": {"style": 10}, "F31": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"expense\")"}, "F32": {"style": 10}, "F33": {"style": 2}, "F34": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_receivable\")"}, "F35": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"liability_payable\")"}, "F36": {"style": 10}, "F37": {"style": 2}, "F38": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"income\")"}, "F39": {"content": "=ODOO.ACCOUNT.GROUP(\"income_other\")"}, "F40": {"style": 10}, "F41": {"style": 10}, "F42": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_cash\")"}, "F43": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_receivable\")"}, "F44": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_current\")"}, "F45": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_prepayments\")"}, "F46": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_fixed\")"}, "F47": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"asset_non_current\")"}, "F48": {"style": 10}, "F49": {"style": 10}, "F50": {"style": 10, "content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"liability_current\"),\", \",ODOO.ACCOUNT.GROUP(\"liability_credit_card\"))"}, "F51": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"liability_payable\")"}, "F52": {"style": 10, "content": "=ODOO.ACCOUNT.GROUP(\"liability_non_current\")"}, "F53": {"content": "=ODOO.ACCOUNT.GROUP(\"expense_depreciation\")"}, "F57": {"content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"income\"),\",\",ODOO.ACCOUNT.GROUP(\"income_other\"),\",\",ODOO.ACCOUNT.GROUP(\"expense_direct_cost\"),\",\",ODOO.ACCOUNT.GROUP(\"expense\"),\",\",ODOO.ACCOUNT.GROUP(\"expense_depreciation\"))"}, "F58": {"content": "=ODOO.ACCOUNT.GROUP(\"equity_unaffected\")"}, "F59": {"content": "=CONCATENATE(ODOO.ACCOUNT.GROUP(\"income\"),\",\",ODOO.ACCOUNT.GROUP(\"income_other\"),\",\",ODOO.ACCOUNT.GROUP(\"expense_direct_cost\"),\",\",ODOO.ACCOUNT.GROUP(\"expense\"),\",\",ODOO.ACCOUNT.GROUP(\"expense_depreciation\"))"}, "F60": {"content": "=ODOO.ACCOUNT.GROUP(\"equity\")"}, "G16": {"style": 6}, "G17": {"style": 10}, "G18": {"style": 10}, "G19": {"style": 10}, "G20": {"style": 10}, "G21": {"style": 10}, "G22": {"style": 10}, "G23": {"style": 10}, "G24": {"style": 10}, "G25": {"style": 10}, "G26": {"style": 10}, "G27": {"style": 10}, "G28": {"style": 10}, "G29": {"style": 10}, "G30": {"style": 10}, "G31": {"style": 10}, "G32": {"style": 10}, "G33": {"style": 10}, "G34": {"style": 10}, "G35": {"style": 10}, "G36": {"style": 10}, "G37": {"style": 10}, "G38": {"style": 10}, "G39": {"style": 10}, "G40": {"style": 10}, "G41": {"style": 10}, "G42": {"style": 10}, "G43": {"style": 10}, "G44": {"style": 10}, "G45": {"style": 10}, "G46": {"style": 10}, "G47": {"style": 10}, "G48": {"style": 10}, "G49": {"style": 10}, "G50": {"style": 10}, "G51": {"style": 10}, "G52": {"style": 10}, "G53": {"style": 11}, "G54": {"style": 11}, "H8": {"style": 12}, "H9": {"style": 12}, "H10": {"style": 12}, "H11": {"style": 12}, "H12": {"style": 12}, "H13": {"style": 12}, "H14": {"style": 12}, "H15": {"style": 12}, "H16": {"style": 13}, "H17": {"style": 6}, "H18": {"style": 6}, "H19": {"style": 6}, "H20": {"style": 6}, "H21": {"style": 6}, "H22": {"style": 6}, "H23": {"style": 6}, "H24": {"style": 6}, "H25": {"style": 6}, "H26": {"style": 6}, "H27": {"style": 6}, "H28": {"style": 6}, "H29": {"style": 6}, "H30": {"style": 6}, "H31": {"style": 6}, "H32": {"style": 6}, "H33": {"style": 6}, "H34": {"style": 6}, "H35": {"style": 6}, "H36": {"style": 6}, "H37": {"style": 6}, "H38": {"style": 6}, "H39": {"style": 6}, "H40": {"style": 6}, "H41": {"style": 6}, "H42": {"style": 6}, "H43": {"style": 6}, "H44": {"style": 6}, "H45": {"style": 6}, "H46": {"style": 6}, "H47": {"style": 6}, "H48": {"style": 6}, "H49": {"style": 6}, "H50": {"style": 6}, "H51": {"style": 6}, "H52": {"style": 6}, "H53": {"style": 2}, "H54": {"style": 2}, "I8": {"style": 12}, "I9": {"style": 12}, "I10": {"style": 12}, "I11": {"style": 12}, "I12": {"style": 12}, "I13": {"style": 12}, "I14": {"style": 12}, "I15": {"style": 12}, "I16": {"style": 12}, "I17": {"style": 2}, "I18": {"style": 2}, "I19": {"style": 2}, "I20": {"style": 2}, "I21": {"style": 2}, "I22": {"style": 2}, "I23": {"style": 2}, "I24": {"style": 2}, "I25": {"style": 2}, "I26": {"style": 2}, "I27": {"style": 2}, "I28": {"style": 2}, "I29": {"style": 2}, "I30": {"style": 2}, "I31": {"style": 2}, "I32": {"style": 2}, "I33": {"style": 2}, "I34": {"style": 2}, "I35": {"style": 2}, "I36": {"style": 2}, "I37": {"style": 2}, "I38": {"style": 2}, "I39": {"style": 2}, "I40": {"style": 2}, "I41": {"style": 2}, "I42": {"style": 2}, "I43": {"style": 2}, "I44": {"style": 2}, "I45": {"style": 2}, "I46": {"style": 2}, "I47": {"style": 2}, "I48": {"style": 2}, "I49": {"style": 2}, "I50": {"style": 2}, "I51": {"style": 2}, "I52": {"style": 2}, "I53": {"style": 2}, "I54": {"style": 2}, "J8": {"style": 12}, "J9": {"style": 12}, "J10": {"style": 12}, "J11": {"style": 12}, "J12": {"style": 12}, "J13": {"style": 12}, "J14": {"style": 12}, "J15": {"style": 12}, "J16": {"style": 12}, "J17": {"style": 2}, "J18": {"style": 2}, "J19": {"style": 2}, "J20": {"style": 2}, "J21": {"style": 2}, "J22": {"style": 2}, "J23": {"style": 2}, "J24": {"style": 2}, "J25": {"style": 2}, "J26": {"style": 2}, "J27": {"style": 2}, "J28": {"style": 2}, "J29": {"style": 2}, "J30": {"style": 2}, "J31": {"style": 2}, "J32": {"style": 2}, "J33": {"style": 2}, "J34": {"style": 2}, "J35": {"style": 2}, "J36": {"style": 2}, "J37": {"style": 2}, "J38": {"style": 2}, "J39": {"style": 2}, "J40": {"style": 2}, "J41": {"style": 2}, "J42": {"style": 2}, "J43": {"style": 2}, "J44": {"style": 2}, "J45": {"style": 2}, "J46": {"style": 2}, "J47": {"style": 2}, "J48": {"style": 2}, "J49": {"style": 2}, "J50": {"style": 2}, "J51": {"style": 2}, "J52": {"style": 2}, "J53": {"style": 2}, "J54": {"style": 2}, "K8": {"style": 12}, "K9": {"style": 12}, "K10": {"style": 12}, "K11": {"style": 12}, "K12": {"style": 12}, "K13": {"style": 12}, "K14": {"style": 12}, "K15": {"style": 12}, "K16": {"style": 12}, "K17": {"style": 2}, "K18": {"style": 2}, "K19": {"style": 2}, "K20": {"style": 2}, "K21": {"style": 2}, "K22": {"style": 2}, "K23": {"style": 2}, "K24": {"style": 2}, "K25": {"style": 2}, "K26": {"style": 2}, "K27": {"style": 2}, "K28": {"style": 2}, "K29": {"style": 2}, "K30": {"style": 2}, "K31": {"style": 2}, "K32": {"style": 2}, "K33": {"style": 2}, "K34": {"style": 2}, "K35": {"style": 2}, "K36": {"style": 2}, "K37": {"style": 2}, "K38": {"style": 2}, "K39": {"style": 2}, "K40": {"style": 2}, "K41": {"style": 2}, "K42": {"style": 2}, "K43": {"style": 2}, "K44": {"style": 2}, "K45": {"style": 2}, "K46": {"style": 2}, "K47": {"style": 2}, "K48": {"style": 2}, "K49": {"style": 2}, "K50": {"style": 2}, "K51": {"style": 2}, "K52": {"style": 2}, "K53": {"style": 2}, "K54": {"style": 2}, "L8": {"style": 12}, "L9": {"style": 12}, "L10": {"style": 12}, "L11": {"style": 12}, "L12": {"style": 12}, "L13": {"style": 12}, "L14": {"style": 12}, "L15": {"style": 12}, "L16": {"style": 12}, "L17": {"style": 2}, "L18": {"style": 2}, "L19": {"style": 2}, "L20": {"style": 2}, "L21": {"style": 2}, "L22": {"style": 2}, "L23": {"style": 2}, "L24": {"style": 2}, "L25": {"style": 2}, "L26": {"style": 2}, "L27": {"style": 2}, "L28": {"style": 2}, "L29": {"style": 2}, "L30": {"style": 2}, "L31": {"style": 2}, "L32": {"style": 2}, "L33": {"style": 2}, "L34": {"style": 2}, "L35": {"style": 2}, "L36": {"style": 2}, "L37": {"style": 2}, "L38": {"style": 2}, "L39": {"style": 2}, "L40": {"style": 2}, "L41": {"style": 2}, "L42": {"style": 2}, "L43": {"style": 2}, "L44": {"style": 2}, "L45": {"style": 2}, "L46": {"style": 2}, "L47": {"style": 2}, "L48": {"style": 2}, "L49": {"style": 2}, "L50": {"style": 2}, "L51": {"style": 2}, "L52": {"style": 2}, "L53": {"style": 2}, "L54": {"style": 2}, "M8": {"style": 12}, "M9": {"style": 12}, "M10": {"style": 12}, "M11": {"style": 12}, "M12": {"style": 12}, "M13": {"style": 12}, "M14": {"style": 12}, "M15": {"style": 12}, "M16": {"style": 12}, "M17": {"style": 2}, "M18": {"style": 2}, "M19": {"style": 2}, "M20": {"style": 2}, "M21": {"style": 2}, "M22": {"style": 2}, "M23": {"style": 2}, "M24": {"style": 2}, "M25": {"style": 2}, "M26": {"style": 2}, "M27": {"style": 2}, "M28": {"style": 2}, "M29": {"style": 2}, "M30": {"style": 2}, "M31": {"style": 2}, "M32": {"style": 2}, "M33": {"style": 2}, "M34": {"style": 2}, "M35": {"style": 2}, "M36": {"style": 2}, "M37": {"style": 2}, "M38": {"style": 2}, "M39": {"style": 2}, "M40": {"style": 2}, "M41": {"style": 2}, "M42": {"style": 2}, "M43": {"style": 2}, "M44": {"style": 2}, "M45": {"style": 2}, "M46": {"style": 2}, "M47": {"style": 2}, "M48": {"style": 2}, "M49": {"style": 2}, "M50": {"style": 2}, "M51": {"style": 2}, "M52": {"style": 2}, "M53": {"style": 2}, "M54": {"style": 2}}, "conditionalFormats": [], "figures": [], "filterTables": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "fontSize": 16, "bold": true}, "2": {"bold": true}, "3": {"fillColor": "#f8f9fa"}, "4": {"align": "right", "bold": true}, "5": {"italic": true, "bold": true}, "6": {"bold": false}, "7": {"fillColor": ""}, "8": {"bold": false, "fillColor": ""}, "9": {"align": "left", "italic": true, "bold": true}, "10": {"bold": false, "align": "left"}, "11": {"bold": true, "align": "left"}, "12": {"wrapping": "wrap"}, "13": {"align": "center"}}, "formats": {"1": "0.0", "2": "0%", "3": "0.0%", "4": "0.00"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "odooVersion": 5, "globalFilters": [{"id": "7785b753-917c-4640-a937-4b6226222ca8", "type": "date", "label": "Year", "defaultValue": {}, "rangeType": "year", "defaultsToCurrentPeriod": true}], "pivots": {}, "pivotNextId": 1, "lists": {}, "listNextId": 1, "chartOdooMenusReferences": {"cb548cd5-6798-46c9-ade5-3b497efbfc4a": "account_accountant.menu_accounting", "151b0f60-aa89-457e-9456-9807ab779be6": "account_accountant.menu_accounting", "86aac7b2-f3a5-46e1-a097-916aa4b8f8d0": "account_reports.menu_action_account_report_profit_and_loss", "3586cbc4-6ca3-46af-951f-94d4773f94dc": "account_reports.menu_action_account_report_aged_receivable", "57f18f9a-55b7-4b84-b927-480c4c56c026": "account_reports.menu_action_account_report_profit_and_loss", "8f1fb053-5700-4bb3-b93d-55ceb2083d31": "account_reports.menu_action_account_report_aged_payable"}}