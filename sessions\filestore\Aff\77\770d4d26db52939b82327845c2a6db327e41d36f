
/* /web/static/src/legacy/js/services/session.js */
odoo.define('web.session',function(require){"use strict";var Session=require('web.Session');var session=new Session(undefined,undefined,{use_cors:false});session.is_bound=session.session_bind();return session;});;

/* /web/static/src/legacy/js/public/public_root.js */
odoo.define('@web/legacy/js/public/public_root',async function(require){'use strict';let __exports={};const dom=require('web.dom');const legacyEnv=require('web.public_env');const session=require('web.session');const{getCookie}=require('web.utils.cookies');const publicWidget=require('web.public.widget');const{registry}=require('@web/core/registry');const AbstractService=require("web.AbstractService");const lazyloader=require("web.public.lazyloader");const{makeLegacyNotificationService,makeLegacyRpcService,makeLegacySessionService,makeLegacyDialogMappingService,mapLegacyEnvToWowlEnv,makeLegacyRainbowManService,}=require("@web/legacy/utils");const{standaloneAdapter}=require("web.OwlCompatibility");const{makeEnv,startServices}=require("@web/env");const{setLoadXmlDefaultApp,loadJS,templates}=require('@web/core/assets');const{MainComponentsContainer}=require("@web/core/main_components_container");const{browser}=require('@web/core/browser/browser');const{jsonrpc}=require('@web/core/network/rpc_service');const{_t}=require("@web/core/l10n/translation");const serviceRegistry=registry.category("services");const{Component,App,whenReady}=require("@odoo/owl");function getLang(){var html=document.documentElement;return(html.getAttribute('lang')||'en_US').replace('-','_');}
const lang=getCookie('frontend_lang')||getLang();var localeDef=lang!=='en_US'?loadJS('/web/webclient/locale/'+lang.replace('-','_')):Promise.resolve();const PublicRoot=__exports.PublicRoot=publicWidget.RootWidget.extend({events:_.extend({},publicWidget.RootWidget.prototype.events||{},{'submit .js_website_submit_form':'_onWebsiteFormSubmit','click .js_disable_on_click':'_onDisableOnClick',}),custom_events:_.extend({},publicWidget.RootWidget.prototype.custom_events||{},{call_service:'_onCallService',context_get:'_onContextGet',main_object_request:'_onMainObjectRequest',widgets_start_request:'_onWidgetsStartRequest',widgets_stop_request:'_onWidgetsStopRequest',}),init:function(){this._super.apply(this,arguments);this.env=legacyEnv;this.publicWidgets=[];},willStart:function(){return Promise.all([this._super.apply(this,arguments),session.is_bound,localeDef]);},start:function(){var defs=[this._super.apply(this,arguments),this._startWidgets()];this.$(".o_image[data-mimetype^='image']").each(function(){var $img=$(this);if(/gif|jpe|jpg|png/.test($img.data('mimetype'))&&$img.data('src')){$img.css('background-image',"url('"+$img.data('src')+"')");}});if(window.location.hash.indexOf("scrollTop=")>-1){this.el.scrollTop=+window.location.hash.match(/scrollTop=([0-9]+)/)[1];}
if($.fn.placeholder){$('input, textarea').placeholder();}
this.$el.children().on('error.datetimepicker',this._onDateTimePickerError.bind(this));return Promise.all(defs);},_getContext:function(context){return _.extend({'lang':getLang(),},context||{});},_getExtraContext:function(context){return this._getContext(context);},_getPublicWidgetsRegistry:function(options){return publicWidget.registry;},_getRegistry:function(){return registry.category("public_root_widgets");},_startWidgets:function($from,options){var self=this;if($from===undefined){$from=this.$('#wrapwrap');if(!$from.length){$from=this.$el;}}
options=Object.assign({},options,{wysiwyg:$('#wrapwrap').data('wysiwyg'),});this._stopWidgets($from);var defs=_.map(this._getPublicWidgetsRegistry(options),function(PublicWidget){var selector=PublicWidget.prototype.selector||'';var $target=dom.cssFind($from,selector,true);var defs=_.map($target,function(el){var widget=new PublicWidget(self,options);self.publicWidgets.push(widget);return widget.attachTo($(el));});return Promise.all(defs);});return Promise.all(defs);},_stopWidgets:function($from){var removedWidgets=_.map(this.publicWidgets,function(widget){if(!$from||$from.filter(widget.el).length||$from.find(widget.el).length){widget.destroy();return widget;}
return null;});this.publicWidgets=_.difference(this.publicWidgets,removedWidgets);},_onCallService:function(ev){function _computeContext(context,noContextKeys){context=_.extend({},this._getContext(),context);if(noContextKeys){context=_.omit(context,noContextKeys);}
return JSON.parse(JSON.stringify(context));}
const payload=ev.data;let args=payload.args||[];if(payload.service==='ajax'&&payload.method==='rpc'){args=args.concat(ev.target);var route=args[0];if(_.str.startsWith(route,'/web/dataset/call_kw/')){var params=args[1];var options=args[2];var noContextKeys;if(options){noContextKeys=options.noContextKeys;args[2]=_.omit(options,'noContextKeys');}
params.kwargs.context=_computeContext.call(this,params.kwargs.context,noContextKeys);}}else{return;}
const service=this.env.services[payload.service];const result=service[payload.method].apply(service,args);payload.callback(result);ev.stopPropagation();},_onContextGet:function(ev){if(ev.data.extra){ev.data.callback(this._getExtraContext(ev.data.context));}else{ev.data.callback(this._getContext(ev.data.context));}},_onMainObjectRequest:function(ev){var repr=$('html').data('main-object');var m=repr.match(/(.+)\((\d+),(.*)\)/);ev.data.callback({model:m[1],id:m[2]|0,});},_onWidgetsStartRequest:function(ev){this._startWidgets(ev.data.$target,ev.data.options).then(ev.data.onSuccess).guardedCatch(ev.data.onFailure);},_onWidgetsStopRequest:function(ev){this._stopWidgets(ev.data.$target);},_onWebsiteFormSubmit:function(ev){var $buttons=$(ev.currentTarget).find('button[type="submit"], a.a-submit');_.each($buttons,function(btn){var $btn=$(btn);$btn.prepend('<i class="fa fa-circle-o-notch fa-spin"></i> ');$btn.prop('disabled',true);});},_onDisableOnClick:function(ev){$(ev.currentTarget).addClass('disabled');},_onDateTimePickerError:function(ev){return false;},});owl.Component.env=legacyEnv;__exports.createPublicRoot=createPublicRoot;async function createPublicRoot(RootWidget){await lazyloader.allScriptsLoaded;AbstractService.prototype.deployServices(legacyEnv);serviceRegistry.add("legacy_rpc",makeLegacyRpcService(legacyEnv));serviceRegistry.add("legacy_session",makeLegacySessionService(legacyEnv,session));serviceRegistry.add("legacy_notification",makeLegacyNotificationService(legacyEnv));serviceRegistry.add("legacy_dialog_mapping",makeLegacyDialogMappingService(legacyEnv));serviceRegistry.add("legacy_rainbowman_service",makeLegacyRainbowManService(legacyEnv));const wowlToLegacyServiceMappers=registry.category('wowlToLegacyServiceMappers').getEntries();for(const[legacyServiceName,wowlToLegacyServiceMapper]of wowlToLegacyServiceMappers){serviceRegistry.add(legacyServiceName,wowlToLegacyServiceMapper(legacyEnv));}
await Promise.all([whenReady(),session.is_bound]);const baseUrl=session.prefix;const{fetch}=browser;browser.fetch=function(url,...args){if(!url.match(/^(?:https?:)?\/\//)){url=baseUrl+url;}
return fetch(url,...args);}
serviceRegistry.add("rpc",{async:true,start(env){let rpcId=0;return function rpc(route,params={},settings){if(!route.match(/^(?:https?:)?\/\//)){route=baseUrl+route;}
return jsonrpc(env,rpcId++,route,params,settings);};},},{force:true});const wowlEnv=makeEnv();await startServices(wowlEnv);mapLegacyEnvToWowlEnv(legacyEnv,wowlEnv);const publicRoot=new RootWidget(standaloneAdapter({Component}));const app=new App(MainComponentsContainer,{templates,env:wowlEnv,dev:wowlEnv.debug,translateFn:_t,translatableAttributes:["data-tooltip"],});setLoadXmlDefaultApp(app);await Promise.all([app.mount(document.body),publicRoot.attachTo(document.body),]);return publicRoot;}
__exports[Symbol.for("default")]={PublicRoot,createPublicRoot};return __exports;});odoo.define(`web.public.root`,async function(require){return require('@web/legacy/js/public/public_root')[Symbol.for("default")];});;

/* /web/static/src/legacy/js/public/public_root_instance.js */
odoo.define('@web/legacy/js/public/public_root_instance',async function(require){'use strict';let __exports={};const{PublicRoot,createPublicRoot}=require("@web/legacy/js/public/public_root");__exports[Symbol.for("default")]=createPublicRoot(PublicRoot);return __exports;});odoo.define(`root.widget`,async function(require){return require('@web/legacy/js/public/public_root_instance')[Symbol.for("default")];});;

/* /web/static/src/legacy/js/public/public_widget.js */
odoo.define('@web/legacy/js/public/public_widget',async function(require){'use strict';let __exports={};const dom=require('web.dom');const Widget=require('web.Widget');var RootWidget=Widget.extend({init:function(){this._super.apply(this,arguments);this._widgets=[];},start:function(){var defs=[this._super.apply(this,arguments)];defs.push(this._attachComponents());this._getRegistry().on("UPDATE",this,({operation,value})=>{if(operation==="add"){this._attachComponent(value);}});return Promise.all(defs);},_attachComponent:function(childInfo,$from){var self=this;var $elements=dom.cssFind($from||this.$el,childInfo.selector);var defs=_.map($elements,function(element){var w=new childInfo.Widget(self);self._widgets.push(w);return w.attachTo(element);});return Promise.all(defs);},_attachComponents:function($from){var self=this;var childInfos=this._getRegistry().getAll();var defs=_.map(childInfos,function(childInfo){return self._attachComponent(childInfo,$from);});return Promise.all(defs);},_getRegistry:function(){},});var PublicWidget=Widget.extend({selector:false,events:{},init:function(parent,options){this._super.apply(this,arguments);this.options=options||{};},destroy:function(){if(this.selector){var $oldel=this.$el;this.setElement(null);}
this._super.apply(this,arguments);if(this.selector){this.$el=$oldel;this.el=$oldel[0];this.$target=this.$el;this.target=this.el;}},setElement:function(){this._super.apply(this,arguments);if(this.selector){this.$target=this.$el;this.target=this.el;}},_delegateEvents:function(){var self=this;var originalEvents=this.events;var events={};_.each(this.events,function(method,event){if(typeof method!=='string'){events[event]=method;return;}
var methodOptions=method.split(' ');if(methodOptions.length<=1){events[event]=method;return;}
var isAsync=_.contains(methodOptions,'async');if(!isAsync){events[event]=method;return;}
method=self.proxy(methodOptions[methodOptions.length-1]);if(_.str.startsWith(event,'click')){method=dom.makeButtonHandler(method);}else{method=dom.makeAsyncHandler(method);}
events[event]=method;});this.events=events;this._super.apply(this,arguments);this.events=originalEvents;},_getContext:function(extra,extraContext){var context;this.trigger_up('context_get',{extra:extra||false,context:extraContext,callback:function(ctx){context=ctx;},});return context;},});var registry={};registry._fixAppleCollapse=PublicWidget.extend({selector:'div[data-bs-toggle="collapse"]',events:{'click':function(){},},});registry.login=PublicWidget.extend({selector:'.oe_login_form',events:{'submit':'_onSubmit',},_onSubmit(ev){if(!ev.isDefaultPrevented()){const btnEl=ev.currentTarget.querySelector('button[type="submit"]');const removeLoadingEffect=dom.addButtonLoadingEffect(btnEl);const oldPreventDefault=ev.preventDefault.bind(ev);ev.preventDefault=()=>{removeLoadingEffect();oldPreventDefault();};}},});__exports[Symbol.for("default")]={RootWidget:RootWidget,Widget:PublicWidget,registry:registry,};return __exports;});odoo.define(`web.public.widget`,async function(require){return require('@web/legacy/js/public/public_widget')[Symbol.for("default")];});;

/* /stock/static/src/legacy_web_report/utils.js */
odoo.define("report.utils",function(require){"use strict";function get_protocol_from_url(url){var a=document.createElement("a");a.href=url;return a.protocol;}
function get_host_from_url(url){var a=document.createElement("a");a.href=url;return a.host;}
function build_origin(protocol,host){return protocol+"//"+host;}
return{get_protocol_from_url:get_protocol_from_url,get_host_from_url:get_host_from_url,build_origin:build_origin,};});;

/* /stock/static/src/legacy_web_report/report.js */
odoo.define("report",function(require){"use strict";require("web.dom_ready");var utils=require("report.utils");if(window.self===window.top){return;}
$(document.body).addClass("o_in_iframe").addClass("container-fluid").removeClass("container");var web_base_url=window.origin;var trusted_host=utils.get_host_from_url(web_base_url);var trusted_protocol=utils.get_protocol_from_url(web_base_url);var trusted_origin=utils.build_origin(trusted_protocol,trusted_host);$("[res-id][res-model][view-type]").wrap("<a/>").attr("href","#").on("click",function(ev){ev.preventDefault();var action={type:"ir.actions.act_window",view_mode:$(this).attr("view-mode")||$(this).attr("view-type"),res_id:Number($(this).attr("res-id")),res_model:$(this).attr("res-model"),views:[[$(this).attr("view-id")||false,$(this).attr("view-type")]],};window.parent.postMessage({message:"report:do_action",action:action,},trusted_origin);});});