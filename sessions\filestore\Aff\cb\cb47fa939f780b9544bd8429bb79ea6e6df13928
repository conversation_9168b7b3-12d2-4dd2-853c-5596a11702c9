
/* <inline asset> */
@charset "UTF-8"; 

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 .o_report_reception .bg-dark-light h1, .o_colored_level .o_report_reception .bg-dark-light h1, .o_report_reception .bg-light-light h1, .o_colored_level .o_report_reception .bg-light-light h1, .o_report_reception .bg-danger-light h1, .o_colored_level .o_report_reception .bg-danger-light h1, .o_report_reception .bg-warning-light h1, .o_colored_level .o_report_reception .bg-warning-light h1, .o_report_reception .bg-info-light h1, .o_colored_level .o_report_reception .bg-info-light h1, .o_report_reception .bg-success-light h1, .o_colored_level .o_report_reception .bg-success-light h1, .o_report_reception .bg-secondary-light h1, .o_colored_level .o_report_reception .bg-secondary-light h1, .o_report_reception .bg-primary-light h1, .o_colored_level .o_report_reception .bg-primary-light h1, .toast-body h1, .o_colored_level .toast-body h1, .card-body h1, .o_colored_level .card-body h1, .bg-900 h1, .o_colored_level .bg-900 h1, .bg-800 h1, .o_colored_level .bg-800 h1, .bg-700 h1, .o_colored_level .bg-700 h1, .bg-600 h1, .o_colored_level .bg-600 h1, .bg-500 h1, .o_colored_level .bg-500 h1, .bg-400 h1, .o_colored_level .bg-400 h1, .bg-300 h1, .o_colored_level .bg-300 h1, .bg-200 h1, .o_colored_level .bg-200 h1, .bg-100 h1, .o_colored_level .bg-100 h1, .o_report_reception .bg-dark-light .h1, .o_colored_level .o_report_reception .bg-dark-light .h1, .o_report_reception .bg-light-light .h1, .o_colored_level .o_report_reception .bg-light-light .h1, .o_report_reception .bg-danger-light .h1, .o_colored_level .o_report_reception .bg-danger-light .h1, .o_report_reception .bg-warning-light .h1, .o_colored_level .o_report_reception .bg-warning-light .h1, .o_report_reception .bg-info-light .h1, .o_colored_level .o_report_reception .bg-info-light .h1, .o_report_reception .bg-success-light .h1, .o_colored_level .o_report_reception .bg-success-light .h1, .o_report_reception .bg-secondary-light .h1, .o_colored_level .o_report_reception .bg-secondary-light .h1, .o_report_reception .bg-primary-light .h1, .o_colored_level .o_report_reception .bg-primary-light .h1, .toast-body .h1, .o_colored_level .toast-body .h1, .card-body .h1, .o_colored_level .card-body .h1, .bg-900 .h1, .o_colored_level .bg-900 .h1, .bg-800 .h1, .o_colored_level .bg-800 .h1, .bg-700 .h1, .o_colored_level .bg-700 .h1, .bg-600 .h1, .o_colored_level .bg-600 .h1, .bg-500 .h1, .o_colored_level .bg-500 .h1, .bg-400 .h1, .bg-300 .h1, .bg-200 .h1, .bg-100 .h1, .o_report_reception .bg-dark-light h2, .o_colored_level .o_report_reception .bg-dark-light h2, .o_report_reception .bg-light-light h2, .o_colored_level .o_report_reception .bg-light-light h2, .o_report_reception .bg-danger-light h2, .o_colored_level .o_report_reception .bg-danger-light h2, .o_report_reception .bg-warning-light h2, .o_colored_level .o_report_reception .bg-warning-light h2, .o_report_reception .bg-info-light h2, .o_colored_level .o_report_reception .bg-info-light h2, .o_report_reception .bg-success-light h2, .o_colored_level .o_report_reception .bg-success-light h2, .o_report_reception .bg-secondary-light h2, .o_colored_level .o_report_reception .bg-secondary-light h2, .o_report_reception .bg-primary-light h2, .o_colored_level .o_report_reception .bg-primary-light h2, .toast-body h2, .o_colored_level .toast-body h2, .card-body h2, .o_colored_level .card-body h2, .bg-900 h2, .o_colored_level .bg-900 h2, .bg-800 h2, .o_colored_level .bg-800 h2, .bg-700 h2, .o_colored_level .bg-700 h2, .bg-600 h2, .o_colored_level .bg-600 h2, .bg-500 h2, .o_colored_level .bg-500 h2, .bg-400 h2, .o_colored_level .bg-400 h2, .bg-300 h2, .o_colored_level .bg-300 h2, .bg-200 h2, .o_colored_level .bg-200 h2, .bg-100 h2, .o_colored_level .bg-100 h2, .o_report_reception .bg-dark-light .h2, .o_colored_level .o_report_reception .bg-dark-light .h2, .o_report_reception .bg-light-light .h2, .o_colored_level .o_report_reception .bg-light-light .h2, .o_report_reception .bg-danger-light .h2, .o_colored_level .o_report_reception .bg-danger-light .h2, .o_report_reception .bg-warning-light .h2, .o_colored_level .o_report_reception .bg-warning-light .h2, .o_report_reception .bg-info-light .h2, .o_colored_level .o_report_reception .bg-info-light .h2, .o_report_reception .bg-success-light .h2, .o_colored_level .o_report_reception .bg-success-light .h2, .o_report_reception .bg-secondary-light .h2, .o_colored_level .o_report_reception .bg-secondary-light .h2, .o_report_reception .bg-primary-light .h2, .o_colored_level .o_report_reception .bg-primary-light .h2, .toast-body .h2, .o_colored_level .toast-body .h2, .card-body .h2, .o_colored_level .card-body .h2, .bg-900 .h2, .o_colored_level .bg-900 .h2, .bg-800 .h2, .o_colored_level .bg-800 .h2, .bg-700 .h2, .o_colored_level .bg-700 .h2, .bg-600 .h2, .o_colored_level .bg-600 .h2, .bg-500 .h2, .o_colored_level .bg-500 .h2, .bg-400 .h2, .bg-300 .h2, .bg-200 .h2, .bg-100 .h2, .o_report_reception .bg-dark-light h3, .o_colored_level .o_report_reception .bg-dark-light h3, .o_report_reception .bg-light-light h3, .o_colored_level .o_report_reception .bg-light-light h3, .o_report_reception .bg-danger-light h3, .o_colored_level .o_report_reception .bg-danger-light h3, .o_report_reception .bg-warning-light h3, .o_colored_level .o_report_reception .bg-warning-light h3, .o_report_reception .bg-info-light h3, .o_colored_level .o_report_reception .bg-info-light h3, .o_report_reception .bg-success-light h3, .o_colored_level .o_report_reception .bg-success-light h3, .o_report_reception .bg-secondary-light h3, .o_colored_level .o_report_reception .bg-secondary-light h3, .o_report_reception .bg-primary-light h3, .o_colored_level .o_report_reception .bg-primary-light h3, .toast-body h3, .o_colored_level .toast-body h3, .card-body h3, .o_colored_level .card-body h3, .bg-900 h3, .o_colored_level .bg-900 h3, .bg-800 h3, .o_colored_level .bg-800 h3, .bg-700 h3, .o_colored_level .bg-700 h3, .bg-600 h3, .o_colored_level .bg-600 h3, .bg-500 h3, .o_colored_level .bg-500 h3, .bg-400 h3, .o_colored_level .bg-400 h3, .bg-300 h3, .o_colored_level .bg-300 h3, .bg-200 h3, .o_colored_level .bg-200 h3, .bg-100 h3, .o_colored_level .bg-100 h3, .o_report_reception .bg-dark-light .h3, .o_colored_level .o_report_reception .bg-dark-light .h3, .o_report_reception .bg-light-light .h3, .o_colored_level .o_report_reception .bg-light-light .h3, .o_report_reception .bg-danger-light .h3, .o_colored_level .o_report_reception .bg-danger-light .h3, .o_report_reception .bg-warning-light .h3, .o_colored_level .o_report_reception .bg-warning-light .h3, .o_report_reception .bg-info-light .h3, .o_colored_level .o_report_reception .bg-info-light .h3, .o_report_reception .bg-success-light .h3, .o_colored_level .o_report_reception .bg-success-light .h3, .o_report_reception .bg-secondary-light .h3, .o_colored_level .o_report_reception .bg-secondary-light .h3, .o_report_reception .bg-primary-light .h3, .o_colored_level .o_report_reception .bg-primary-light .h3, .toast-body .h3, .o_colored_level .toast-body .h3, .card-body .h3, .o_colored_level .card-body .h3, .bg-900 .h3, .o_colored_level .bg-900 .h3, .bg-800 .h3, .o_colored_level .bg-800 .h3, .bg-700 .h3, .o_colored_level .bg-700 .h3, .bg-600 .h3, .o_colored_level .bg-600 .h3, .bg-500 .h3, .o_colored_level .bg-500 .h3, .bg-400 .h3, .bg-300 .h3, .bg-200 .h3, .bg-100 .h3, .o_report_reception .bg-dark-light h4, .o_colored_level .o_report_reception .bg-dark-light h4, .o_report_reception .bg-light-light h4, .o_colored_level .o_report_reception .bg-light-light h4, .o_report_reception .bg-danger-light h4, .o_colored_level .o_report_reception .bg-danger-light h4, .o_report_reception .bg-warning-light h4, .o_colored_level .o_report_reception .bg-warning-light h4, .o_report_reception .bg-info-light h4, .o_colored_level .o_report_reception .bg-info-light h4, .o_report_reception .bg-success-light h4, .o_colored_level .o_report_reception .bg-success-light h4, .o_report_reception .bg-secondary-light h4, .o_colored_level .o_report_reception .bg-secondary-light h4, .o_report_reception .bg-primary-light h4, .o_colored_level .o_report_reception .bg-primary-light h4, .toast-body h4, .o_colored_level .toast-body h4, .card-body h4, .o_colored_level .card-body h4, .bg-900 h4, .o_colored_level .bg-900 h4, .bg-800 h4, .o_colored_level .bg-800 h4, .bg-700 h4, .o_colored_level .bg-700 h4, .bg-600 h4, .o_colored_level .bg-600 h4, .bg-500 h4, .o_colored_level .bg-500 h4, .bg-400 h4, .o_colored_level .bg-400 h4, .bg-300 h4, .o_colored_level .bg-300 h4, .bg-200 h4, .o_colored_level .bg-200 h4, .bg-100 h4, .o_colored_level .bg-100 h4, .o_report_reception .bg-dark-light .h4, .o_colored_level .o_report_reception .bg-dark-light .h4, .o_report_reception .bg-light-light .h4, .o_colored_level .o_report_reception .bg-light-light .h4, .o_report_reception .bg-danger-light .h4, .o_colored_level .o_report_reception .bg-danger-light .h4, .o_report_reception .bg-warning-light .h4, .o_colored_level .o_report_reception .bg-warning-light .h4, .o_report_reception .bg-info-light .h4, .o_colored_level .o_report_reception .bg-info-light .h4, .o_report_reception .bg-success-light .h4, .o_colored_level .o_report_reception .bg-success-light .h4, .o_report_reception .bg-secondary-light .h4, .o_colored_level .o_report_reception .bg-secondary-light .h4, .o_report_reception .bg-primary-light .h4, .o_colored_level .o_report_reception .bg-primary-light .h4, .toast-body .h4, .o_colored_level .toast-body .h4, .card-body .h4, .o_colored_level .card-body .h4, .bg-900 .h4, .o_colored_level .bg-900 .h4, .bg-800 .h4, .o_colored_level .bg-800 .h4, .bg-700 .h4, .o_colored_level .bg-700 .h4, .bg-600 .h4, .o_colored_level .bg-600 .h4, .bg-500 .h4, .o_colored_level .bg-500 .h4, .bg-400 .h4, .bg-300 .h4, .bg-200 .h4, .bg-100 .h4, .o_report_reception .bg-dark-light h5, .o_colored_level .o_report_reception .bg-dark-light h5, .o_report_reception .bg-light-light h5, .o_colored_level .o_report_reception .bg-light-light h5, .o_report_reception .bg-danger-light h5, .o_colored_level .o_report_reception .bg-danger-light h5, .o_report_reception .bg-warning-light h5, .o_colored_level .o_report_reception .bg-warning-light h5, .o_report_reception .bg-info-light h5, .o_colored_level .o_report_reception .bg-info-light h5, .o_report_reception .bg-success-light h5, .o_colored_level .o_report_reception .bg-success-light h5, .o_report_reception .bg-secondary-light h5, .o_colored_level .o_report_reception .bg-secondary-light h5, .o_report_reception .bg-primary-light h5, .o_colored_level .o_report_reception .bg-primary-light h5, .toast-body h5, .o_colored_level .toast-body h5, .card-body h5, .o_colored_level .card-body h5, .bg-900 h5, .o_colored_level .bg-900 h5, .bg-800 h5, .o_colored_level .bg-800 h5, .bg-700 h5, .o_colored_level .bg-700 h5, .bg-600 h5, .o_colored_level .bg-600 h5, .bg-500 h5, .o_colored_level .bg-500 h5, .bg-400 h5, .o_colored_level .bg-400 h5, .bg-300 h5, .o_colored_level .bg-300 h5, .bg-200 h5, .o_colored_level .bg-200 h5, .bg-100 h5, .o_colored_level .bg-100 h5, .o_report_reception .bg-dark-light .h5, .o_colored_level .o_report_reception .bg-dark-light .h5, .o_report_reception .bg-light-light .h5, .o_colored_level .o_report_reception .bg-light-light .h5, .o_report_reception .bg-danger-light .h5, .o_colored_level .o_report_reception .bg-danger-light .h5, .o_report_reception .bg-warning-light .h5, .o_colored_level .o_report_reception .bg-warning-light .h5, .o_report_reception .bg-info-light .h5, .o_colored_level .o_report_reception .bg-info-light .h5, .o_report_reception .bg-success-light .h5, .o_colored_level .o_report_reception .bg-success-light .h5, .o_report_reception .bg-secondary-light .h5, .o_colored_level .o_report_reception .bg-secondary-light .h5, .o_report_reception .bg-primary-light .h5, .o_colored_level .o_report_reception .bg-primary-light .h5, .toast-body .h5, .o_colored_level .toast-body .h5, .card-body .h5, .o_colored_level .card-body .h5, .bg-900 .h5, .o_colored_level .bg-900 .h5, .bg-800 .h5, .o_colored_level .bg-800 .h5, .bg-700 .h5, .o_colored_level .bg-700 .h5, .bg-600 .h5, .o_colored_level .bg-600 .h5, .bg-500 .h5, .o_colored_level .bg-500 .h5, .bg-400 .h5, .bg-300 .h5, .bg-200 .h5, .bg-100 .h5, .o_report_reception .bg-dark-light h6, .o_colored_level .o_report_reception .bg-dark-light h6, .o_report_reception .bg-light-light h6, .o_colored_level .o_report_reception .bg-light-light h6, .o_report_reception .bg-danger-light h6, .o_colored_level .o_report_reception .bg-danger-light h6, .o_report_reception .bg-warning-light h6, .o_colored_level .o_report_reception .bg-warning-light h6, .o_report_reception .bg-info-light h6, .o_colored_level .o_report_reception .bg-info-light h6, .o_report_reception .bg-success-light h6, .o_colored_level .o_report_reception .bg-success-light h6, .o_report_reception .bg-secondary-light h6, .o_colored_level .o_report_reception .bg-secondary-light h6, .o_report_reception .bg-primary-light h6, .o_colored_level .o_report_reception .bg-primary-light h6, .toast-body h6, .o_colored_level .toast-body h6, .card-body h6, .o_colored_level .card-body h6, .bg-900 h6, .o_colored_level .bg-900 h6, .bg-800 h6, .o_colored_level .bg-800 h6, .bg-700 h6, .o_colored_level .bg-700 h6, .bg-600 h6, .o_colored_level .bg-600 h6, .bg-500 h6, .o_colored_level .bg-500 h6, .bg-400 h6, .o_colored_level .bg-400 h6, .bg-300 h6, .o_colored_level .bg-300 h6, .bg-200 h6, .o_colored_level .bg-200 h6, .bg-100 h6, .o_colored_level .bg-100 h6, .o_report_reception .bg-dark-light .h6, .o_colored_level .o_report_reception .bg-dark-light .h6, .o_report_reception .bg-light-light .h6, .o_colored_level .o_report_reception .bg-light-light .h6, .o_report_reception .bg-danger-light .h6, .o_colored_level .o_report_reception .bg-danger-light .h6, .o_report_reception .bg-warning-light .h6, .o_colored_level .o_report_reception .bg-warning-light .h6, .o_report_reception .bg-info-light .h6, .o_colored_level .o_report_reception .bg-info-light .h6, .o_report_reception .bg-success-light .h6, .o_colored_level .o_report_reception .bg-success-light .h6, .o_report_reception .bg-secondary-light .h6, .o_colored_level .o_report_reception .bg-secondary-light .h6, .o_report_reception .bg-primary-light .h6, .o_colored_level .o_report_reception .bg-primary-light .h6, .toast-body .h6, .o_colored_level .toast-body .h6, .card-body .h6, .o_colored_level .card-body .h6, .bg-900 .h6, .o_colored_level .bg-900 .h6, .bg-800 .h6, .o_colored_level .bg-800 .h6, .bg-700 .h6, .o_colored_level .bg-700 .h6, .bg-600 .h6, .o_colored_level .bg-600 .h6, .bg-500 .h6, .o_colored_level .bg-500 .h6, .bg-400 .h6, .bg-300 .h6, .bg-200 .h6, .bg-100 .h6{color: inherit;}

/* /web/static/src/legacy/scss/utils.scss */
 .o_view_nocontent .o_nocontent_help .o_view_nocontent_neutral_face:before, .o_view_nocontent .o_nocontent_help .o_view_nocontent_smiling_face:before, .o_view_nocontent .o_nocontent_help .o_view_nocontent_empty_folder:before{content: ""; display: block; margin: auto; background-size: cover;}.o_view_nocontent .o_nocontent_help .o_view_nocontent_empty_folder:before{width: 120px; height: 80px; margin-top: 30px; margin-bottom: 30px; background: transparent url(/web/static/img/empty_folder.svg) no-repeat center;}

/* /web_enterprise/static/src/scss/primary_variables.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */
 

/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */
 

/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /base/static/src/scss/onboarding.variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /account/static/src/scss/variables.scss */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /website/static/src/scss/primary_variables.scss */
 

/* /website/static/src/scss/options/user_values.scss */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */
 

/* /web_gantt/static/src/scss/web_gantt.variables.scss */
 

/* /documents/static/src/scss/documents.variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /website/static/src/snippets/s_badge/000_variables.scss */
 

/* /website/static/src/snippets/s_product_list/000_variables.scss */
 

/* /website/static/src/scss/secondary_variables.scss */
 

/* /web_enterprise/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/webclient/actions/reports/bootstrap_overridden_report.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/src/scss/import_bootstrap.scss */
 :root{--bs-blue: #0d6efd; --bs-indigo: #6610f2; --bs-purple: #6f42c1; --bs-pink: #d63384; --bs-red: #dc3545; --bs-orange: #fd7e14; --bs-yellow: #ffc107; --bs-green: #198754; --bs-teal: #20c997; --bs-cyan: #0dcaf0; --bs-white: #fff; --bs-gray: #6c757d; --bs-gray-dark: #343a40; --bs-gray-100: #f8f9fa; --bs-gray-200: #e9ecef; --bs-gray-300: #dee2e6; --bs-gray-400: #ced4da; --bs-gray-500: #adb5bd; --bs-gray-600: #6c757d; --bs-gray-700: #495057; --bs-gray-800: #343a40; --bs-gray-900: #212529; --bs-primary: #0d6efd; --bs-secondary: #6c757d; --bs-success: #198754; --bs-info: #0dcaf0; --bs-warning: #ffc107; --bs-danger: #dc3545; --bs-light: #f8f9fa; --bs-dark: #212529; --bs-primary-rgb: 13, 110, 253; --bs-secondary-rgb: 108, 117, 125; --bs-success-rgb: 25, 135, 84; --bs-info-rgb: 13, 202, 240; --bs-warning-rgb: 255, 193, 7; --bs-danger-rgb: 220, 53, 69; --bs-light-rgb: 248, 249, 250; --bs-dark-rgb: 33, 37, 41; --bs-white-rgb: 255, 255, 255; --bs-black-rgb: 0, 0, 0; --bs-body-color-rgb: 33, 37, 41; --bs-body-bg-rgb: 255, 255, 255; --bs-font-sans-serif: "Odoo Unicode Support Noto", "Lucida Grande", Helvetica, Verdana, Arial, "Odoo Unicode Support Noto", sans-serif; --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --bs-body-font-family: var(--bs-font-sans-serif); --bs-body-font-size: 1rem; --bs-body-font-weight: 400; --bs-body-line-height: 1.5; --bs-body-color: #212529; --bs-body-bg: #fff;}*, *::before, *::after{box-sizing: border-box;}@media (prefers-reduced-motion: no-preference){:root{scroll-behavior: smooth;}}body{margin: 0; font-family: var(--bs-body-font-family); font-size: var(--bs-body-font-size); font-weight: var(--bs-body-font-weight); line-height: var(--bs-body-line-height); color: var(--bs-body-color); text-align: var(--bs-body-text-align); background-color: var(--bs-body-bg); -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}hr{margin: 1rem 0; color: inherit; background-color: currentColor; border: 0; opacity: 0.25;}hr:not([size]){height: 1px;}h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1{margin-top: 0; margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2;}h1, .h1{font-size: calc(1.375rem + 1.5vw);}@media (min-width: 1200px){h1, .h1{font-size: 2.5rem;}}h2, .h2{font-size: calc(1.325rem + 0.9vw);}@media (min-width: 1200px){h2, .h2{font-size: 2rem;}}h3, .h3{font-size: calc(1.3rem + 0.6vw);}@media (min-width: 1200px){h3, .h3{font-size: 1.75rem;}}h4, .h4{font-size: calc(1.275rem + 0.3vw);}@media (min-width: 1200px){h4, .h4{font-size: 1.5rem;}}h5, .h5{font-size: 1.25rem;}h6, .h6{font-size: 1rem;}p{margin-top: 0; margin-bottom: 1rem;}abbr[title], abbr[data-bs-original-title]{text-decoration: underline dotted; cursor: help; text-decoration-skip-ink: none;}address{margin-bottom: 1rem; font-style: normal; line-height: inherit;}ol, ul{padding-left: 2rem;}ol, ul, dl{margin-top: 0; margin-bottom: 1rem;}ol ol, ul ul, ol ul, ul ol{margin-bottom: 0;}dt{font-weight: 700;}dd{margin-bottom: .5rem; margin-left: 0;}blockquote{margin: 0 0 1rem;}b, strong{font-weight: bolder;}small, .small{font-size: 0.875em;}mark, .mark{padding: 0.2em; background-color: #fcf8e3;}sub, sup{position: relative; font-size: 0.75em; line-height: 0; vertical-align: baseline;}sub{bottom: -.25em;}sup{top: -.5em;}a{color: #0d6efd; text-decoration: none;}a:hover{color: #0a58ca;}a:not([href]):not([class]), a:not([href]):not([class]):hover{color: inherit; text-decoration: none;}pre, code, kbd, samp{font-family: var(--bs-font-monospace); font-size: 1em; direction: ltr ; unicode-bidi: bidi-override;}pre{display: block; margin-top: 0; margin-bottom: 1rem; overflow: auto; font-size: 0.875em;}pre code{font-size: inherit; color: inherit; word-break: normal;}code{font-size: 0.875em; color: #d63384; word-wrap: break-word;}a > code{color: inherit;}kbd{padding: 0.2rem 0.4rem; font-size: 0.875em; color: #fff; background-color: #212529; border-radius: 0.2rem;}kbd kbd{padding: 0; font-size: 1em; font-weight: 700;}figure{margin: 0 0 1rem;}img, svg{vertical-align: middle;}table{caption-side: bottom; border-collapse: collapse;}caption{padding-top: 0.5rem; padding-bottom: 0.5rem; color: #6c757d; text-align: left;}th{text-align: inherit; text-align: -webkit-match-parent;}thead, tbody, tfoot, tr, td, th{border-color: inherit; border-style: solid; border-width: 0;}label{display: inline-block;}button{border-radius: 0;}button:focus:not(:focus-visible){outline: 0;}input, button, select, optgroup, textarea{margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}button, select{text-transform: none;}[role="button"]{cursor: pointer;}select{word-wrap: normal;}select:disabled{opacity: 1;}[list]::-webkit-calendar-picker-indicator{display: none;}button, [type="button"], [type="reset"], [type="submit"]{-webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled){cursor: pointer;}::-moz-focus-inner{padding: 0; border-style: none;}textarea{resize: vertical;}fieldset{min-width: 0; padding: 0; margin: 0; border: 0;}legend{float: left; width: 100%; padding: 0; margin-bottom: 0.5rem; font-size: calc(1.275rem + 0.3vw); line-height: inherit;}@media (min-width: 1200px){legend{font-size: 1.5rem;}}legend + *{clear: left;}::-webkit-datetime-edit-fields-wrapper, ::-webkit-datetime-edit-text, ::-webkit-datetime-edit-minute, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-year-field{padding: 0;}::-webkit-inner-spin-button{height: auto;}[type="search"]{outline-offset: -2px; -webkit--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;}::-webkit-search-decoration{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}::-webkit-color-swatch-wrapper{padding: 0;}::file-selector-button{font: inherit;}::-webkit-file-upload-button{font: inherit; -webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}output{display: inline-block;}iframe{border: 0;}summary{display: list-item; cursor: pointer;}progress{vertical-align: baseline;}[hidden]{display: none !important;}.lead{font-size: 1.25rem; font-weight: 300;}.display-1{font-size: calc(1.625rem + 4.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-1{font-size: 5rem;}}.display-2{font-size: calc(1.575rem + 3.9vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-2{font-size: 4.5rem;}}.display-3{font-size: calc(1.525rem + 3.3vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-3{font-size: 4rem;}}.display-4{font-size: calc(1.475rem + 2.7vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-4{font-size: 3.5rem;}}.display-5{font-size: calc(1.425rem + 2.1vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-5{font-size: 3rem;}}.display-6{font-size: calc(1.375rem + 1.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-6{font-size: 2.5rem;}}.list-unstyled{padding-left: 0; list-style: none;}.list-inline{padding-left: 0; list-style: none;}.list-inline-item{display: inline-block;}.list-inline-item:not(:last-child){margin-right: 0.5rem;}.initialism{font-size: 0.875em; text-transform: uppercase;}.blockquote{margin-bottom: 1rem; font-size: 1.25rem;}.blockquote > :last-child{margin-bottom: 0;}.blockquote-footer{margin-top: -1rem; margin-bottom: 1rem; font-size: 0.875em; color: #6c757d;}.blockquote-footer::before{content: "\2014\00A0";}.img-fluid{max-width: 100%; height: auto;}.img-thumbnail{padding: 0.25rem; background-color: #fff; border: 1px solid #dee2e6; border-radius: 0.25rem; max-width: 100%; height: auto;}.figure{display: inline-block;}.figure-img{margin-bottom: 0.5rem; line-height: 1;}.figure-caption{font-size: 0.875em; color: #6c757d;}.container, .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm{width: 100%; padding-right: var(--bs-gutter-x, 15px); padding-left: var(--bs-gutter-x, 15px); margin-right: auto; margin-left: auto;}@media (min-width: 576px){.container-sm, .container{max-width: 540px;}}@media (min-width: 768px){.container-md, .container-sm, .container{max-width: 720px;}}@media (min-width: 992px){.container-lg, .container-md, .container-sm, .container{max-width: 960px;}}@media (min-width: 1200px){.container-xl, .container-lg, .container-md, .container-sm, .container{max-width: 1140px;}}@media (min-width: 1400px){.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container{max-width: 1320px;}}.row{--bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-top: calc(-1 * var(--bs-gutter-y)); margin-right: calc(-.5 * var(--bs-gutter-x)); margin-left: calc(-.5 * var(--bs-gutter-x));}.row > *{flex-shrink: 0; width: 100%; max-width: 100%; padding-right: calc(var(--bs-gutter-x) * .5); padding-left: calc(var(--bs-gutter-x) * .5); margin-top: var(--bs-gutter-y);}.col{flex: 1 0 0%;}.row-cols-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-1{margin-left: 8.33333333%;}.offset-2{margin-left: 16.66666667%;}.offset-3{margin-left: 25%;}.offset-4{margin-left: 33.33333333%;}.offset-5{margin-left: 41.66666667%;}.offset-6{margin-left: 50%;}.offset-7{margin-left: 58.33333333%;}.offset-8{margin-left: 66.66666667%;}.offset-9{margin-left: 75%;}.offset-10{margin-left: 83.33333333%;}.offset-11{margin-left: 91.66666667%;}.g-0, .gx-0{--bs-gutter-x: 0;}.g-0, .gy-0{--bs-gutter-y: 0;}.g-1, .gx-1{--bs-gutter-x: 0.25rem;}.g-1, .gy-1{--bs-gutter-y: 0.25rem;}.g-2, .gx-2{--bs-gutter-x: 0.5rem;}.g-2, .gy-2{--bs-gutter-y: 0.5rem;}.g-3, .gx-3{--bs-gutter-x: 1rem;}.g-3, .gy-3{--bs-gutter-y: 1rem;}.g-4, .gx-4{--bs-gutter-x: 1.5rem;}.g-4, .gy-4{--bs-gutter-y: 1.5rem;}.g-5, .gx-5{--bs-gutter-x: 3rem;}.g-5, .gy-5{--bs-gutter-y: 3rem;}@media (min-width: 576px){.col-sm{flex: 1 0 0%;}.row-cols-sm-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-sm-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-sm-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-sm-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-sm-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-sm-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-sm-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-sm-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-sm-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-sm-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-sm-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-sm-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-sm-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-sm-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-sm-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-sm-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-sm-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-sm-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-sm-0{margin-left: 0;}.offset-sm-1{margin-left: 8.33333333%;}.offset-sm-2{margin-left: 16.66666667%;}.offset-sm-3{margin-left: 25%;}.offset-sm-4{margin-left: 33.33333333%;}.offset-sm-5{margin-left: 41.66666667%;}.offset-sm-6{margin-left: 50%;}.offset-sm-7{margin-left: 58.33333333%;}.offset-sm-8{margin-left: 66.66666667%;}.offset-sm-9{margin-left: 75%;}.offset-sm-10{margin-left: 83.33333333%;}.offset-sm-11{margin-left: 91.66666667%;}.g-sm-0, .gx-sm-0{--bs-gutter-x: 0;}.g-sm-0, .gy-sm-0{--bs-gutter-y: 0;}.g-sm-1, .gx-sm-1{--bs-gutter-x: 0.25rem;}.g-sm-1, .gy-sm-1{--bs-gutter-y: 0.25rem;}.g-sm-2, .gx-sm-2{--bs-gutter-x: 0.5rem;}.g-sm-2, .gy-sm-2{--bs-gutter-y: 0.5rem;}.g-sm-3, .gx-sm-3{--bs-gutter-x: 1rem;}.g-sm-3, .gy-sm-3{--bs-gutter-y: 1rem;}.g-sm-4, .gx-sm-4{--bs-gutter-x: 1.5rem;}.g-sm-4, .gy-sm-4{--bs-gutter-y: 1.5rem;}.g-sm-5, .gx-sm-5{--bs-gutter-x: 3rem;}.g-sm-5, .gy-sm-5{--bs-gutter-y: 3rem;}}@media (min-width: 768px){.col-md{flex: 1 0 0%;}.row-cols-md-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-md-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-md-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-md-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-md-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-md-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-md-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-md-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-md-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-md-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-md-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-md-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-md-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-md-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-md-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-md-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-md-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-md-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-md-0{margin-left: 0;}.offset-md-1{margin-left: 8.33333333%;}.offset-md-2{margin-left: 16.66666667%;}.offset-md-3{margin-left: 25%;}.offset-md-4{margin-left: 33.33333333%;}.offset-md-5{margin-left: 41.66666667%;}.offset-md-6{margin-left: 50%;}.offset-md-7{margin-left: 58.33333333%;}.offset-md-8{margin-left: 66.66666667%;}.offset-md-9{margin-left: 75%;}.offset-md-10{margin-left: 83.33333333%;}.offset-md-11{margin-left: 91.66666667%;}.g-md-0, .gx-md-0{--bs-gutter-x: 0;}.g-md-0, .gy-md-0{--bs-gutter-y: 0;}.g-md-1, .gx-md-1{--bs-gutter-x: 0.25rem;}.g-md-1, .gy-md-1{--bs-gutter-y: 0.25rem;}.g-md-2, .gx-md-2{--bs-gutter-x: 0.5rem;}.g-md-2, .gy-md-2{--bs-gutter-y: 0.5rem;}.g-md-3, .gx-md-3{--bs-gutter-x: 1rem;}.g-md-3, .gy-md-3{--bs-gutter-y: 1rem;}.g-md-4, .gx-md-4{--bs-gutter-x: 1.5rem;}.g-md-4, .gy-md-4{--bs-gutter-y: 1.5rem;}.g-md-5, .gx-md-5{--bs-gutter-x: 3rem;}.g-md-5, .gy-md-5{--bs-gutter-y: 3rem;}}@media (min-width: 992px){.col-lg{flex: 1 0 0%;}.row-cols-lg-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-lg-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-lg-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-lg-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-lg-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-lg-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-lg-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-lg-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-lg-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-lg-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-lg-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-lg-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-lg-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-lg-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-lg-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-lg-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-lg-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-lg-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-lg-0{margin-left: 0;}.offset-lg-1{margin-left: 8.33333333%;}.offset-lg-2{margin-left: 16.66666667%;}.offset-lg-3{margin-left: 25%;}.offset-lg-4{margin-left: 33.33333333%;}.offset-lg-5{margin-left: 41.66666667%;}.offset-lg-6{margin-left: 50%;}.offset-lg-7{margin-left: 58.33333333%;}.offset-lg-8{margin-left: 66.66666667%;}.offset-lg-9{margin-left: 75%;}.offset-lg-10{margin-left: 83.33333333%;}.offset-lg-11{margin-left: 91.66666667%;}.g-lg-0, .gx-lg-0{--bs-gutter-x: 0;}.g-lg-0, .gy-lg-0{--bs-gutter-y: 0;}.g-lg-1, .gx-lg-1{--bs-gutter-x: 0.25rem;}.g-lg-1, .gy-lg-1{--bs-gutter-y: 0.25rem;}.g-lg-2, .gx-lg-2{--bs-gutter-x: 0.5rem;}.g-lg-2, .gy-lg-2{--bs-gutter-y: 0.5rem;}.g-lg-3, .gx-lg-3{--bs-gutter-x: 1rem;}.g-lg-3, .gy-lg-3{--bs-gutter-y: 1rem;}.g-lg-4, .gx-lg-4{--bs-gutter-x: 1.5rem;}.g-lg-4, .gy-lg-4{--bs-gutter-y: 1.5rem;}.g-lg-5, .gx-lg-5{--bs-gutter-x: 3rem;}.g-lg-5, .gy-lg-5{--bs-gutter-y: 3rem;}}@media (min-width: 1200px){.col-xl{flex: 1 0 0%;}.row-cols-xl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xl-0{margin-left: 0;}.offset-xl-1{margin-left: 8.33333333%;}.offset-xl-2{margin-left: 16.66666667%;}.offset-xl-3{margin-left: 25%;}.offset-xl-4{margin-left: 33.33333333%;}.offset-xl-5{margin-left: 41.66666667%;}.offset-xl-6{margin-left: 50%;}.offset-xl-7{margin-left: 58.33333333%;}.offset-xl-8{margin-left: 66.66666667%;}.offset-xl-9{margin-left: 75%;}.offset-xl-10{margin-left: 83.33333333%;}.offset-xl-11{margin-left: 91.66666667%;}.g-xl-0, .gx-xl-0{--bs-gutter-x: 0;}.g-xl-0, .gy-xl-0{--bs-gutter-y: 0;}.g-xl-1, .gx-xl-1{--bs-gutter-x: 0.25rem;}.g-xl-1, .gy-xl-1{--bs-gutter-y: 0.25rem;}.g-xl-2, .gx-xl-2{--bs-gutter-x: 0.5rem;}.g-xl-2, .gy-xl-2{--bs-gutter-y: 0.5rem;}.g-xl-3, .gx-xl-3{--bs-gutter-x: 1rem;}.g-xl-3, .gy-xl-3{--bs-gutter-y: 1rem;}.g-xl-4, .gx-xl-4{--bs-gutter-x: 1.5rem;}.g-xl-4, .gy-xl-4{--bs-gutter-y: 1.5rem;}.g-xl-5, .gx-xl-5{--bs-gutter-x: 3rem;}.g-xl-5, .gy-xl-5{--bs-gutter-y: 3rem;}}@media (min-width: 1400px){.col-xxl{flex: 1 0 0%;}.row-cols-xxl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xxl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xxl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xxl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xxl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xxl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xxl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xxl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xxl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xxl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xxl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xxl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xxl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xxl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xxl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xxl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xxl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xxl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xxl-0{margin-left: 0;}.offset-xxl-1{margin-left: 8.33333333%;}.offset-xxl-2{margin-left: 16.66666667%;}.offset-xxl-3{margin-left: 25%;}.offset-xxl-4{margin-left: 33.33333333%;}.offset-xxl-5{margin-left: 41.66666667%;}.offset-xxl-6{margin-left: 50%;}.offset-xxl-7{margin-left: 58.33333333%;}.offset-xxl-8{margin-left: 66.66666667%;}.offset-xxl-9{margin-left: 75%;}.offset-xxl-10{margin-left: 83.33333333%;}.offset-xxl-11{margin-left: 91.66666667%;}.g-xxl-0, .gx-xxl-0{--bs-gutter-x: 0;}.g-xxl-0, .gy-xxl-0{--bs-gutter-y: 0;}.g-xxl-1, .gx-xxl-1{--bs-gutter-x: 0.25rem;}.g-xxl-1, .gy-xxl-1{--bs-gutter-y: 0.25rem;}.g-xxl-2, .gx-xxl-2{--bs-gutter-x: 0.5rem;}.g-xxl-2, .gy-xxl-2{--bs-gutter-y: 0.5rem;}.g-xxl-3, .gx-xxl-3{--bs-gutter-x: 1rem;}.g-xxl-3, .gy-xxl-3{--bs-gutter-y: 1rem;}.g-xxl-4, .gx-xxl-4{--bs-gutter-x: 1.5rem;}.g-xxl-4, .gy-xxl-4{--bs-gutter-y: 1.5rem;}.g-xxl-5, .gx-xxl-5{--bs-gutter-x: 3rem;}.g-xxl-5, .gy-xxl-5{--bs-gutter-y: 3rem;}}.table{--bs-table-bg: transparent; --bs-table-accent-bg: transparent; --bs-table-striped-color: #212529; --bs-table-striped-bg: rgba(0, 0, 0, 0.05); --bs-table-active-color: #212529; --bs-table-active-bg: rgba(0, 0, 0, 0.1); --bs-table-hover-color: ; --bs-table-hover-bg: rgba(0, 0, 0, 0.075); width: 100%; margin-bottom: 1rem; vertical-align: top; border-color: #dee2e6;}.table > :not(caption) > * > *{padding: 0.5rem 0.5rem; background-color: var(--bs-table-bg); border-bottom-width: 1px; box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);}.table > tbody{vertical-align: inherit;}.table > thead{vertical-align: bottom;}.table > :not(:first-child){border-top: 2px solid currentColor;}.caption-top{caption-side: top;}.table-sm > :not(caption) > * > *{padding: 0.25rem 0.25rem;}.table-bordered > :not(caption) > *{border-width: 1px 0;}.table-bordered > :not(caption) > * > *{border-width: 0 1px;}.table-borderless > :not(caption) > * > *{border-bottom-width: 0;}.table-borderless > :not(:first-child){border-top-width: 0;}.table-striped > tbody > tr:nth-of-type(odd) > *{--bs-table-accent-bg: var(--bs-table-striped-bg); color: var(--bs-table-striped-color);}.table-active{--bs-table-accent-bg: var(--bs-table-active-bg); color: var(--bs-table-active-color);}.table-hover > tbody > tr:hover > *{--bs-table-accent-bg: var(--bs-table-hover-bg); color: var(--bs-table-hover-color);}.table-primary{--bs-table-bg: #cfe2ff; --bs-table-striped-bg: #c5d7f2; --bs-table-striped-color: #000; --bs-table-active-bg: #bacbe6; --bs-table-active-color: #000; --bs-table-hover-bg: #bfd1ec; --bs-table-hover-color: #000; color: #000; border-color: #bacbe6;}.table-secondary{--bs-table-bg: #e2e3e5; --bs-table-striped-bg: #d7d8da; --bs-table-striped-color: #000; --bs-table-active-bg: #cbccce; --bs-table-active-color: #000; --bs-table-hover-bg: #d1d2d4; --bs-table-hover-color: #000; color: #000; border-color: #cbccce;}.table-success{--bs-table-bg: #d1e7dd; --bs-table-striped-bg: #c7dbd2; --bs-table-striped-color: #000; --bs-table-active-bg: #bcd0c7; --bs-table-active-color: #000; --bs-table-hover-bg: #c1d6cc; --bs-table-hover-color: #000; color: #000; border-color: #bcd0c7;}.table-info{--bs-table-bg: #cff4fc; --bs-table-striped-bg: #c5e8ef; --bs-table-striped-color: #000; --bs-table-active-bg: #badce3; --bs-table-active-color: #000; --bs-table-hover-bg: #bfe2e9; --bs-table-hover-color: #000; color: #000; border-color: #badce3;}.table-warning{--bs-table-bg: #fff3cd; --bs-table-striped-bg: #f2e7c3; --bs-table-striped-color: #000; --bs-table-active-bg: #e6dbb9; --bs-table-active-color: #000; --bs-table-hover-bg: #ece1be; --bs-table-hover-color: #000; color: #000; border-color: #e6dbb9;}.table-danger{--bs-table-bg: #f8d7da; --bs-table-striped-bg: #eccccf; --bs-table-striped-color: #000; --bs-table-active-bg: #dfc2c4; --bs-table-active-color: #000; --bs-table-hover-bg: #e5c7ca; --bs-table-hover-color: #000; color: #000; border-color: #dfc2c4;}.table-light{--bs-table-bg: #f8f9fa; --bs-table-striped-bg: #ecedee; --bs-table-striped-color: #000; --bs-table-active-bg: #dfe0e1; --bs-table-active-color: #000; --bs-table-hover-bg: #e5e6e7; --bs-table-hover-color: #000; color: #000; border-color: #dfe0e1;}.table-dark{--bs-table-bg: #212529; --bs-table-striped-bg: #2c3034; --bs-table-striped-color: #fff; --bs-table-active-bg: #373b3e; --bs-table-active-color: #fff; --bs-table-hover-bg: #323539; --bs-table-hover-color: #fff; color: #fff; border-color: #373b3e;}.table-responsive{overflow-x: auto; -webkit-overflow-scrolling: touch;}@media (max-width: 575.98px){.table-responsive-sm{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 767.98px){.table-responsive-md{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 991.98px){.table-responsive-lg{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1199.98px){.table-responsive-xl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1399.98px){.table-responsive-xxl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}.form-label{margin-bottom: 0.5rem;}.col-form-label{padding-top: calc(0.375rem + 1px); padding-bottom: calc(0.375rem + 1px); margin-bottom: 0; font-size: inherit; line-height: 1.5;}.col-form-label-lg{padding-top: calc(0.5rem + 1px); padding-bottom: calc(0.5rem + 1px); font-size: 1.25rem;}.col-form-label-sm{padding-top: calc(0.25rem + 1px); padding-bottom: calc(0.25rem + 1px); font-size: 0.875rem;}.form-text{margin-top: 0.25rem; font-size: 0.875em; color: #6c757d;}.form-control{display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; background-color: #fff; background-clip: padding-box; border: 1px solid #ced4da; -webkit-appearance: none; -moz-appearance: none; appearance: none; border-radius: 0.25rem; transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control{transition: none;}}.form-control[type="file"]{overflow: hidden;}.form-control[type="file"]:not(:disabled):not([readonly]){cursor: pointer;}.form-control:focus{color: #212529; background-color: #fff; border-color: #86b7fe; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.form-control::-webkit-date-and-time-value{height: 1.5em;}.form-control::placeholder{color: #6c757d; opacity: 1;}.form-control:disabled, .form-control[readonly]{background-color: #e9ecef; opacity: 1;}.form-control::file-selector-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: #212529; background-color: #e9ecef; pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: 1px; border-radius: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control::file-selector-button{transition: none;}}.form-control:hover:not(:disabled):not([readonly])::file-selector-button{background-color: #dde0e3;}.form-control::-webkit-file-upload-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: #212529; background-color: #e9ecef; pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: 1px; border-radius: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control::-webkit-file-upload-button{transition: none;}}.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button{background-color: #dde0e3;}.form-control-plaintext{display: block; width: 100%; padding: 0.375rem 0; margin-bottom: 0; line-height: 1.5; color: #212529; background-color: transparent; border: solid transparent; border-width: 1px 0;}.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg{padding-right: 0; padding-left: 0;}.form-control-sm{min-height: calc(1.5em + 0.5rem + 2px); padding: 0.25rem 0.5rem; font-size: 0.875rem; border-radius: 0.2rem;}.form-control-sm::file-selector-button{padding: 0.25rem 0.5rem; margin: -0.25rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-sm::-webkit-file-upload-button{padding: 0.25rem 0.5rem; margin: -0.25rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-lg{min-height: calc(1.5em + 1rem + 2px); padding: 0.5rem 1rem; font-size: 1.25rem; border-radius: 0.3rem;}.form-control-lg::file-selector-button{padding: 0.5rem 1rem; margin: -0.5rem -1rem; margin-inline-end: 1rem;}.form-control-lg::-webkit-file-upload-button{padding: 0.5rem 1rem; margin: -0.5rem -1rem; margin-inline-end: 1rem;}textarea.form-control{min-height: calc(1.5em + 0.75rem + 2px);}textarea.form-control-sm{min-height: calc(1.5em + 0.5rem + 2px);}textarea.form-control-lg{min-height: calc(1.5em + 1rem + 2px);}.form-control-color{width: 3rem; height: auto; padding: 0.375rem;}.form-control-color:not(:disabled):not([readonly]){cursor: pointer;}.form-control-color::-moz-color-swatch{height: 1.5em; border-radius: 0.25rem;}.form-control-color::-webkit-color-swatch{height: 1.5em; border-radius: 0.25rem;}.form-select{display: block; width: 100%; padding: 0.375rem 2.25rem 0.375rem 0.75rem; -moz-padding-start: calc(0.75rem - 3px); font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; background-color: #fff; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 16px 12px; border: 1px solid #ced4da; border-radius: 0.25rem; transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-select{transition: none;}}.form-select:focus{border-color: #86b7fe; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.form-select[multiple], .form-select[size]:not([size="1"]){padding-right: 0.75rem; background-image: none;}.form-select:disabled{background-color: #e9ecef;}.form-select:-moz-focusring{color: transparent; text-shadow: 0 0 0 #212529;}.form-select-sm{padding-top: 0.25rem; padding-bottom: 0.25rem; padding-left: 0.5rem; font-size: 0.875rem; border-radius: 0.2rem;}.form-select-lg{padding-top: 0.5rem; padding-bottom: 0.5rem; padding-left: 1rem; font-size: 1.25rem; border-radius: 0.3rem;}.form-check{display: block; min-height: 1.5rem; padding-left: 1.5em; margin-bottom: 0.125rem;}.form-check .form-check-input{float: left; margin-left: -1.5em;}.form-check-input{width: 1em; height: 1em; margin-top: 0.25em; vertical-align: top; background-color: #fff; background-repeat: no-repeat; background-position: center; background-size: contain; border: 1px solid rgba(0, 0, 0, 0.25); -webkit-appearance: none; -moz-appearance: none; appearance: none; color-adjust: exact;}.form-check-input[type="checkbox"]{border-radius: 0.25em;}.form-check-input[type="radio"]{border-radius: 50%;}.form-check-input:active{filter: brightness(90%);}.form-check-input:focus{border-color: #86b7fe; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.form-check-input:checked{background-color: #0d6efd; border-color: #0d6efd;}.form-check-input:checked[type="checkbox"]{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");}.form-check-input:checked[type="radio"]{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");}.form-check-input[type="checkbox"]:indeterminate{background-color: #0d6efd; border-color: #0d6efd; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");}.form-check-input:disabled{pointer-events: none; filter: none; opacity: 0.5;}.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label{opacity: 0.5;}.form-switch{padding-left: 2.5em;}.form-switch .form-check-input{width: 2em; margin-left: -2.5em; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e"); background-position: left center; border-radius: 2em; transition: background-position 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-switch .form-check-input{transition: none;}}.form-switch .form-check-input:focus{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e");}.form-switch .form-check-input:checked{background-position: right center; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");}.form-check-inline{display: inline-block; margin-right: 1rem;}.btn-check{position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none;}.btn-check[disabled] + .btn, .btn-check:disabled + .btn{pointer-events: none; filter: none; opacity: 0.65;}.form-range{width: 100%; height: 1.5rem; padding: 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}.form-range:focus{outline: 0;}.form-range:focus::-webkit-slider-thumb{box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.form-range:focus::-moz-range-thumb{box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.form-range::-moz-focus-outer{border: 0;}.form-range::-webkit-slider-thumb{width: 1rem; height: 1rem; margin-top: -0.25rem; background-color: #0d6efd; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-range::-webkit-slider-thumb{transition: none;}}.form-range::-webkit-slider-thumb:active{background-color: #b6d4fe;}.form-range::-webkit-slider-runnable-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.form-range::-moz-range-thumb{width: 1rem; height: 1rem; background-color: #0d6efd; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-range::-moz-range-thumb{transition: none;}}.form-range::-moz-range-thumb:active{background-color: #b6d4fe;}.form-range::-moz-range-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.form-range:disabled{pointer-events: none;}.form-range:disabled::-webkit-slider-thumb{background-color: #adb5bd;}.form-range:disabled::-moz-range-thumb{background-color: #adb5bd;}.form-floating{position: relative;}.form-floating > .form-control, .form-floating > .form-select{height: calc(3.5rem + 2px); line-height: 1.25;}.form-floating > label{position: absolute; top: 0; left: 0; height: 100%; padding: 1rem 0.75rem; pointer-events: none; border: 1px solid transparent; transform-origin: 0 0; transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-floating > label{transition: none;}}.form-floating > .form-control{padding: 1rem 0.75rem;}.form-floating > .form-control::placeholder{color: transparent;}.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown){padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:-webkit-autofill{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-select{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:focus ~ label, .form-floating > .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-select ~ label{opacity: 0.65; transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control:-webkit-autofill ~ label{opacity: 0.65; transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.input-group{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: stretch; width: 100%;}.input-group > .form-control, .input-group > .form-select{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 1%; min-width: 0;}.input-group > .form-control:focus, .input-group > .form-select:focus{z-index: 3;}.input-group .btn{position: relative; z-index: 2;}.input-group .btn:focus{z-index: 3;}.input-group-text{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; text-align: center; white-space: nowrap; background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 0.25rem;}.input-group-lg > .form-control, .input-group-lg > .form-select, .input-group-lg > .input-group-text, .input-group-lg > .btn{padding: 0.5rem 1rem; font-size: 1.25rem; border-radius: 0.3rem;}.input-group-sm > .form-control, .input-group-sm > .form-select, .input-group-sm > .input-group-text, .input-group-sm > .btn{padding: 0.25rem 0.5rem; font-size: 0.875rem; border-radius: 0.2rem;}.input-group-lg > .form-select, .input-group-sm > .form-select{padding-right: 3rem;}.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu), .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group.has-validation > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu), .input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){margin-left: -1px; border-top-left-radius: 0; border-bottom-left-radius: 0;}.valid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.875em; color: #198754;}.valid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.875rem; color: #fff; background-color: rgba(25, 135, 84, 0.9); border-radius: 0.25rem;}.was-validated :valid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip, .is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip{display: block;}.was-validated .form-control:valid, .form-control.is-valid{border-color: #198754; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:valid:focus, .form-control.is-valid:focus{border-color: #198754; box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);}.was-validated textarea.form-control:valid, textarea.form-control.is-valid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .form-select:valid, .form-select.is-valid{border-color: #198754;}.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"]{padding-right: 4.125rem; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-position: right 0.75rem center, center right 2.25rem; background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-select:valid:focus, .form-select.is-valid:focus{border-color: #198754; box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);}.was-validated .form-check-input:valid, .form-check-input.is-valid{border-color: #198754;}.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked{background-color: #198754;}.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus{box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);}.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label{color: #198754;}.form-check-inline .form-check-input ~ .valid-feedback{margin-left: .5em;}.was-validated .input-group .form-control:valid, .input-group .form-control.is-valid, .was-validated .input-group .form-select:valid, .input-group .form-select.is-valid{z-index: 1;}.was-validated .input-group .form-control:valid:focus, .input-group .form-control.is-valid:focus, .was-validated .input-group .form-select:valid:focus, .input-group .form-select.is-valid:focus{z-index: 3;}.invalid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.875em; color: #dc3545;}.invalid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.875rem; color: #fff; background-color: rgba(220, 53, 69, 0.9); border-radius: 0.25rem;}.was-validated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-tooltip, .is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-control:invalid, .form-control.is-invalid{border-color: #dc3545; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .form-select:invalid, .form-select.is-invalid{border-color: #dc3545;}.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"]{padding-right: 4.125rem; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); background-position: right 0.75rem center, center right 2.25rem; background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated .form-check-input:invalid, .form-check-input.is-invalid{border-color: #dc3545;}.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked{background-color: #dc3545;}.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label{color: #dc3545;}.form-check-inline .form-check-input ~ .invalid-feedback{margin-left: .5em;}.was-validated .input-group .form-control:invalid, .input-group .form-control.is-invalid, .was-validated .input-group .form-select:invalid, .input-group .form-select.is-invalid{z-index: 2;}.was-validated .input-group .form-control:invalid:focus, .input-group .form-control.is-invalid:focus, .was-validated .input-group .form-select:invalid:focus, .input-group .form-select.is-invalid:focus{z-index: 3;}.btn{display: inline-block; font-weight: 400; line-height: 1.5; color: #212529; text-align: center; vertical-align: middle; cursor: pointer; user-select: none; background-color: transparent; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 1rem; border-radius: 0.25rem; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.btn{transition: none;}}.btn:hover{color: #212529;}.btn-check:focus + .btn, .btn:focus{outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.btn:disabled, .btn.disabled, fieldset:disabled .btn{pointer-events: none; opacity: 0.65;}.btn-link{font-weight: 400; color: #0d6efd; text-decoration: none;}.btn-link:hover{color: #0a58ca;}.btn-link:disabled, .btn-link.disabled{color: #6c757d;}.btn-lg, .btn-group-lg > .btn{padding: 0.5rem 1rem; font-size: 1.25rem; border-radius: 0.3rem;}.btn-sm, .btn-group-sm > .btn{padding: 0.25rem 0.5rem; font-size: 0.875rem; border-radius: 0.2rem;}.fade{transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce){.fade{transition: none;}}.fade:not(.show){opacity: 0;}.collapse:not(.show){display: none;}.collapsing{height: 0; overflow: hidden; transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing{transition: none;}}.collapsing.collapse-horizontal{width: 0; height: auto; transition: width 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing.collapse-horizontal{transition: none;}}.dropup, .dropend, .dropdown, .dropstart{position: relative;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid; border-right: 0.3em solid transparent; border-bottom: 0; border-left: 0.3em solid transparent;}.dropdown-toggle:empty::after{margin-left: 0;}.dropdown-menu{position: absolute; z-index: 1000; display: none; min-width: 10rem; padding: 0.5rem 0; margin: 0; font-size: 1rem; color: #212529; text-align: left; list-style: none; background-color: #fff; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.15); border-radius: 0.25rem;}.dropdown-menu[data-bs-popper]{top: 100%; left: 0; margin-top: 0.125rem;}.dropdown-menu-start{--bs-position: start;}.dropdown-menu-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-end{--bs-position: end;}.dropdown-menu-end[data-bs-popper]{right: 0; left: auto;}@media (min-width: 576px){.dropdown-menu-sm-start{--bs-position: start;}.dropdown-menu-sm-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-sm-end{--bs-position: end;}.dropdown-menu-sm-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 768px){.dropdown-menu-md-start{--bs-position: start;}.dropdown-menu-md-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-md-end{--bs-position: end;}.dropdown-menu-md-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 992px){.dropdown-menu-lg-start{--bs-position: start;}.dropdown-menu-lg-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-lg-end{--bs-position: end;}.dropdown-menu-lg-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1200px){.dropdown-menu-xl-start{--bs-position: start;}.dropdown-menu-xl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xl-end{--bs-position: end;}.dropdown-menu-xl-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1400px){.dropdown-menu-xxl-start{--bs-position: start;}.dropdown-menu-xxl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xxl-end{--bs-position: end;}.dropdown-menu-xxl-end[data-bs-popper]{right: 0; left: auto;}}.dropup .dropdown-menu[data-bs-popper]{top: auto; bottom: 100%; margin-top: 0; margin-bottom: 0.125rem;}.dropup .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0; border-right: 0.3em solid transparent; border-bottom: 0.3em solid; border-left: 0.3em solid transparent;}.dropup .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-menu[data-bs-popper]{top: 0; right: auto; left: 100%; margin-top: 0; margin-left: 0.125rem;}.dropend .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0; border-bottom: 0.3em solid transparent; border-left: 0.3em solid;}.dropend .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-toggle::after{vertical-align: 0;}.dropstart .dropdown-menu[data-bs-popper]{top: 0; right: 100%; left: auto; margin-top: 0; margin-right: 0.125rem;}.dropstart .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: "";}.dropstart .dropdown-toggle::after{display: none;}.dropstart .dropdown-toggle::before{display: inline-block; margin-right: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0.3em solid; border-bottom: 0.3em solid transparent;}.dropstart .dropdown-toggle:empty::after{margin-left: 0;}.dropstart .dropdown-toggle::before{vertical-align: 0;}.dropdown-divider{height: 0; margin: 0.5rem 0; overflow: hidden; border-top: 1px solid rgba(0, 0, 0, 0.15);}.dropdown-item{display: block; width: 100%; padding: 0.25rem 1rem; clear: both; font-weight: 400; color: #212529; text-align: inherit; white-space: nowrap; background-color: transparent; border: 0;}.dropdown-item:hover, .dropdown-item:focus{color: #1e2125; background-color: #e9ecef;}.dropdown-item.active, .dropdown-item:active{color: #fff; text-decoration: none; background-color: #0d6efd;}.dropdown-item.disabled, .dropdown-item:disabled{color: #adb5bd; pointer-events: none; background-color: transparent;}.dropdown-menu.show{display: block;}.dropdown-header{display: block; padding: 0.5rem 1rem; margin-bottom: 0; font-size: 0.875rem; color: #6c757d; white-space: nowrap;}.dropdown-item-text{display: block; padding: 0.25rem 1rem; color: #212529;}.dropdown-menu-dark{color: #dee2e6; background-color: #343a40; border-color: rgba(0, 0, 0, 0.15);}.dropdown-menu-dark .dropdown-item{color: #dee2e6;}.dropdown-menu-dark .dropdown-item:hover, .dropdown-menu-dark .dropdown-item:focus{color: #fff; background-color: rgba(255, 255, 255, 0.15);}.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-item:active{color: #fff; background-color: #0d6efd;}.dropdown-menu-dark .dropdown-item.disabled, .dropdown-menu-dark .dropdown-item:disabled{color: #adb5bd;}.dropdown-menu-dark .dropdown-divider{border-color: rgba(0, 0, 0, 0.15);}.dropdown-menu-dark .dropdown-item-text{color: #dee2e6;}.dropdown-menu-dark .dropdown-header{color: #adb5bd;}.btn-group, .btn-group-vertical{position: relative; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; vertical-align: middle;}.btn-group > .btn, .btn-group-vertical > .btn{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:focus + .btn, .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn-check:focus + .btn, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active{z-index: 1;}.btn-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; justify-content: flex-start;}.btn-toolbar .input-group{width: auto;}.btn-group > .btn:not(:first-child), .btn-group > .btn-group:not(:first-child){margin-left: -1px;}.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn-group:not(:last-child) > .btn{border-top-right-radius: 0; border-bottom-right-radius: 0;}.btn-group > .btn:nth-child(n + 3), .btn-group > :not(.btn-check) + .btn, .btn-group > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}.dropdown-toggle-split{padding-right: 0.5625rem; padding-left: 0.5625rem;}.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after{margin-left: 0;}.dropstart .dropdown-toggle-split::before{margin-right: 0;}.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split{padding-right: 0.375rem; padding-left: 0.375rem;}.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split{padding-right: 0.75rem; padding-left: 0.75rem;}.btn-group-vertical{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: flex-start; justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group{width: 100%;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child){margin-top: -1px;}.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn-group:not(:last-child) > .btn{border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.btn-group-vertical > .btn ~ .btn, .btn-group-vertical > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-top-right-radius: 0;}.nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding-left: 0; margin-bottom: 0; list-style: none;}.nav-link{display: block; padding: 0.5rem 1rem; color: #0d6efd; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.nav-link{transition: none;}}.nav-link:hover, .nav-link:focus{color: #0a58ca;}.nav-link.disabled{color: #6c757d; pointer-events: none; cursor: default;}.nav-tabs{border-bottom: 1px solid #dee2e6;}.nav-tabs .nav-link{margin-bottom: -1px; background: none; border: 1px solid transparent; border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus{border-color: #e9ecef #e9ecef #dee2e6; isolation: isolate;}.nav-tabs .nav-link.disabled{color: #6c757d; background-color: transparent; border-color: transparent;}.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link{color: #495057; background-color: #fff; border-color: #dee2e6 #dee2e6 #fff;}.nav-tabs .dropdown-menu{margin-top: -1px; border-top-left-radius: 0; border-top-right-radius: 0;}.nav-pills .nav-link{background: none; border: 0; border-radius: 0.25rem;}.nav-pills .nav-link.active, .nav-pills .show > .nav-link{color: #fff; background-color: #0d6efd;}.nav-fill > .nav-link, .nav-fill .nav-item{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: center;}.nav-justified > .nav-link, .nav-justified .nav-item{flex-basis: 0; flex-grow: 1; text-align: center;}.nav-fill .nav-item .nav-link, .nav-justified .nav-item .nav-link{width: 100%;}.tab-content > .tab-pane{display: none;}.tab-content > .active{display: block;}.navbar{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between; padding-top: 0.5rem; padding-bottom: 0.5rem;}.navbar > .container, .navbar > .container-fluid, .navbar > .container-sm, .navbar > .container-md, .navbar > .container-lg, .navbar > .container-xl, .navbar > .container-xxl{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: inherit; flex-wrap: inherit; align-items: center; justify-content: space-between;}.navbar-brand{padding-top: 0.3125rem; padding-bottom: 0.3125rem; margin-right: 1rem; font-size: 1.25rem; white-space: nowrap;}.navbar-nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; list-style: none;}.navbar-nav .nav-link{padding-right: 0; padding-left: 0;}.navbar-nav .dropdown-menu{position: static;}.navbar-text{padding-top: 0.5rem; padding-bottom: 0.5rem;}.navbar-collapse{flex-basis: 100%; flex-grow: 1; align-items: center;}.navbar-toggler{padding: 0.25rem 0.75rem; font-size: 1.25rem; line-height: 1; background-color: transparent; border: 1px solid transparent; border-radius: 0.25rem; transition: box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.navbar-toggler{transition: none;}}.navbar-toggler:hover{text-decoration: none;}.navbar-toggler:focus{text-decoration: none; outline: 0; box-shadow: 0 0 0 0.25rem;}.navbar-toggler-icon{display: inline-block; width: 1.5em; height: 1.5em; vertical-align: middle; background-repeat: no-repeat; background-position: center; background-size: 100%;}.navbar-nav-scroll{max-height: var(--bs-scroll-height, 75vh); overflow-y: auto;}@media (min-width: 576px){.navbar-expand-sm{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-sm .navbar-nav{flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-sm .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-sm .navbar-nav-scroll{overflow: visible;}.navbar-expand-sm .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-sm .navbar-toggler{display: none;}.navbar-expand-sm .offcanvas-header{display: none;}.navbar-expand-sm .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-sm .offcanvas-top, .navbar-expand-sm .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-sm .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 768px){.navbar-expand-md{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-md .navbar-nav{flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-md .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-md .navbar-nav-scroll{overflow: visible;}.navbar-expand-md .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-md .navbar-toggler{display: none;}.navbar-expand-md .offcanvas-header{display: none;}.navbar-expand-md .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-md .offcanvas-top, .navbar-expand-md .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-md .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 992px){.navbar-expand-lg{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-lg .navbar-nav{flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-lg .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-lg .navbar-nav-scroll{overflow: visible;}.navbar-expand-lg .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-lg .navbar-toggler{display: none;}.navbar-expand-lg .offcanvas-header{display: none;}.navbar-expand-lg .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-lg .offcanvas-top, .navbar-expand-lg .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-lg .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1200px){.navbar-expand-xl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xl .navbar-nav{flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xl .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-xl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xl .navbar-toggler{display: none;}.navbar-expand-xl .offcanvas-header{display: none;}.navbar-expand-xl .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-xl .offcanvas-top, .navbar-expand-xl .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-xl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1400px){.navbar-expand-xxl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xxl .navbar-nav{flex-direction: row;}.navbar-expand-xxl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xxl .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-xxl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xxl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xxl .navbar-toggler{display: none;}.navbar-expand-xxl .offcanvas-header{display: none;}.navbar-expand-xxl .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-xxl .offcanvas-top, .navbar-expand-xxl .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-xxl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}.navbar-expand{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand .navbar-nav{flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand .navbar-nav-scroll{overflow: visible;}.navbar-expand .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand .navbar-toggler{display: none;}.navbar-expand .offcanvas-header{display: none;}.navbar-expand .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand .offcanvas-top, .navbar-expand .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}.navbar-light .navbar-brand{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-nav .nav-link{color: rgba(0, 0, 0, 0.55);}.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus{color: rgba(0, 0, 0, 0.7);}.navbar-light .navbar-nav .nav-link.disabled{color: rgba(0, 0, 0, 0.3);}.navbar-light .navbar-nav .show > .nav-link, .navbar-light .navbar-nav .nav-link.active{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-toggler{color: rgba(0, 0, 0, 0.55); border-color: rgba(0, 0, 0, 0.1);}.navbar-light .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-light .navbar-text{color: rgba(0, 0, 0, 0.55);}.navbar-light .navbar-text a, .navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus{color: rgba(0, 0, 0, 0.9);}.navbar-dark .navbar-brand{color: #fff;}.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus{color: #fff;}.navbar-dark .navbar-nav .nav-link{color: rgba(255, 255, 255, 0.55);}.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus{color: rgba(255, 255, 255, 0.75);}.navbar-dark .navbar-nav .nav-link.disabled{color: rgba(255, 255, 255, 0.25);}.navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .nav-link.active{color: #fff;}.navbar-dark .navbar-toggler{color: rgba(255, 255, 255, 0.55); border-color: rgba(255, 255, 255, 0.1);}.navbar-dark .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-dark .navbar-text{color: rgba(255, 255, 255, 0.55);}.navbar-dark .navbar-text a, .navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus{color: #fff;}.card{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; background-clip: border-box; border: 1px solid rgba(0, 0, 0, 0.125); border-radius: 0.25rem;}.card > hr{margin-right: 0; margin-left: 0;}.card > .list-group{border-top: inherit; border-bottom: inherit;}.card > .list-group:first-child{border-top-width: 0; border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card > .list-group:last-child{border-bottom-width: 0; border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card > .card-header + .list-group, .card > .list-group + .card-footer{border-top: 0;}.card-body{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1rem 1rem;}.card-title{margin-bottom: 0.5rem;}.card-subtitle{margin-top: -0.25rem; margin-bottom: 0;}.card-text:last-child{margin-bottom: 0;}.card-link + .card-link{margin-left: 1rem;}.card-header{padding: 0.5rem 1rem; margin-bottom: 0; background-color: rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.125);}.card-header:first-child{border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;}.card-footer{padding: 0.5rem 1rem; background-color: rgba(0, 0, 0, 0.03); border-top: 1px solid rgba(0, 0, 0, 0.125);}.card-footer:last-child{border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);}.card-header-tabs{margin-right: -0.5rem; margin-bottom: -0.5rem; margin-left: -0.5rem; border-bottom: 0;}.card-header-pills{margin-right: -0.5rem; margin-left: -0.5rem;}.card-img-overlay{position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: 1rem; border-radius: calc(0.25rem - 1px);}.card-img, .card-img-top, .card-img-bottom{width: 100%;}.card-img, .card-img-top{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-img, .card-img-bottom{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card-group > .card{margin-bottom: 0.75rem;}@media (min-width: 576px){.card-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}.card-group > .card{flex: 1 0 0%; margin-bottom: 0;}.card-group > .card + .card{margin-left: 0; border-left: 0;}.card-group > .card:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-top, .card-group > .card:not(:last-child) .card-header{border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-bottom, .card-group > .card:not(:last-child) .card-footer{border-bottom-right-radius: 0;}.card-group > .card:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-top, .card-group > .card:not(:first-child) .card-header{border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-bottom, .card-group > .card:not(:first-child) .card-footer{border-bottom-left-radius: 0;}}.accordion-button{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 100%; padding: 1rem 1.25rem; font-size: 1rem; color: #212529; text-align: left; background-color: #fff; border: 0; border-radius: 0; overflow-anchor: none; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;}@media (prefers-reduced-motion: reduce){.accordion-button{transition: none;}}.accordion-button:not(.collapsed){color: #0c63e4; background-color: #e7f1ff; box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);}.accordion-button:not(.collapsed)::after{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); transform: rotate(-180deg);}.accordion-button::after{flex-shrink: 0; width: 1.25rem; height: 1.25rem; margin-left: auto; content: ""; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-size: 1.25rem; transition: transform 0.2s ease-in-out;}@media (prefers-reduced-motion: reduce){.accordion-button::after{transition: none;}}.accordion-button:hover{z-index: 2;}.accordion-button:focus{z-index: 3; border-color: #86b7fe; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.accordion-header{margin-bottom: 0;}.accordion-item{background-color: #fff; border: 1px solid rgba(0, 0, 0, 0.125);}.accordion-item:first-of-type{border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.accordion-item:first-of-type .accordion-button{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.accordion-item:not(:first-of-type){border-top: 0;}.accordion-item:last-of-type{border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.accordion-item:last-of-type .accordion-button.collapsed{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.accordion-item:last-of-type .accordion-collapse{border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.accordion-body{padding: 1rem 1.25rem;}.accordion-flush .accordion-collapse{border-width: 0;}.accordion-flush .accordion-item{border-right: 0; border-left: 0; border-radius: 0;}.accordion-flush .accordion-item:first-child{border-top: 0;}.accordion-flush .accordion-item:last-child{border-bottom: 0;}.accordion-flush .accordion-item .accordion-button{border-radius: 0;}.breadcrumb{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding: 0 0; margin-bottom: 1rem; list-style: none;}.breadcrumb-item + .breadcrumb-item{padding-left: 0.5rem;}.breadcrumb-item + .breadcrumb-item::before{float: left; padding-right: 0.5rem; color: #6c757d; content: var(--bs-breadcrumb-divider, "/") ;}.breadcrumb-item.active{color: #6c757d;}.pagination{display: -webkit-box; display: -webkit-flex; display: flex; padding-left: 0; list-style: none;}.page-link{position: relative; display: block; color: #0d6efd; background-color: #fff; border: 1px solid #dee2e6; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.page-link{transition: none;}}.page-link:hover{z-index: 2; color: #0a58ca; background-color: #e9ecef; border-color: #dee2e6;}.page-link:focus{z-index: 3; color: #0a58ca; background-color: #e9ecef; outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}.page-item:not(:first-child) .page-link{margin-left: -1px;}.page-item.active .page-link{z-index: 3; color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.page-item.disabled .page-link{color: #6c757d; pointer-events: none; background-color: #fff; border-color: #dee2e6;}.page-link{padding: 0.375rem 0.75rem;}.page-item:first-child .page-link{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.page-item:last-child .page-link{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem;}.pagination-lg .page-link{padding: 0.75rem 1.5rem; font-size: 1.25rem;}.pagination-lg .page-item:first-child .page-link{border-top-left-radius: 0.3rem; border-bottom-left-radius: 0.3rem;}.pagination-lg .page-item:last-child .page-link{border-top-right-radius: 0.3rem; border-bottom-right-radius: 0.3rem;}.pagination-sm .page-link{padding: 0.25rem 0.5rem; font-size: 0.875rem;}.pagination-sm .page-item:first-child .page-link{border-top-left-radius: 0.2rem; border-bottom-left-radius: 0.2rem;}.pagination-sm .page-item:last-child .page-link{border-top-right-radius: 0.2rem; border-bottom-right-radius: 0.2rem;}.badge{display: inline-block; padding: 0.35em 0.65em; font-size: 0.75em; font-weight: 700; line-height: 1; color: #fff; text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: 0.25rem;}.badge:empty{display: none;}.btn .badge{position: relative; top: -1px;}.alert{position: relative; padding: 1rem 1rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.25rem;}.alert-heading{color: inherit;}.alert-link{font-weight: 700;}.alert-dismissible{padding-right: 3rem;}.alert-dismissible .btn-close{position: absolute; top: 0; right: 0; z-index: 2; padding: 1.25rem 1rem;}.alert-primary{color: #084298; background-color: #cfe2ff; border-color: #b6d4fe;}.alert-primary .alert-link{color: #06357a;}.alert-secondary{color: #41464b; background-color: #e2e3e5; border-color: #d3d6d8;}.alert-secondary .alert-link{color: #34383c;}.alert-success{color: #0f5132; background-color: #d1e7dd; border-color: #badbcc;}.alert-success .alert-link{color: #0c4128;}.alert-info{color: #055160; background-color: #cff4fc; border-color: #b6effb;}.alert-info .alert-link{color: #04414d;}.alert-warning{color: #664d03; background-color: #fff3cd; border-color: #ffecb5;}.alert-warning .alert-link{color: #523e02;}.alert-danger{color: #842029; background-color: #f8d7da; border-color: #f5c2c7;}.alert-danger .alert-link{color: #6a1a21;}.alert-light{color: #636464; background-color: #fefefe; border-color: #fdfdfe;}.alert-light .alert-link{color: #4f5050;}.alert-dark{color: #141619; background-color: #d3d3d4; border-color: #bcbebf;}.alert-dark .alert-link{color: #101214;}@keyframes progress-bar-stripes{0%{background-position-x: 1rem;}}.progress{display: -webkit-box; display: -webkit-flex; display: flex; height: 1rem; overflow: hidden; font-size: 0.75rem; background-color: #e9ecef; border-radius: 0.25rem;}.progress-bar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; overflow: hidden; color: #fff; text-align: center; white-space: nowrap; background-color: #0d6efd; transition: width 0.6s ease;}@media (prefers-reduced-motion: reduce){.progress-bar{transition: none;}}.progress-bar-striped{background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 1rem 1rem;}.progress-bar-animated{animation: 1s linear infinite progress-bar-stripes;}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation: none;}}.list-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; border-radius: 0.25rem;}.list-group-numbered{list-style-type: none; counter-reset: section;}.list-group-numbered > li::before{content: counters(section, ".") ". "; counter-increment: section;}.list-group-item-action{width: 100%; color: #495057; text-align: inherit;}.list-group-item-action:hover, .list-group-item-action:focus{z-index: 1; color: #495057; text-decoration: none; background-color: #f8f9fa;}.list-group-item-action:active{color: #212529; background-color: #e9ecef;}.list-group-item{position: relative; display: block; padding: 0.5rem 1rem; color: #212529; background-color: #fff; border: 1px solid rgba(0, 0, 0, 0.125);}.list-group-item:first-child{border-top-left-radius: inherit; border-top-right-radius: inherit;}.list-group-item:last-child{border-bottom-right-radius: inherit; border-bottom-left-radius: inherit;}.list-group-item.disabled, .list-group-item:disabled{color: #6c757d; pointer-events: none; background-color: #fff;}.list-group-item.active{z-index: 2; color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.list-group-item + .list-group-item{border-top-width: 0;}.list-group-item + .list-group-item.active{margin-top: -1px; border-top-width: 1px;}.list-group-horizontal{flex-direction: row;}.list-group-horizontal > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal > .list-group-item.active{margin-top: 0;}.list-group-horizontal > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction: row;}.list-group-horizontal-sm > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-sm > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-sm > .list-group-item.active{margin-top: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction: row;}.list-group-horizontal-md > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-md > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-md > .list-group-item.active{margin-top: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction: row;}.list-group-horizontal-lg > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-lg > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-lg > .list-group-item.active{margin-top: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction: row;}.list-group-horizontal-xl > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-xl > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-xl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 1400px){.list-group-horizontal-xxl{flex-direction: row;}.list-group-horizontal-xxl > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-xxl > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-xxl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}.list-group-flush{border-radius: 0;}.list-group-flush > .list-group-item{border-width: 0 0 1px;}.list-group-flush > .list-group-item:last-child{border-bottom-width: 0;}.list-group-item-primary{color: #084298; background-color: #cfe2ff;}.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus{color: #084298; background-color: #bacbe6;}.list-group-item-primary.list-group-item-action.active{color: #fff; background-color: #084298; border-color: #084298;}.list-group-item-secondary{color: #41464b; background-color: #e2e3e5;}.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus{color: #41464b; background-color: #cbccce;}.list-group-item-secondary.list-group-item-action.active{color: #fff; background-color: #41464b; border-color: #41464b;}.list-group-item-success{color: #0f5132; background-color: #d1e7dd;}.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus{color: #0f5132; background-color: #bcd0c7;}.list-group-item-success.list-group-item-action.active{color: #fff; background-color: #0f5132; border-color: #0f5132;}.list-group-item-info{color: #055160; background-color: #cff4fc;}.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus{color: #055160; background-color: #badce3;}.list-group-item-info.list-group-item-action.active{color: #fff; background-color: #055160; border-color: #055160;}.list-group-item-warning{color: #664d03; background-color: #fff3cd;}.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus{color: #664d03; background-color: #e6dbb9;}.list-group-item-warning.list-group-item-action.active{color: #fff; background-color: #664d03; border-color: #664d03;}.list-group-item-danger{color: #842029; background-color: #f8d7da;}.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus{color: #842029; background-color: #dfc2c4;}.list-group-item-danger.list-group-item-action.active{color: #fff; background-color: #842029; border-color: #842029;}.list-group-item-light{color: #636464; background-color: #fefefe;}.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus{color: #636464; background-color: #e5e5e5;}.list-group-item-light.list-group-item-action.active{color: #fff; background-color: #636464; border-color: #636464;}.list-group-item-dark{color: #141619; background-color: #d3d3d4;}.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus{color: #141619; background-color: #bebebf;}.list-group-item-dark.list-group-item-action.active{color: #fff; background-color: #141619; border-color: #141619;}.btn-close{box-sizing: content-box; width: 1em; height: 1em; padding: 0.25em 0.25em; color: #000; background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat; border: 0; border-radius: 0.25rem; opacity: 0.5;}.btn-close:hover{color: #000; text-decoration: none; opacity: 0.75;}.btn-close:focus{outline: 0; box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); opacity: 1;}.btn-close:disabled, .btn-close.disabled{pointer-events: none; user-select: none; opacity: 0.25;}.btn-close-white{filter: invert(1) grayscale(100%) brightness(200%);}.toast{width: 350px; max-width: 100%; font-size: 0.875rem; pointer-events: auto; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); border-radius: 0.25rem;}.toast.showing{opacity: 0;}.toast:not(.show){display: none;}.toast-container{width: max-content; max-width: 100%; pointer-events: none;}.toast-container > :not(:last-child){margin-bottom: 15px;}.toast-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.5rem 0.75rem; color: #6c757d; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border-bottom: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.toast-header .btn-close{margin-right: -0.375rem; margin-left: 0.75rem;}.toast-body{padding: 0.75rem; word-wrap: break-word;}.modal{position: fixed; top: 0; left: 0; z-index: 1055; display: none; width: 100%; height: 100%; overflow-x: hidden; overflow-y: auto; outline: 0;}.modal-dialog{position: relative; width: auto; margin: 0.5rem; pointer-events: none;}.modal.fade .modal-dialog{transition: transform 0.3s ease-out; transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition: none;}}.modal.show .modal-dialog{transform: none;}.modal.modal-static .modal-dialog{transform: scale(1.02);}.modal-dialog-scrollable{height: calc(100% - 1rem);}.modal-dialog-scrollable .modal-content{max-height: 100%; overflow: hidden;}.modal-dialog-scrollable .modal-body{overflow-y: auto;}.modal-dialog-centered{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: calc(100% - 1rem);}.modal-content{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; pointer-events: auto; background-color: #fff; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem; outline: 0;}.modal-backdrop{position: fixed; top: 0; left: 0; z-index: 1050; width: 100vw; height: 100vh; background-color: #000;}.modal-backdrop.fade{opacity: 0;}.modal-backdrop.show{opacity: 0.5;}.modal-header{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; align-items: center; justify-content: space-between; padding: 1rem 1rem; border-bottom: 1px solid #dee2e6; border-top-left-radius: calc(0.3rem - 1px); border-top-right-radius: calc(0.3rem - 1px);}.modal-header .btn-close{padding: 0.5rem 0.5rem; margin: -0.5rem -0.5rem -0.5rem auto;}.modal-title{margin-bottom: 0; line-height: 1.5;}.modal-body{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1rem;}.modal-footer{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; flex-shrink: 0; align-items: center; -webkit-box-pack: end; justify-content: flex-end; padding: 0.75rem; border-top: 1px solid #dee2e6; border-bottom-right-radius: calc(0.3rem - 1px); border-bottom-left-radius: calc(0.3rem - 1px);}.modal-footer > *{margin: 0.25rem;}@media (min-width: 576px){.modal-dialog{max-width: 500px; margin: 1.75rem auto;}.modal-dialog-scrollable{height: calc(100% - 3.5rem);}.modal-dialog-centered{min-height: calc(100% - 3.5rem);}.modal-sm{max-width: 300px;}}@media (min-width: 992px){.modal-lg, .modal-xl{max-width: 800px;}}@media (min-width: 1200px){.modal-xl{max-width: 1140px;}}.modal-fullscreen{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen .modal-header{border-radius: 0;}.modal-fullscreen .modal-body{overflow-y: auto;}.modal-fullscreen .modal-footer{border-radius: 0;}@media (max-width: 575.98px){.modal-fullscreen-sm-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-sm-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-sm-down .modal-header{border-radius: 0;}.modal-fullscreen-sm-down .modal-body{overflow-y: auto;}.modal-fullscreen-sm-down .modal-footer{border-radius: 0;}}@media (max-width: 767.98px){.modal-fullscreen-md-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-md-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-md-down .modal-header{border-radius: 0;}.modal-fullscreen-md-down .modal-body{overflow-y: auto;}.modal-fullscreen-md-down .modal-footer{border-radius: 0;}}@media (max-width: 991.98px){.modal-fullscreen-lg-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-lg-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-lg-down .modal-header{border-radius: 0;}.modal-fullscreen-lg-down .modal-body{overflow-y: auto;}.modal-fullscreen-lg-down .modal-footer{border-radius: 0;}}@media (max-width: 1199.98px){.modal-fullscreen-xl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xl-down .modal-header{border-radius: 0;}.modal-fullscreen-xl-down .modal-body{overflow-y: auto;}.modal-fullscreen-xl-down .modal-footer{border-radius: 0;}}@media (max-width: 1399.98px){.modal-fullscreen-xxl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xxl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xxl-down .modal-header{border-radius: 0;}.modal-fullscreen-xxl-down .modal-body{overflow-y: auto;}.modal-fullscreen-xxl-down .modal-footer{border-radius: 0;}}.tooltip{position: absolute; z-index: 1080; display: block; margin: 0; font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.875rem; word-wrap: break-word; opacity: 0;}.tooltip.show{opacity: 0.9;}.tooltip .tooltip-arrow{position: absolute; display: block; width: 0.8rem; height: 0.4rem;}.tooltip .tooltip-arrow::before{position: absolute; content: ""; border-color: transparent; border-style: solid;}.bs-tooltip-top, .bs-tooltip-auto[data-popper-placement^="top"]{padding: 0.4rem 0;}.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow{bottom: 0;}.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before{top: -1px; border-width: 0.4rem 0.4rem 0; border-top-color: #000;}.bs-tooltip-end, .bs-tooltip-auto[data-popper-placement^="right"]{padding: 0 0.4rem;}.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow{left: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before{right: -1px; border-width: 0.4rem 0.4rem 0.4rem 0; border-right-color: #000;}.bs-tooltip-bottom, .bs-tooltip-auto[data-popper-placement^="bottom"]{padding: 0.4rem 0;}.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow{top: 0;}.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before{bottom: -1px; border-width: 0 0.4rem 0.4rem; border-bottom-color: #000;}.bs-tooltip-start, .bs-tooltip-auto[data-popper-placement^="left"]{padding: 0 0.4rem;}.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow{right: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before{left: -1px; border-width: 0.4rem 0 0.4rem 0.4rem; border-left-color: #000;}.tooltip-inner{max-width: 200px; padding: 0.25rem 0.5rem; color: #fff; text-align: center; background-color: #000; border-radius: 0.25rem;}.popover{position: absolute; top: 0; left: 0 ; z-index: 1070; display: block; max-width: 276px; font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.875rem; word-wrap: break-word; background-color: #fff; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem;}.popover .popover-arrow{position: absolute; display: block; width: 1rem; height: 0.5rem;}.popover .popover-arrow::before, .popover .popover-arrow::after{position: absolute; display: block; content: ""; border-color: transparent; border-style: solid;}.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow{bottom: calc(-0.5rem - 1px);}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before{bottom: 0; border-width: 0.5rem 0.5rem 0; border-top-color: rgba(0, 0, 0, 0.25);}.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{bottom: 1px; border-width: 0.5rem 0.5rem 0; border-top-color: #fff;}.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow{left: calc(-0.5rem - 1px); width: 0.5rem; height: 1rem;}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before{left: 0; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: rgba(0, 0, 0, 0.25);}.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{left: 1px; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: #fff;}.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow{top: calc(-0.5rem - 1px);}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before{top: 0; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: rgba(0, 0, 0, 0.25);}.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{top: 1px; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: #fff;}.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before{position: absolute; top: 0; left: 50%; display: block; width: 1rem; margin-left: -0.5rem; content: ""; border-bottom: 1px solid #f0f0f0;}.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow{right: calc(-0.5rem - 1px); width: 0.5rem; height: 1rem;}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before{right: 0; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: rgba(0, 0, 0, 0.25);}.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{right: 1px; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: #fff;}.popover-header{padding: 0.5rem 1rem; margin-bottom: 0; font-size: 1rem; background-color: #f0f0f0; border-bottom: 1px solid rgba(0, 0, 0, 0.2); border-top-left-radius: calc(0.3rem - 1px); border-top-right-radius: calc(0.3rem - 1px);}.popover-header:empty{display: none;}.popover-body{padding: 1rem 1rem; color: #212529;}.carousel{position: relative;}.carousel.pointer-event{touch-action: pan-y;}.carousel-inner{position: relative; width: 100%; overflow: hidden;}.carousel-inner::after{display: block; clear: both; content: "";}.carousel-item{position: relative; display: none; float: left; width: 100%; margin-right: -100%; backface-visibility: hidden; transition: transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce){.carousel-item{transition: none;}}.carousel-item.active, .carousel-item-next, .carousel-item-prev{display: block;}.carousel-item-next:not(.carousel-item-start), .active.carousel-item-end{transform: translateX(100%);}.carousel-item-prev:not(.carousel-item-end), .active.carousel-item-start{transform: translateX(-100%);}.carousel-fade .carousel-item{opacity: 0; transition-property: opacity; transform: none;}.carousel-fade .carousel-item.active, .carousel-fade .carousel-item-next.carousel-item-start, .carousel-fade .carousel-item-prev.carousel-item-end{z-index: 1; opacity: 1;}.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{z-index: 0; opacity: 0; transition: opacity 0s 0.6s;}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{transition: none;}}.carousel-control-prev, .carousel-control-next{position: absolute; top: 0; bottom: 0; z-index: 1; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: 15%; padding: 0; color: #fff; text-align: center; background: none; border: 0; opacity: 0.5; transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce){.carousel-control-prev, .carousel-control-next{transition: none;}}.carousel-control-prev:hover, .carousel-control-prev:focus, .carousel-control-next:hover, .carousel-control-next:focus{color: #fff; text-decoration: none; outline: 0; opacity: 0.9;}.carousel-control-prev{left: 0;}.carousel-control-next{right: 0;}.carousel-control-prev-icon, .carousel-control-next-icon{display: inline-block; width: 2rem; height: 2rem; background-repeat: no-repeat; background-position: 50%; background-size: 100% 100%;}.carousel-control-prev-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");}.carousel-control-next-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");}.carousel-indicators{position: absolute; right: 0; bottom: 0; left: 0; z-index: 2; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; padding: 0; margin-right: 15%; margin-bottom: 1rem; margin-left: 15%; list-style: none;}.carousel-indicators [data-bs-target]{box-sizing: content-box; -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 30px; height: 3px; padding: 0; margin-right: 3px; margin-left: 3px; text-indent: -999px; cursor: pointer; background-color: #fff; background-clip: padding-box; border: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; opacity: 0.5; transition: opacity 0.6s ease;}@media (prefers-reduced-motion: reduce){.carousel-indicators [data-bs-target]{transition: none;}}.carousel-indicators .active{opacity: 1;}.carousel-caption{position: absolute; right: 15%; bottom: 1.25rem; left: 15%; padding-top: 1.25rem; padding-bottom: 1.25rem; color: #fff; text-align: center;}.carousel-dark .carousel-control-prev-icon, .carousel-dark .carousel-control-next-icon{filter: invert(1) grayscale(100);}.carousel-dark .carousel-indicators [data-bs-target]{background-color: #000;}.carousel-dark .carousel-caption{color: #000;}@keyframes spinner-border{to{transform: rotate(360deg) ;}}.spinner-border{display: inline-block; width: 2rem; height: 2rem; vertical-align: -0.125em; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: 0.75s linear infinite spinner-border;}.spinner-border-sm{width: 1rem; height: 1rem; border-width: 0.2em;}@keyframes spinner-grow{0%{transform: scale(0);}50%{opacity: 1; transform: none;}}.spinner-grow{display: inline-block; width: 2rem; height: 2rem; vertical-align: -0.125em; background-color: currentColor; border-radius: 50%; opacity: 0; animation: 0.75s linear infinite spinner-grow;}.spinner-grow-sm{width: 1rem; height: 1rem;}@media (prefers-reduced-motion: reduce){.spinner-border, .spinner-grow{animation-duration: 1.5s;}}.offcanvas{position: fixed; bottom: 0; z-index: 1045; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; visibility: hidden; background-color: #fff; background-clip: padding-box; outline: 0; transition: transform 0.3s ease-in-out;}@media (prefers-reduced-motion: reduce){.offcanvas{transition: none;}}.offcanvas-backdrop{position: fixed; top: 0; left: 0; z-index: 1040; width: 100vw; height: 100vh; background-color: #000;}.offcanvas-backdrop.fade{opacity: 0;}.offcanvas-backdrop.show{opacity: 0.5;}.offcanvas-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: space-between; padding: 1rem 1rem;}.offcanvas-header .btn-close{padding: 0.5rem 0.5rem; margin-top: -0.5rem; margin-right: -0.5rem; margin-bottom: -0.5rem;}.offcanvas-title{margin-bottom: 0; line-height: 1.5;}.offcanvas-body{flex-grow: 1; padding: 1rem 1rem; overflow-y: auto;}.offcanvas-start{top: 0; left: 0; width: 400px; border-right: 1px solid rgba(0, 0, 0, 0.2); transform: translateX(-100%);}.offcanvas-end{top: 0; right: 0; width: 400px; border-left: 1px solid rgba(0, 0, 0, 0.2); transform: translateX(100%);}.offcanvas-top{top: 0; right: 0; left: 0; height: 30vh; max-height: 100%; border-bottom: 1px solid rgba(0, 0, 0, 0.2); transform: translateY(-100%);}.offcanvas-bottom{right: 0; left: 0; height: 30vh; max-height: 100%; border-top: 1px solid rgba(0, 0, 0, 0.2); transform: translateY(100%);}.offcanvas.show{transform: none;}.placeholder{display: inline-block; min-height: 1em; vertical-align: middle; cursor: wait; background-color: currentColor; opacity: 0.5;}.placeholder.btn::before{display: inline-block; content: "";}.placeholder-xs{min-height: .6em;}.placeholder-sm{min-height: .8em;}.placeholder-lg{min-height: 1.2em;}.placeholder-glow .placeholder{animation: placeholder-glow 2s ease-in-out infinite;}@keyframes placeholder-glow{50%{opacity: 0.2;}}.placeholder-wave{mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%); mask-size: 200% 100%; animation: placeholder-wave 2s linear infinite;}@keyframes placeholder-wave{100%{mask-position: -200% 0%;}}.clearfix::after{display: block; clear: both; content: "";}.link-primary{color: #0d6efd;}.link-primary:hover, .link-primary:focus{color: #0a58ca;}.link-secondary{color: #6c757d;}.link-secondary:hover, .link-secondary:focus{color: #565e64;}.link-success{color: #198754;}.link-success:hover, .link-success:focus{color: #146c43;}.link-info{color: #0dcaf0;}.link-info:hover, .link-info:focus{color: #3dd5f3;}.link-warning{color: #ffc107;}.link-warning:hover, .link-warning:focus{color: #ffcd39;}.link-danger{color: #dc3545;}.link-danger:hover, .link-danger:focus{color: #b02a37;}.link-light{color: #f8f9fa;}.link-light:hover, .link-light:focus{color: #f9fafb;}.link-dark{color: #212529;}.link-dark:hover, .link-dark:focus{color: #1a1e21;}.ratio{position: relative; width: 100%;}.ratio::before{display: block; padding-top: var(--bs-aspect-ratio); content: "";}.ratio > *{position: absolute; top: 0; left: 0; width: 100%; height: 100%;}.ratio-1x1{--bs-aspect-ratio: 100%;}.ratio-4x3{--bs-aspect-ratio: calc(3 / 4 * 100%);}.ratio-16x9{--bs-aspect-ratio: calc(9 / 16 * 100%);}.ratio-21x9{--bs-aspect-ratio: calc(9 / 21 * 100%);}.fixed-top{position: fixed; top: 0; right: 0; left: 0; z-index: 1030;}.fixed-bottom{position: fixed; right: 0; bottom: 0; left: 0; z-index: 1030;}.sticky-top{position: sticky; top: 0; z-index: 1020;}@media (min-width: 576px){.sticky-sm-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 768px){.sticky-md-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 992px){.sticky-lg-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 1200px){.sticky-xl-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 1400px){.sticky-xxl-top{position: sticky; top: 0; z-index: 1020;}}.hstack{display: -webkit-box; display: -webkit-flex; display: flex; flex-direction: row; align-items: center; align-self: stretch;}.vstack{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-self: stretch;}.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within){position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0, 0, 0, 0) !important; white-space: nowrap !important; border: 0 !important;}.stretched-link::after{position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; content: "";}.text-truncate{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.vr{display: inline-block; align-self: stretch; width: 1px; min-height: 1em; background-color: currentColor; opacity: 0.25;}

/* /web/static/src/scss/helpers_backport.scss */
 .text-bg-primary{color: #fff !important; background-color: RGBA(13, 110, 253, var(--bg-opacity, 1)) !important;}.text-bg-secondary{color: #fff !important; background-color: RGBA(108, 117, 125, var(--bg-opacity, 1)) !important;}.text-bg-success{color: #fff !important; background-color: RGBA(25, 135, 84, var(--bg-opacity, 1)) !important;}.text-bg-info{color: #000 !important; background-color: RGBA(13, 202, 240, var(--bg-opacity, 1)) !important;}.text-bg-warning{color: #000 !important; background-color: RGBA(255, 193, 7, var(--bg-opacity, 1)) !important;}.text-bg-danger{color: #fff !important; background-color: RGBA(220, 53, 69, var(--bg-opacity, 1)) !important;}.text-bg-light{color: #000 !important; background-color: RGBA(248, 249, 250, var(--bg-opacity, 1)) !important;}.text-bg-dark{color: #fff !important; background-color: RGBA(33, 37, 41, var(--bg-opacity, 1)) !important;}

/* /web/static/src/scss/utilities_custom.scss */
 .opacity-0-hover:hover, .opacity-trigger-hover:hover .opacity-0-hover{opacity: 0 !important;}.opacity-25-hover:hover, .opacity-trigger-hover:hover .opacity-25-hover{opacity: 0.25 !important;}.opacity-50-hover:hover, .opacity-trigger-hover:hover .opacity-50-hover{opacity: 0.5 !important;}.opacity-75-hover:hover, .opacity-trigger-hover:hover .opacity-75-hover{opacity: 0.75 !important;}.opacity-100-hover:hover, .opacity-trigger-hover:hover .opacity-100-hover{opacity: 1 !important;}.opacity-disabled-hover:hover, .opacity-trigger-hover:hover .opacity-disabled-hover{opacity: 0.5 !important;}

/* /web/static/lib/bootstrap/scss/utilities/_api.scss */
 .align-baseline{vertical-align: baseline !important;}.align-top{vertical-align: top !important;}.align-middle{vertical-align: middle !important;}.align-bottom{vertical-align: bottom !important;}.align-text-bottom{vertical-align: text-bottom !important;}.align-text-top{vertical-align: text-top !important;}.float-start{float: left !important;}.float-end{float: right !important;}.float-none{float: none !important;}.opacity-0{opacity: 0 !important;}.opacity-25{opacity: 0.25 !important;}.opacity-50{opacity: 0.5 !important;}.opacity-75{opacity: 0.75 !important;}.opacity-100{opacity: 1 !important;}.opacity-disabled{opacity: 0.5 !important;}.overflow-auto{overflow: auto !important;}.overflow-hidden{overflow: hidden !important;}.overflow-visible{overflow: visible !important;}.overflow-scroll{overflow: scroll !important;}.d-inline{display: inline !important;}.d-inline-block{display: inline-block !important;}.d-block{display: block !important;}.d-grid{display: grid !important;}.d-table{display: table !important;}.d-table-row{display: table-row !important;}.d-table-cell{display: table-cell !important;}.d-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-none{display: none !important;}.d-contents{display: contents !important;}.shadow{box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;}.shadow-sm{box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;}.shadow-lg{box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;}.shadow-none{box-shadow: none !important;}.position-static{position: static !important;}.position-relative{position: relative !important;}.position-absolute{position: absolute !important;}.position-fixed{position: fixed !important;}.position-sticky{position: sticky !important;}.top-0{top: 0 !important;}.top-50{top: 50% !important;}.top-100{top: 100% !important;}.bottom-0{bottom: 0 !important;}.bottom-50{bottom: 50% !important;}.bottom-100{bottom: 100% !important;}.start-0{left: 0 !important;}.start-50{left: 50% !important;}.start-100{left: 100% !important;}.end-0{right: 0 !important;}.end-50{right: 50% !important;}.end-100{right: 100% !important;}.translate-middle{transform: translate(-50%, -50%) !important;}.translate-middle-x{transform: translateX(-50%) !important;}.translate-middle-y{transform: translateY(-50%) !important;}.border{border: 1px solid #dee2e6 !important;}.border-0{border: 0 !important;}.border-top{border-top: 1px solid #dee2e6 !important;}.border-top-0{border-top: 0 !important;}.border-end{border-right: 1px solid #dee2e6 !important;}.border-end-0{border-right: 0 !important;}.border-bottom{border-bottom: 1px solid #dee2e6 !important;}.border-bottom-0{border-bottom: 0 !important;}.border-start{border-left: 1px solid #dee2e6 !important;}.border-start-0{border-left: 0 !important;}.border-primary{border-color: #0d6efd !important;}.border-secondary{border-color: #6c757d !important;}.border-success{border-color: #198754 !important;}.border-info{border-color: #0dcaf0 !important;}.border-warning{border-color: #ffc107 !important;}.border-danger{border-color: #dc3545 !important;}.border-light{border-color: #f8f9fa !important;}.border-dark{border-color: #212529 !important;}.border-white{border-color: #fff !important;}.border-transparent{border-color: transparent !important;}.border-1{border-width: 1px !important;}.border-2{border-width: 2px !important;}.border-3{border-width: 3px !important;}.border-4{border-width: 4px !important;}.border-5{border-width: 5px !important;}.w-0{width: 0 !important;}.w-25{width: 25% !important;}.w-50{width: 50% !important;}.w-75{width: 75% !important;}.w-100{width: 100% !important;}.w-auto{width: auto !important;}.mw-0{max-width: 0 !important;}.mw-25{max-width: 25% !important;}.mw-50{max-width: 50% !important;}.mw-75{max-width: 75% !important;}.mw-100{max-width: 100% !important;}.mw-auto{max-width: auto !important;}.vw-100{width: 100vw !important;}.min-vw-100{min-width: 100vw !important;}.h-0{height: 0 !important;}.h-25{height: 25% !important;}.h-50{height: 50% !important;}.h-75{height: 75% !important;}.h-100{height: 100% !important;}.h-auto{height: auto !important;}.mh-0{max-height: 0 !important;}.mh-25{max-height: 25% !important;}.mh-50{max-height: 50% !important;}.mh-75{max-height: 75% !important;}.mh-100{max-height: 100% !important;}.mh-auto{max-height: auto !important;}.vh-100{height: 100vh !important;}.min-vh-100{min-height: 100vh !important;}.flex-fill{flex: 1 1 auto !important;}.flex-row{flex-direction: row !important;}.flex-column{flex-direction: column !important;}.flex-row-reverse{flex-direction: row-reverse !important;}.flex-column-reverse{flex-direction: column-reverse !important;}.flex-grow-0{flex-grow: 0 !important;}.flex-grow-1{flex-grow: 1 !important;}.flex-shrink-0{flex-shrink: 0 !important;}.flex-shrink-1{flex-shrink: 1 !important;}.flex-wrap{flex-wrap: wrap !important;}.flex-nowrap{flex-wrap: nowrap !important;}.flex-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-0{gap: 0 !important;}.gap-1{gap: 0.25rem !important;}.gap-2{gap: 0.5rem !important;}.gap-3{gap: 1rem !important;}.gap-4{gap: 1.5rem !important;}.gap-5{gap: 3rem !important;}.justify-content-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-center{justify-content: center !important;}.justify-content-between{justify-content: space-between !important;}.justify-content-around{justify-content: space-around !important;}.justify-content-evenly{justify-content: space-evenly !important;}.align-items-start{align-items: flex-start !important;}.align-items-end{align-items: flex-end !important;}.align-items-center{align-items: center !important;}.align-items-baseline{align-items: baseline !important;}.align-items-stretch{align-items: stretch !important;}.align-content-start{align-content: flex-start !important;}.align-content-end{align-content: flex-end !important;}.align-content-center{align-content: center !important;}.align-content-between{align-content: space-between !important;}.align-content-around{align-content: space-around !important;}.align-content-stretch{align-content: stretch !important;}.align-self-auto{align-self: auto !important;}.align-self-start{align-self: flex-start !important;}.align-self-end{align-self: flex-end !important;}.align-self-center{align-self: center !important;}.align-self-baseline{align-self: baseline !important;}.align-self-stretch{align-self: stretch !important;}.order-first{order: -1 !important;}.order-last{order: 13 !important;}.order-0{order: 0 !important;}.order-1{order: 1 !important;}.order-2{order: 2 !important;}.order-3{order: 3 !important;}.order-4{order: 4 !important;}.order-5{order: 5 !important;}.order-6{order: 6 !important;}.order-7{order: 7 !important;}.order-8{order: 8 !important;}.order-9{order: 9 !important;}.order-10{order: 10 !important;}.order-11{order: 11 !important;}.order-12{order: 12 !important;}.m-0{margin: 0 !important;}.m-1{margin: 0.25rem !important;}.m-2{margin: 0.5rem !important;}.m-3{margin: 1rem !important;}.m-4{margin: 1.5rem !important;}.m-5{margin: 3rem !important;}.m-auto{margin: auto !important;}.mx-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-auto{margin-right: auto !important; margin-left: auto !important;}.my-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-0{margin-top: 0 !important;}.mt-1{margin-top: 0.25rem !important;}.mt-2{margin-top: 0.5rem !important;}.mt-3{margin-top: 1rem !important;}.mt-4{margin-top: 1.5rem !important;}.mt-5{margin-top: 3rem !important;}.mt-auto{margin-top: auto !important;}.me-0{margin-right: 0 !important;}.me-1{margin-right: 0.25rem !important;}.me-2{margin-right: 0.5rem !important;}.me-3{margin-right: 1rem !important;}.me-4{margin-right: 1.5rem !important;}.me-5{margin-right: 3rem !important;}.me-auto{margin-right: auto !important;}.mb-0{margin-bottom: 0 !important;}.mb-1{margin-bottom: 0.25rem !important;}.mb-2{margin-bottom: 0.5rem !important;}.mb-3{margin-bottom: 1rem !important;}.mb-4{margin-bottom: 1.5rem !important;}.mb-5{margin-bottom: 3rem !important;}.mb-auto{margin-bottom: auto !important;}.ms-0{margin-left: 0 !important;}.ms-1{margin-left: 0.25rem !important;}.ms-2{margin-left: 0.5rem !important;}.ms-3{margin-left: 1rem !important;}.ms-4{margin-left: 1.5rem !important;}.ms-5{margin-left: 3rem !important;}.ms-auto{margin-left: auto !important;}.p-0{padding: 0 !important;}.p-1{padding: 0.25rem !important;}.p-2{padding: 0.5rem !important;}.p-3{padding: 1rem !important;}.p-4{padding: 1.5rem !important;}.p-5{padding: 3rem !important;}.px-0{padding-right: 0 !important; padding-left: 0 !important;}.px-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-0{padding-top: 0 !important;}.pt-1{padding-top: 0.25rem !important;}.pt-2{padding-top: 0.5rem !important;}.pt-3{padding-top: 1rem !important;}.pt-4{padding-top: 1.5rem !important;}.pt-5{padding-top: 3rem !important;}.pe-0{padding-right: 0 !important;}.pe-1{padding-right: 0.25rem !important;}.pe-2{padding-right: 0.5rem !important;}.pe-3{padding-right: 1rem !important;}.pe-4{padding-right: 1.5rem !important;}.pe-5{padding-right: 3rem !important;}.pb-0{padding-bottom: 0 !important;}.pb-1{padding-bottom: 0.25rem !important;}.pb-2{padding-bottom: 0.5rem !important;}.pb-3{padding-bottom: 1rem !important;}.pb-4{padding-bottom: 1.5rem !important;}.pb-5{padding-bottom: 3rem !important;}.ps-0{padding-left: 0 !important;}.ps-1{padding-left: 0.25rem !important;}.ps-2{padding-left: 0.5rem !important;}.ps-3{padding-left: 1rem !important;}.ps-4{padding-left: 1.5rem !important;}.ps-5{padding-left: 3rem !important;}.font-monospace{font-family: var(--bs-font-monospace) !important;}.font-sans-serif{font-family: var(--bs-font-sans-serif) !important;}.fs-1{font-size: calc(1.375rem + 1.5vw) !important;}.fs-2{font-size: calc(1.325rem + 0.9vw) !important;}.fs-3{font-size: calc(1.3rem + 0.6vw) !important;}.fs-4{font-size: calc(1.275rem + 0.3vw) !important;}.fs-5{font-size: 1.25rem !important;}.fs-6{font-size: 1rem !important;}.fst-italic{font-style: italic !important;}.fst-normal{font-style: normal !important;}.fw-light{font-weight: 300 !important;}.fw-lighter{font-weight: lighter !important;}.fw-normal{font-weight: 400 !important;}.fw-bold{font-weight: 700 !important;}.fw-bolder{font-weight: bolder !important;}.lh-1{line-height: 1 !important;}.lh-sm{line-height: 1.25 !important;}.lh-base{line-height: 1.5 !important;}.lh-lg{line-height: 2 !important;}.text-start{text-align: left !important;}.text-end{text-align: right !important;}.text-center{text-align: center !important;}.text-decoration-none{text-decoration: none !important;}.text-decoration-underline{text-decoration: underline !important;}.text-decoration-line-through{text-decoration: line-through !important;}.text-lowercase{text-transform: lowercase !important;}.text-uppercase{text-transform: uppercase !important;}.text-capitalize{text-transform: capitalize !important;}.text-wrap{white-space: normal !important;}.text-nowrap{white-space: nowrap !important;}.text-break{word-wrap: break-word !important; word-break: break-word !important;}.text-body{--bs-text-opacity: 1; color: #212529 !important;}.text-black{--bs-text-opacity: 1; color: #000 !important;}.text-white{--bs-text-opacity: 1; color: #fff !important;}.text-muted{--bs-text-opacity: 1; color: #6c757d !important;}.text-black-50{--bs-text-opacity: 1; color: rgba(0, 0, 0, 0.5) !important;}.text-white-50{--bs-text-opacity: 1; color: rgba(255, 255, 255, 0.5) !important;}.text-reset{--bs-text-opacity: 1; color: inherit !important;}.text-opacity-25{--bs-text-opacity: 0.25;}.text-opacity-50{--bs-text-opacity: 0.5;}.text-opacity-75{--bs-text-opacity: 0.75;}.text-opacity-100{--bs-text-opacity: 1;}.bg-primary{--bs-bg-opacity: 1; background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;}.bg-secondary{--bs-bg-opacity: 1; background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;}.bg-success, .o_status.o_status_green{--bs-bg-opacity: 1; background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;}.bg-info{--bs-bg-opacity: 1; background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;}.bg-warning{--bs-bg-opacity: 1; background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;}.bg-danger, .o_status.o_status_red{--bs-bg-opacity: 1; background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;}.bg-light{--bs-bg-opacity: 1; background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;}.bg-dark{--bs-bg-opacity: 1; background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;}.bg-black{--bs-bg-opacity: 1; background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;}.bg-white{--bs-bg-opacity: 1; background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;}.bg-body{--bs-bg-opacity: 1; background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;}.bg-transparent{--bs-bg-opacity: 1; background-color: transparent !important;}.bg-view{--bs-bg-opacity: 1; background-color: white !important;}.bg-opacity-0{--bs-bg-opacity: 0;}.bg-opacity-25{--bs-bg-opacity: 0.25;}.bg-opacity-50{--bs-bg-opacity: 0.5;}.bg-opacity-75{--bs-bg-opacity: 0.75;}.bg-opacity-100{--bs-bg-opacity: 1;}.bg-opacity-disabled{--bs-bg-opacity: 0.5;}.bg-gradient{background-image: var(--bs-gradient) !important;}.user-select-all{user-select: all !important;}.user-select-auto{user-select: auto !important;}.user-select-none{user-select: none !important;}.pe-none{pointer-events: none !important;}.pe-auto{pointer-events: auto !important;}.rounded{border-radius: 0.25rem !important;}.rounded-0{border-radius: 0 !important;}.rounded-1{border-radius: 0.2rem !important;}.rounded-2{border-radius: 0.25rem !important;}.rounded-3{border-radius: 0.3rem !important;}.rounded-circle, .o_status{border-radius: 50% !important;}.rounded-pill{border-radius: 50rem !important;}.rounded-top{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-0{border-top-left-radius: 0 !important; border-top-right-radius: 0 !important;}.rounded-top-1{border-top-left-radius: 0.2rem !important; border-top-right-radius: 0.2rem !important;}.rounded-top-2{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-3{border-top-left-radius: 0.3rem !important; border-top-right-radius: 0.3rem !important;}.rounded-top-circle{border-top-left-radius: 50% !important; border-top-right-radius: 50% !important;}.rounded-top-pill{border-top-left-radius: 50rem !important; border-top-right-radius: 50rem !important;}.rounded-end{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-0{border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;}.rounded-end-1{border-top-right-radius: 0.2rem !important; border-bottom-right-radius: 0.2rem !important;}.rounded-end-2{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-3{border-top-right-radius: 0.3rem !important; border-bottom-right-radius: 0.3rem !important;}.rounded-end-circle{border-top-right-radius: 50% !important; border-bottom-right-radius: 50% !important;}.rounded-end-pill{border-top-right-radius: 50rem !important; border-bottom-right-radius: 50rem !important;}.rounded-bottom{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-0{border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;}.rounded-bottom-1{border-bottom-right-radius: 0.2rem !important; border-bottom-left-radius: 0.2rem !important;}.rounded-bottom-2{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-3{border-bottom-right-radius: 0.3rem !important; border-bottom-left-radius: 0.3rem !important;}.rounded-bottom-circle{border-bottom-right-radius: 50% !important; border-bottom-left-radius: 50% !important;}.rounded-bottom-pill{border-bottom-right-radius: 50rem !important; border-bottom-left-radius: 50rem !important;}.rounded-start{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-0{border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important;}.rounded-start-1{border-bottom-left-radius: 0.2rem !important; border-top-left-radius: 0.2rem !important;}.rounded-start-2{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-3{border-bottom-left-radius: 0.3rem !important; border-top-left-radius: 0.3rem !important;}.rounded-start-circle{border-bottom-left-radius: 50% !important; border-top-left-radius: 50% !important;}.rounded-start-pill{border-bottom-left-radius: 50rem !important; border-top-left-radius: 50rem !important;}.visible{visibility: visible !important;}.invisible{visibility: hidden !important;}.cursor-default{cursor: default !important;}.cursor-pointer{cursor: pointer !important;}.flex-basis-0{flex-basis: 0 !important;}.flex-basis-25{flex-basis: 25% !important;}.flex-basis-50{flex-basis: 50% !important;}.flex-basis-75{flex-basis: 75% !important;}.flex-basis-100{flex-basis: 100% !important;}.flex-basis-auto{flex-basis: auto !important;}.z-index-0{z-index: 0 !important;}.z-index-1{z-index: 1 !important;}.overflow-x-auto{overflow-x: auto !important;}.overflow-x-hidden{overflow-x: hidden !important;}.overflow-x-visible{overflow-x: visible !important;}.overflow-x-scroll{overflow-x: scroll !important;}.overflow-y-auto{overflow-y: auto !important;}.overflow-y-hidden{overflow-y: hidden !important;}.overflow-y-visible{overflow-y: visible !important;}.overflow-y-scroll{overflow-y: scroll !important;}.transition-none{transition: none !important;}.transition-base{transition: all 0.2s ease-in-out !important;}.transition-fade{transition: opacity 0.15s linear !important;}.min-w-0{min-width: 0 !important;}@media (min-width: 576px){.float-sm-start{float: left !important;}.float-sm-end{float: right !important;}.float-sm-none{float: none !important;}.d-sm-inline{display: inline !important;}.d-sm-inline-block{display: inline-block !important;}.d-sm-block{display: block !important;}.d-sm-grid{display: grid !important;}.d-sm-table{display: table !important;}.d-sm-table-row{display: table-row !important;}.d-sm-table-cell{display: table-cell !important;}.d-sm-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-sm-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-sm-none{display: none !important;}.d-sm-contents{display: contents !important;}.position-sm-static{position: static !important;}.position-sm-relative{position: relative !important;}.position-sm-absolute{position: absolute !important;}.position-sm-fixed{position: fixed !important;}.position-sm-sticky{position: sticky !important;}.w-sm-0{width: 0 !important;}.w-sm-25{width: 25% !important;}.w-sm-50{width: 50% !important;}.w-sm-75{width: 75% !important;}.w-sm-100{width: 100% !important;}.w-sm-auto{width: auto !important;}.mw-sm-0{max-width: 0 !important;}.mw-sm-25{max-width: 25% !important;}.mw-sm-50{max-width: 50% !important;}.mw-sm-75{max-width: 75% !important;}.mw-sm-100{max-width: 100% !important;}.mw-sm-auto{max-width: auto !important;}.h-sm-0{height: 0 !important;}.h-sm-25{height: 25% !important;}.h-sm-50{height: 50% !important;}.h-sm-75{height: 75% !important;}.h-sm-100{height: 100% !important;}.h-sm-auto{height: auto !important;}.mh-sm-0{max-height: 0 !important;}.mh-sm-25{max-height: 25% !important;}.mh-sm-50{max-height: 50% !important;}.mh-sm-75{max-height: 75% !important;}.mh-sm-100{max-height: 100% !important;}.mh-sm-auto{max-height: auto !important;}.flex-sm-fill{flex: 1 1 auto !important;}.flex-sm-row{flex-direction: row !important;}.flex-sm-column{flex-direction: column !important;}.flex-sm-row-reverse{flex-direction: row-reverse !important;}.flex-sm-column-reverse{flex-direction: column-reverse !important;}.flex-sm-grow-0{flex-grow: 0 !important;}.flex-sm-grow-1{flex-grow: 1 !important;}.flex-sm-shrink-0{flex-shrink: 0 !important;}.flex-sm-shrink-1{flex-shrink: 1 !important;}.flex-sm-wrap{flex-wrap: wrap !important;}.flex-sm-nowrap{flex-wrap: nowrap !important;}.flex-sm-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-sm-0{gap: 0 !important;}.gap-sm-1{gap: 0.25rem !important;}.gap-sm-2{gap: 0.5rem !important;}.gap-sm-3{gap: 1rem !important;}.gap-sm-4{gap: 1.5rem !important;}.gap-sm-5{gap: 3rem !important;}.justify-content-sm-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-sm-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-sm-center{justify-content: center !important;}.justify-content-sm-between{justify-content: space-between !important;}.justify-content-sm-around{justify-content: space-around !important;}.justify-content-sm-evenly{justify-content: space-evenly !important;}.align-items-sm-start{align-items: flex-start !important;}.align-items-sm-end{align-items: flex-end !important;}.align-items-sm-center{align-items: center !important;}.align-items-sm-baseline{align-items: baseline !important;}.align-items-sm-stretch{align-items: stretch !important;}.align-content-sm-start{align-content: flex-start !important;}.align-content-sm-end{align-content: flex-end !important;}.align-content-sm-center{align-content: center !important;}.align-content-sm-between{align-content: space-between !important;}.align-content-sm-around{align-content: space-around !important;}.align-content-sm-stretch{align-content: stretch !important;}.align-self-sm-auto{align-self: auto !important;}.align-self-sm-start{align-self: flex-start !important;}.align-self-sm-end{align-self: flex-end !important;}.align-self-sm-center{align-self: center !important;}.align-self-sm-baseline{align-self: baseline !important;}.align-self-sm-stretch{align-self: stretch !important;}.order-sm-first{order: -1 !important;}.order-sm-last{order: 13 !important;}.order-sm-0{order: 0 !important;}.order-sm-1{order: 1 !important;}.order-sm-2{order: 2 !important;}.order-sm-3{order: 3 !important;}.order-sm-4{order: 4 !important;}.order-sm-5{order: 5 !important;}.order-sm-6{order: 6 !important;}.order-sm-7{order: 7 !important;}.order-sm-8{order: 8 !important;}.order-sm-9{order: 9 !important;}.order-sm-10{order: 10 !important;}.order-sm-11{order: 11 !important;}.order-sm-12{order: 12 !important;}.m-sm-0{margin: 0 !important;}.m-sm-1{margin: 0.25rem !important;}.m-sm-2{margin: 0.5rem !important;}.m-sm-3{margin: 1rem !important;}.m-sm-4{margin: 1.5rem !important;}.m-sm-5{margin: 3rem !important;}.m-sm-auto{margin: auto !important;}.mx-sm-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-sm-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-sm-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-sm-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-sm-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-sm-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-sm-auto{margin-right: auto !important; margin-left: auto !important;}.my-sm-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-sm-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-sm-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-sm-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-sm-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-sm-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-sm-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-sm-0{margin-top: 0 !important;}.mt-sm-1{margin-top: 0.25rem !important;}.mt-sm-2{margin-top: 0.5rem !important;}.mt-sm-3{margin-top: 1rem !important;}.mt-sm-4{margin-top: 1.5rem !important;}.mt-sm-5{margin-top: 3rem !important;}.mt-sm-auto{margin-top: auto !important;}.me-sm-0{margin-right: 0 !important;}.me-sm-1{margin-right: 0.25rem !important;}.me-sm-2{margin-right: 0.5rem !important;}.me-sm-3{margin-right: 1rem !important;}.me-sm-4{margin-right: 1.5rem !important;}.me-sm-5{margin-right: 3rem !important;}.me-sm-auto{margin-right: auto !important;}.mb-sm-0{margin-bottom: 0 !important;}.mb-sm-1{margin-bottom: 0.25rem !important;}.mb-sm-2{margin-bottom: 0.5rem !important;}.mb-sm-3{margin-bottom: 1rem !important;}.mb-sm-4{margin-bottom: 1.5rem !important;}.mb-sm-5{margin-bottom: 3rem !important;}.mb-sm-auto{margin-bottom: auto !important;}.ms-sm-0{margin-left: 0 !important;}.ms-sm-1{margin-left: 0.25rem !important;}.ms-sm-2{margin-left: 0.5rem !important;}.ms-sm-3{margin-left: 1rem !important;}.ms-sm-4{margin-left: 1.5rem !important;}.ms-sm-5{margin-left: 3rem !important;}.ms-sm-auto{margin-left: auto !important;}.p-sm-0{padding: 0 !important;}.p-sm-1{padding: 0.25rem !important;}.p-sm-2{padding: 0.5rem !important;}.p-sm-3{padding: 1rem !important;}.p-sm-4{padding: 1.5rem !important;}.p-sm-5{padding: 3rem !important;}.px-sm-0{padding-right: 0 !important; padding-left: 0 !important;}.px-sm-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-sm-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-sm-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-sm-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-sm-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-sm-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-sm-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-sm-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-sm-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-sm-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-sm-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-sm-0{padding-top: 0 !important;}.pt-sm-1{padding-top: 0.25rem !important;}.pt-sm-2{padding-top: 0.5rem !important;}.pt-sm-3{padding-top: 1rem !important;}.pt-sm-4{padding-top: 1.5rem !important;}.pt-sm-5{padding-top: 3rem !important;}.pe-sm-0{padding-right: 0 !important;}.pe-sm-1{padding-right: 0.25rem !important;}.pe-sm-2{padding-right: 0.5rem !important;}.pe-sm-3{padding-right: 1rem !important;}.pe-sm-4{padding-right: 1.5rem !important;}.pe-sm-5{padding-right: 3rem !important;}.pb-sm-0{padding-bottom: 0 !important;}.pb-sm-1{padding-bottom: 0.25rem !important;}.pb-sm-2{padding-bottom: 0.5rem !important;}.pb-sm-3{padding-bottom: 1rem !important;}.pb-sm-4{padding-bottom: 1.5rem !important;}.pb-sm-5{padding-bottom: 3rem !important;}.ps-sm-0{padding-left: 0 !important;}.ps-sm-1{padding-left: 0.25rem !important;}.ps-sm-2{padding-left: 0.5rem !important;}.ps-sm-3{padding-left: 1rem !important;}.ps-sm-4{padding-left: 1.5rem !important;}.ps-sm-5{padding-left: 3rem !important;}.text-sm-start{text-align: left !important;}.text-sm-end{text-align: right !important;}.text-sm-center{text-align: center !important;}.flex-basis-sm-0{flex-basis: 0 !important;}.flex-basis-sm-25{flex-basis: 25% !important;}.flex-basis-sm-50{flex-basis: 50% !important;}.flex-basis-sm-75{flex-basis: 75% !important;}.flex-basis-sm-100{flex-basis: 100% !important;}.flex-basis-sm-auto{flex-basis: auto !important;}}@media (min-width: 768px){.float-md-start{float: left !important;}.float-md-end{float: right !important;}.float-md-none{float: none !important;}.d-md-inline{display: inline !important;}.d-md-inline-block{display: inline-block !important;}.d-md-block{display: block !important;}.d-md-grid{display: grid !important;}.d-md-table{display: table !important;}.d-md-table-row{display: table-row !important;}.d-md-table-cell{display: table-cell !important;}.d-md-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-md-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-md-none{display: none !important;}.d-md-contents{display: contents !important;}.position-md-static{position: static !important;}.position-md-relative{position: relative !important;}.position-md-absolute{position: absolute !important;}.position-md-fixed{position: fixed !important;}.position-md-sticky{position: sticky !important;}.w-md-0{width: 0 !important;}.w-md-25{width: 25% !important;}.w-md-50{width: 50% !important;}.w-md-75{width: 75% !important;}.w-md-100{width: 100% !important;}.w-md-auto{width: auto !important;}.mw-md-0{max-width: 0 !important;}.mw-md-25{max-width: 25% !important;}.mw-md-50{max-width: 50% !important;}.mw-md-75{max-width: 75% !important;}.mw-md-100{max-width: 100% !important;}.mw-md-auto{max-width: auto !important;}.h-md-0{height: 0 !important;}.h-md-25{height: 25% !important;}.h-md-50{height: 50% !important;}.h-md-75{height: 75% !important;}.h-md-100{height: 100% !important;}.h-md-auto{height: auto !important;}.mh-md-0{max-height: 0 !important;}.mh-md-25{max-height: 25% !important;}.mh-md-50{max-height: 50% !important;}.mh-md-75{max-height: 75% !important;}.mh-md-100{max-height: 100% !important;}.mh-md-auto{max-height: auto !important;}.flex-md-fill{flex: 1 1 auto !important;}.flex-md-row{flex-direction: row !important;}.flex-md-column{flex-direction: column !important;}.flex-md-row-reverse{flex-direction: row-reverse !important;}.flex-md-column-reverse{flex-direction: column-reverse !important;}.flex-md-grow-0{flex-grow: 0 !important;}.flex-md-grow-1{flex-grow: 1 !important;}.flex-md-shrink-0{flex-shrink: 0 !important;}.flex-md-shrink-1{flex-shrink: 1 !important;}.flex-md-wrap{flex-wrap: wrap !important;}.flex-md-nowrap{flex-wrap: nowrap !important;}.flex-md-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-md-0{gap: 0 !important;}.gap-md-1{gap: 0.25rem !important;}.gap-md-2{gap: 0.5rem !important;}.gap-md-3{gap: 1rem !important;}.gap-md-4{gap: 1.5rem !important;}.gap-md-5{gap: 3rem !important;}.justify-content-md-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-md-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-md-center{justify-content: center !important;}.justify-content-md-between{justify-content: space-between !important;}.justify-content-md-around{justify-content: space-around !important;}.justify-content-md-evenly{justify-content: space-evenly !important;}.align-items-md-start{align-items: flex-start !important;}.align-items-md-end{align-items: flex-end !important;}.align-items-md-center{align-items: center !important;}.align-items-md-baseline{align-items: baseline !important;}.align-items-md-stretch{align-items: stretch !important;}.align-content-md-start{align-content: flex-start !important;}.align-content-md-end{align-content: flex-end !important;}.align-content-md-center{align-content: center !important;}.align-content-md-between{align-content: space-between !important;}.align-content-md-around{align-content: space-around !important;}.align-content-md-stretch{align-content: stretch !important;}.align-self-md-auto{align-self: auto !important;}.align-self-md-start{align-self: flex-start !important;}.align-self-md-end{align-self: flex-end !important;}.align-self-md-center{align-self: center !important;}.align-self-md-baseline{align-self: baseline !important;}.align-self-md-stretch{align-self: stretch !important;}.order-md-first{order: -1 !important;}.order-md-last{order: 13 !important;}.order-md-0{order: 0 !important;}.order-md-1{order: 1 !important;}.order-md-2{order: 2 !important;}.order-md-3{order: 3 !important;}.order-md-4{order: 4 !important;}.order-md-5{order: 5 !important;}.order-md-6{order: 6 !important;}.order-md-7{order: 7 !important;}.order-md-8{order: 8 !important;}.order-md-9{order: 9 !important;}.order-md-10{order: 10 !important;}.order-md-11{order: 11 !important;}.order-md-12{order: 12 !important;}.m-md-0{margin: 0 !important;}.m-md-1{margin: 0.25rem !important;}.m-md-2{margin: 0.5rem !important;}.m-md-3{margin: 1rem !important;}.m-md-4{margin: 1.5rem !important;}.m-md-5{margin: 3rem !important;}.m-md-auto{margin: auto !important;}.mx-md-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-md-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-md-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-md-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-md-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-md-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-md-auto{margin-right: auto !important; margin-left: auto !important;}.my-md-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-md-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-md-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-md-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-md-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-md-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-md-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-md-0{margin-top: 0 !important;}.mt-md-1{margin-top: 0.25rem !important;}.mt-md-2{margin-top: 0.5rem !important;}.mt-md-3{margin-top: 1rem !important;}.mt-md-4{margin-top: 1.5rem !important;}.mt-md-5{margin-top: 3rem !important;}.mt-md-auto{margin-top: auto !important;}.me-md-0{margin-right: 0 !important;}.me-md-1{margin-right: 0.25rem !important;}.me-md-2{margin-right: 0.5rem !important;}.me-md-3{margin-right: 1rem !important;}.me-md-4{margin-right: 1.5rem !important;}.me-md-5{margin-right: 3rem !important;}.me-md-auto{margin-right: auto !important;}.mb-md-0{margin-bottom: 0 !important;}.mb-md-1{margin-bottom: 0.25rem !important;}.mb-md-2{margin-bottom: 0.5rem !important;}.mb-md-3{margin-bottom: 1rem !important;}.mb-md-4{margin-bottom: 1.5rem !important;}.mb-md-5{margin-bottom: 3rem !important;}.mb-md-auto{margin-bottom: auto !important;}.ms-md-0{margin-left: 0 !important;}.ms-md-1{margin-left: 0.25rem !important;}.ms-md-2{margin-left: 0.5rem !important;}.ms-md-3{margin-left: 1rem !important;}.ms-md-4{margin-left: 1.5rem !important;}.ms-md-5{margin-left: 3rem !important;}.ms-md-auto{margin-left: auto !important;}.p-md-0{padding: 0 !important;}.p-md-1{padding: 0.25rem !important;}.p-md-2{padding: 0.5rem !important;}.p-md-3{padding: 1rem !important;}.p-md-4{padding: 1.5rem !important;}.p-md-5{padding: 3rem !important;}.px-md-0{padding-right: 0 !important; padding-left: 0 !important;}.px-md-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-md-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-md-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-md-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-md-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-md-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-md-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-md-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-md-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-md-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-md-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-md-0{padding-top: 0 !important;}.pt-md-1{padding-top: 0.25rem !important;}.pt-md-2{padding-top: 0.5rem !important;}.pt-md-3{padding-top: 1rem !important;}.pt-md-4{padding-top: 1.5rem !important;}.pt-md-5{padding-top: 3rem !important;}.pe-md-0{padding-right: 0 !important;}.pe-md-1{padding-right: 0.25rem !important;}.pe-md-2{padding-right: 0.5rem !important;}.pe-md-3{padding-right: 1rem !important;}.pe-md-4{padding-right: 1.5rem !important;}.pe-md-5{padding-right: 3rem !important;}.pb-md-0{padding-bottom: 0 !important;}.pb-md-1{padding-bottom: 0.25rem !important;}.pb-md-2{padding-bottom: 0.5rem !important;}.pb-md-3{padding-bottom: 1rem !important;}.pb-md-4{padding-bottom: 1.5rem !important;}.pb-md-5{padding-bottom: 3rem !important;}.ps-md-0{padding-left: 0 !important;}.ps-md-1{padding-left: 0.25rem !important;}.ps-md-2{padding-left: 0.5rem !important;}.ps-md-3{padding-left: 1rem !important;}.ps-md-4{padding-left: 1.5rem !important;}.ps-md-5{padding-left: 3rem !important;}.text-md-start{text-align: left !important;}.text-md-end{text-align: right !important;}.text-md-center{text-align: center !important;}.flex-basis-md-0{flex-basis: 0 !important;}.flex-basis-md-25{flex-basis: 25% !important;}.flex-basis-md-50{flex-basis: 50% !important;}.flex-basis-md-75{flex-basis: 75% !important;}.flex-basis-md-100{flex-basis: 100% !important;}.flex-basis-md-auto{flex-basis: auto !important;}}@media (min-width: 992px){.float-lg-start{float: left !important;}.float-lg-end{float: right !important;}.float-lg-none{float: none !important;}.d-lg-inline{display: inline !important;}.d-lg-inline-block{display: inline-block !important;}.d-lg-block{display: block !important;}.d-lg-grid{display: grid !important;}.d-lg-table{display: table !important;}.d-lg-table-row{display: table-row !important;}.d-lg-table-cell{display: table-cell !important;}.d-lg-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-lg-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-lg-none{display: none !important;}.d-lg-contents{display: contents !important;}.position-lg-static{position: static !important;}.position-lg-relative{position: relative !important;}.position-lg-absolute{position: absolute !important;}.position-lg-fixed{position: fixed !important;}.position-lg-sticky{position: sticky !important;}.w-lg-0{width: 0 !important;}.w-lg-25{width: 25% !important;}.w-lg-50{width: 50% !important;}.w-lg-75{width: 75% !important;}.w-lg-100{width: 100% !important;}.w-lg-auto{width: auto !important;}.mw-lg-0{max-width: 0 !important;}.mw-lg-25{max-width: 25% !important;}.mw-lg-50{max-width: 50% !important;}.mw-lg-75{max-width: 75% !important;}.mw-lg-100{max-width: 100% !important;}.mw-lg-auto{max-width: auto !important;}.h-lg-0{height: 0 !important;}.h-lg-25{height: 25% !important;}.h-lg-50{height: 50% !important;}.h-lg-75{height: 75% !important;}.h-lg-100{height: 100% !important;}.h-lg-auto{height: auto !important;}.mh-lg-0{max-height: 0 !important;}.mh-lg-25{max-height: 25% !important;}.mh-lg-50{max-height: 50% !important;}.mh-lg-75{max-height: 75% !important;}.mh-lg-100{max-height: 100% !important;}.mh-lg-auto{max-height: auto !important;}.flex-lg-fill{flex: 1 1 auto !important;}.flex-lg-row{flex-direction: row !important;}.flex-lg-column{flex-direction: column !important;}.flex-lg-row-reverse{flex-direction: row-reverse !important;}.flex-lg-column-reverse{flex-direction: column-reverse !important;}.flex-lg-grow-0{flex-grow: 0 !important;}.flex-lg-grow-1{flex-grow: 1 !important;}.flex-lg-shrink-0{flex-shrink: 0 !important;}.flex-lg-shrink-1{flex-shrink: 1 !important;}.flex-lg-wrap{flex-wrap: wrap !important;}.flex-lg-nowrap{flex-wrap: nowrap !important;}.flex-lg-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-lg-0{gap: 0 !important;}.gap-lg-1{gap: 0.25rem !important;}.gap-lg-2{gap: 0.5rem !important;}.gap-lg-3{gap: 1rem !important;}.gap-lg-4{gap: 1.5rem !important;}.gap-lg-5{gap: 3rem !important;}.justify-content-lg-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-lg-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-lg-center{justify-content: center !important;}.justify-content-lg-between{justify-content: space-between !important;}.justify-content-lg-around{justify-content: space-around !important;}.justify-content-lg-evenly{justify-content: space-evenly !important;}.align-items-lg-start{align-items: flex-start !important;}.align-items-lg-end{align-items: flex-end !important;}.align-items-lg-center{align-items: center !important;}.align-items-lg-baseline{align-items: baseline !important;}.align-items-lg-stretch{align-items: stretch !important;}.align-content-lg-start{align-content: flex-start !important;}.align-content-lg-end{align-content: flex-end !important;}.align-content-lg-center{align-content: center !important;}.align-content-lg-between{align-content: space-between !important;}.align-content-lg-around{align-content: space-around !important;}.align-content-lg-stretch{align-content: stretch !important;}.align-self-lg-auto{align-self: auto !important;}.align-self-lg-start{align-self: flex-start !important;}.align-self-lg-end{align-self: flex-end !important;}.align-self-lg-center{align-self: center !important;}.align-self-lg-baseline{align-self: baseline !important;}.align-self-lg-stretch{align-self: stretch !important;}.order-lg-first{order: -1 !important;}.order-lg-last{order: 13 !important;}.order-lg-0{order: 0 !important;}.order-lg-1{order: 1 !important;}.order-lg-2{order: 2 !important;}.order-lg-3{order: 3 !important;}.order-lg-4{order: 4 !important;}.order-lg-5{order: 5 !important;}.order-lg-6{order: 6 !important;}.order-lg-7{order: 7 !important;}.order-lg-8{order: 8 !important;}.order-lg-9{order: 9 !important;}.order-lg-10{order: 10 !important;}.order-lg-11{order: 11 !important;}.order-lg-12{order: 12 !important;}.m-lg-0{margin: 0 !important;}.m-lg-1{margin: 0.25rem !important;}.m-lg-2{margin: 0.5rem !important;}.m-lg-3{margin: 1rem !important;}.m-lg-4{margin: 1.5rem !important;}.m-lg-5{margin: 3rem !important;}.m-lg-auto{margin: auto !important;}.mx-lg-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-lg-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-lg-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-lg-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-lg-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-lg-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-lg-auto{margin-right: auto !important; margin-left: auto !important;}.my-lg-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-lg-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-lg-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-lg-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-lg-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-lg-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-lg-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-lg-0{margin-top: 0 !important;}.mt-lg-1{margin-top: 0.25rem !important;}.mt-lg-2{margin-top: 0.5rem !important;}.mt-lg-3{margin-top: 1rem !important;}.mt-lg-4{margin-top: 1.5rem !important;}.mt-lg-5{margin-top: 3rem !important;}.mt-lg-auto{margin-top: auto !important;}.me-lg-0{margin-right: 0 !important;}.me-lg-1{margin-right: 0.25rem !important;}.me-lg-2{margin-right: 0.5rem !important;}.me-lg-3{margin-right: 1rem !important;}.me-lg-4{margin-right: 1.5rem !important;}.me-lg-5{margin-right: 3rem !important;}.me-lg-auto{margin-right: auto !important;}.mb-lg-0{margin-bottom: 0 !important;}.mb-lg-1{margin-bottom: 0.25rem !important;}.mb-lg-2{margin-bottom: 0.5rem !important;}.mb-lg-3{margin-bottom: 1rem !important;}.mb-lg-4{margin-bottom: 1.5rem !important;}.mb-lg-5{margin-bottom: 3rem !important;}.mb-lg-auto{margin-bottom: auto !important;}.ms-lg-0{margin-left: 0 !important;}.ms-lg-1{margin-left: 0.25rem !important;}.ms-lg-2{margin-left: 0.5rem !important;}.ms-lg-3{margin-left: 1rem !important;}.ms-lg-4{margin-left: 1.5rem !important;}.ms-lg-5{margin-left: 3rem !important;}.ms-lg-auto{margin-left: auto !important;}.p-lg-0{padding: 0 !important;}.p-lg-1{padding: 0.25rem !important;}.p-lg-2{padding: 0.5rem !important;}.p-lg-3{padding: 1rem !important;}.p-lg-4{padding: 1.5rem !important;}.p-lg-5{padding: 3rem !important;}.px-lg-0{padding-right: 0 !important; padding-left: 0 !important;}.px-lg-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-lg-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-lg-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-lg-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-lg-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-lg-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-lg-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-lg-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-lg-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-lg-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-lg-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-lg-0{padding-top: 0 !important;}.pt-lg-1{padding-top: 0.25rem !important;}.pt-lg-2{padding-top: 0.5rem !important;}.pt-lg-3{padding-top: 1rem !important;}.pt-lg-4{padding-top: 1.5rem !important;}.pt-lg-5{padding-top: 3rem !important;}.pe-lg-0{padding-right: 0 !important;}.pe-lg-1{padding-right: 0.25rem !important;}.pe-lg-2{padding-right: 0.5rem !important;}.pe-lg-3{padding-right: 1rem !important;}.pe-lg-4{padding-right: 1.5rem !important;}.pe-lg-5{padding-right: 3rem !important;}.pb-lg-0{padding-bottom: 0 !important;}.pb-lg-1{padding-bottom: 0.25rem !important;}.pb-lg-2{padding-bottom: 0.5rem !important;}.pb-lg-3{padding-bottom: 1rem !important;}.pb-lg-4{padding-bottom: 1.5rem !important;}.pb-lg-5{padding-bottom: 3rem !important;}.ps-lg-0{padding-left: 0 !important;}.ps-lg-1{padding-left: 0.25rem !important;}.ps-lg-2{padding-left: 0.5rem !important;}.ps-lg-3{padding-left: 1rem !important;}.ps-lg-4{padding-left: 1.5rem !important;}.ps-lg-5{padding-left: 3rem !important;}.text-lg-start{text-align: left !important;}.text-lg-end{text-align: right !important;}.text-lg-center{text-align: center !important;}.flex-basis-lg-0{flex-basis: 0 !important;}.flex-basis-lg-25{flex-basis: 25% !important;}.flex-basis-lg-50{flex-basis: 50% !important;}.flex-basis-lg-75{flex-basis: 75% !important;}.flex-basis-lg-100{flex-basis: 100% !important;}.flex-basis-lg-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.float-xl-start{float: left !important;}.float-xl-end{float: right !important;}.float-xl-none{float: none !important;}.d-xl-inline{display: inline !important;}.d-xl-inline-block{display: inline-block !important;}.d-xl-block{display: block !important;}.d-xl-grid{display: grid !important;}.d-xl-table{display: table !important;}.d-xl-table-row{display: table-row !important;}.d-xl-table-cell{display: table-cell !important;}.d-xl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xl-none{display: none !important;}.d-xl-contents{display: contents !important;}.position-xl-static{position: static !important;}.position-xl-relative{position: relative !important;}.position-xl-absolute{position: absolute !important;}.position-xl-fixed{position: fixed !important;}.position-xl-sticky{position: sticky !important;}.w-xl-0{width: 0 !important;}.w-xl-25{width: 25% !important;}.w-xl-50{width: 50% !important;}.w-xl-75{width: 75% !important;}.w-xl-100{width: 100% !important;}.w-xl-auto{width: auto !important;}.mw-xl-0{max-width: 0 !important;}.mw-xl-25{max-width: 25% !important;}.mw-xl-50{max-width: 50% !important;}.mw-xl-75{max-width: 75% !important;}.mw-xl-100{max-width: 100% !important;}.mw-xl-auto{max-width: auto !important;}.h-xl-0{height: 0 !important;}.h-xl-25{height: 25% !important;}.h-xl-50{height: 50% !important;}.h-xl-75{height: 75% !important;}.h-xl-100{height: 100% !important;}.h-xl-auto{height: auto !important;}.mh-xl-0{max-height: 0 !important;}.mh-xl-25{max-height: 25% !important;}.mh-xl-50{max-height: 50% !important;}.mh-xl-75{max-height: 75% !important;}.mh-xl-100{max-height: 100% !important;}.mh-xl-auto{max-height: auto !important;}.flex-xl-fill{flex: 1 1 auto !important;}.flex-xl-row{flex-direction: row !important;}.flex-xl-column{flex-direction: column !important;}.flex-xl-row-reverse{flex-direction: row-reverse !important;}.flex-xl-column-reverse{flex-direction: column-reverse !important;}.flex-xl-grow-0{flex-grow: 0 !important;}.flex-xl-grow-1{flex-grow: 1 !important;}.flex-xl-shrink-0{flex-shrink: 0 !important;}.flex-xl-shrink-1{flex-shrink: 1 !important;}.flex-xl-wrap{flex-wrap: wrap !important;}.flex-xl-nowrap{flex-wrap: nowrap !important;}.flex-xl-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-xl-0{gap: 0 !important;}.gap-xl-1{gap: 0.25rem !important;}.gap-xl-2{gap: 0.5rem !important;}.gap-xl-3{gap: 1rem !important;}.gap-xl-4{gap: 1.5rem !important;}.gap-xl-5{gap: 3rem !important;}.justify-content-xl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xl-center{justify-content: center !important;}.justify-content-xl-between{justify-content: space-between !important;}.justify-content-xl-around{justify-content: space-around !important;}.justify-content-xl-evenly{justify-content: space-evenly !important;}.align-items-xl-start{align-items: flex-start !important;}.align-items-xl-end{align-items: flex-end !important;}.align-items-xl-center{align-items: center !important;}.align-items-xl-baseline{align-items: baseline !important;}.align-items-xl-stretch{align-items: stretch !important;}.align-content-xl-start{align-content: flex-start !important;}.align-content-xl-end{align-content: flex-end !important;}.align-content-xl-center{align-content: center !important;}.align-content-xl-between{align-content: space-between !important;}.align-content-xl-around{align-content: space-around !important;}.align-content-xl-stretch{align-content: stretch !important;}.align-self-xl-auto{align-self: auto !important;}.align-self-xl-start{align-self: flex-start !important;}.align-self-xl-end{align-self: flex-end !important;}.align-self-xl-center{align-self: center !important;}.align-self-xl-baseline{align-self: baseline !important;}.align-self-xl-stretch{align-self: stretch !important;}.order-xl-first{order: -1 !important;}.order-xl-last{order: 13 !important;}.order-xl-0{order: 0 !important;}.order-xl-1{order: 1 !important;}.order-xl-2{order: 2 !important;}.order-xl-3{order: 3 !important;}.order-xl-4{order: 4 !important;}.order-xl-5{order: 5 !important;}.order-xl-6{order: 6 !important;}.order-xl-7{order: 7 !important;}.order-xl-8{order: 8 !important;}.order-xl-9{order: 9 !important;}.order-xl-10{order: 10 !important;}.order-xl-11{order: 11 !important;}.order-xl-12{order: 12 !important;}.m-xl-0{margin: 0 !important;}.m-xl-1{margin: 0.25rem !important;}.m-xl-2{margin: 0.5rem !important;}.m-xl-3{margin: 1rem !important;}.m-xl-4{margin: 1.5rem !important;}.m-xl-5{margin: 3rem !important;}.m-xl-auto{margin: auto !important;}.mx-xl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xl-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-xl-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-xl-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-xl-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-xl-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-xl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xl-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-xl-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-xl-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-xl-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-xl-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-xl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xl-0{margin-top: 0 !important;}.mt-xl-1{margin-top: 0.25rem !important;}.mt-xl-2{margin-top: 0.5rem !important;}.mt-xl-3{margin-top: 1rem !important;}.mt-xl-4{margin-top: 1.5rem !important;}.mt-xl-5{margin-top: 3rem !important;}.mt-xl-auto{margin-top: auto !important;}.me-xl-0{margin-right: 0 !important;}.me-xl-1{margin-right: 0.25rem !important;}.me-xl-2{margin-right: 0.5rem !important;}.me-xl-3{margin-right: 1rem !important;}.me-xl-4{margin-right: 1.5rem !important;}.me-xl-5{margin-right: 3rem !important;}.me-xl-auto{margin-right: auto !important;}.mb-xl-0{margin-bottom: 0 !important;}.mb-xl-1{margin-bottom: 0.25rem !important;}.mb-xl-2{margin-bottom: 0.5rem !important;}.mb-xl-3{margin-bottom: 1rem !important;}.mb-xl-4{margin-bottom: 1.5rem !important;}.mb-xl-5{margin-bottom: 3rem !important;}.mb-xl-auto{margin-bottom: auto !important;}.ms-xl-0{margin-left: 0 !important;}.ms-xl-1{margin-left: 0.25rem !important;}.ms-xl-2{margin-left: 0.5rem !important;}.ms-xl-3{margin-left: 1rem !important;}.ms-xl-4{margin-left: 1.5rem !important;}.ms-xl-5{margin-left: 3rem !important;}.ms-xl-auto{margin-left: auto !important;}.p-xl-0{padding: 0 !important;}.p-xl-1{padding: 0.25rem !important;}.p-xl-2{padding: 0.5rem !important;}.p-xl-3{padding: 1rem !important;}.p-xl-4{padding: 1.5rem !important;}.p-xl-5{padding: 3rem !important;}.px-xl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xl-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-xl-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-xl-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-xl-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-xl-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-xl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xl-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-xl-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-xl-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-xl-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-xl-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-xl-0{padding-top: 0 !important;}.pt-xl-1{padding-top: 0.25rem !important;}.pt-xl-2{padding-top: 0.5rem !important;}.pt-xl-3{padding-top: 1rem !important;}.pt-xl-4{padding-top: 1.5rem !important;}.pt-xl-5{padding-top: 3rem !important;}.pe-xl-0{padding-right: 0 !important;}.pe-xl-1{padding-right: 0.25rem !important;}.pe-xl-2{padding-right: 0.5rem !important;}.pe-xl-3{padding-right: 1rem !important;}.pe-xl-4{padding-right: 1.5rem !important;}.pe-xl-5{padding-right: 3rem !important;}.pb-xl-0{padding-bottom: 0 !important;}.pb-xl-1{padding-bottom: 0.25rem !important;}.pb-xl-2{padding-bottom: 0.5rem !important;}.pb-xl-3{padding-bottom: 1rem !important;}.pb-xl-4{padding-bottom: 1.5rem !important;}.pb-xl-5{padding-bottom: 3rem !important;}.ps-xl-0{padding-left: 0 !important;}.ps-xl-1{padding-left: 0.25rem !important;}.ps-xl-2{padding-left: 0.5rem !important;}.ps-xl-3{padding-left: 1rem !important;}.ps-xl-4{padding-left: 1.5rem !important;}.ps-xl-5{padding-left: 3rem !important;}.text-xl-start{text-align: left !important;}.text-xl-end{text-align: right !important;}.text-xl-center{text-align: center !important;}.flex-basis-xl-0{flex-basis: 0 !important;}.flex-basis-xl-25{flex-basis: 25% !important;}.flex-basis-xl-50{flex-basis: 50% !important;}.flex-basis-xl-75{flex-basis: 75% !important;}.flex-basis-xl-100{flex-basis: 100% !important;}.flex-basis-xl-auto{flex-basis: auto !important;}}@media (min-width: 1400px){.float-xxl-start{float: left !important;}.float-xxl-end{float: right !important;}.float-xxl-none{float: none !important;}.d-xxl-inline{display: inline !important;}.d-xxl-inline-block{display: inline-block !important;}.d-xxl-block{display: block !important;}.d-xxl-grid{display: grid !important;}.d-xxl-table{display: table !important;}.d-xxl-table-row{display: table-row !important;}.d-xxl-table-cell{display: table-cell !important;}.d-xxl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xxl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xxl-none{display: none !important;}.d-xxl-contents{display: contents !important;}.position-xxl-static{position: static !important;}.position-xxl-relative{position: relative !important;}.position-xxl-absolute{position: absolute !important;}.position-xxl-fixed{position: fixed !important;}.position-xxl-sticky{position: sticky !important;}.w-xxl-0{width: 0 !important;}.w-xxl-25{width: 25% !important;}.w-xxl-50{width: 50% !important;}.w-xxl-75{width: 75% !important;}.w-xxl-100{width: 100% !important;}.w-xxl-auto{width: auto !important;}.mw-xxl-0{max-width: 0 !important;}.mw-xxl-25{max-width: 25% !important;}.mw-xxl-50{max-width: 50% !important;}.mw-xxl-75{max-width: 75% !important;}.mw-xxl-100{max-width: 100% !important;}.mw-xxl-auto{max-width: auto !important;}.h-xxl-0{height: 0 !important;}.h-xxl-25{height: 25% !important;}.h-xxl-50{height: 50% !important;}.h-xxl-75{height: 75% !important;}.h-xxl-100{height: 100% !important;}.h-xxl-auto{height: auto !important;}.mh-xxl-0{max-height: 0 !important;}.mh-xxl-25{max-height: 25% !important;}.mh-xxl-50{max-height: 50% !important;}.mh-xxl-75{max-height: 75% !important;}.mh-xxl-100{max-height: 100% !important;}.mh-xxl-auto{max-height: auto !important;}.flex-xxl-fill{flex: 1 1 auto !important;}.flex-xxl-row{flex-direction: row !important;}.flex-xxl-column{flex-direction: column !important;}.flex-xxl-row-reverse{flex-direction: row-reverse !important;}.flex-xxl-column-reverse{flex-direction: column-reverse !important;}.flex-xxl-grow-0{flex-grow: 0 !important;}.flex-xxl-grow-1{flex-grow: 1 !important;}.flex-xxl-shrink-0{flex-shrink: 0 !important;}.flex-xxl-shrink-1{flex-shrink: 1 !important;}.flex-xxl-wrap{flex-wrap: wrap !important;}.flex-xxl-nowrap{flex-wrap: nowrap !important;}.flex-xxl-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-xxl-0{gap: 0 !important;}.gap-xxl-1{gap: 0.25rem !important;}.gap-xxl-2{gap: 0.5rem !important;}.gap-xxl-3{gap: 1rem !important;}.gap-xxl-4{gap: 1.5rem !important;}.gap-xxl-5{gap: 3rem !important;}.justify-content-xxl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xxl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xxl-center{justify-content: center !important;}.justify-content-xxl-between{justify-content: space-between !important;}.justify-content-xxl-around{justify-content: space-around !important;}.justify-content-xxl-evenly{justify-content: space-evenly !important;}.align-items-xxl-start{align-items: flex-start !important;}.align-items-xxl-end{align-items: flex-end !important;}.align-items-xxl-center{align-items: center !important;}.align-items-xxl-baseline{align-items: baseline !important;}.align-items-xxl-stretch{align-items: stretch !important;}.align-content-xxl-start{align-content: flex-start !important;}.align-content-xxl-end{align-content: flex-end !important;}.align-content-xxl-center{align-content: center !important;}.align-content-xxl-between{align-content: space-between !important;}.align-content-xxl-around{align-content: space-around !important;}.align-content-xxl-stretch{align-content: stretch !important;}.align-self-xxl-auto{align-self: auto !important;}.align-self-xxl-start{align-self: flex-start !important;}.align-self-xxl-end{align-self: flex-end !important;}.align-self-xxl-center{align-self: center !important;}.align-self-xxl-baseline{align-self: baseline !important;}.align-self-xxl-stretch{align-self: stretch !important;}.order-xxl-first{order: -1 !important;}.order-xxl-last{order: 13 !important;}.order-xxl-0{order: 0 !important;}.order-xxl-1{order: 1 !important;}.order-xxl-2{order: 2 !important;}.order-xxl-3{order: 3 !important;}.order-xxl-4{order: 4 !important;}.order-xxl-5{order: 5 !important;}.order-xxl-6{order: 6 !important;}.order-xxl-7{order: 7 !important;}.order-xxl-8{order: 8 !important;}.order-xxl-9{order: 9 !important;}.order-xxl-10{order: 10 !important;}.order-xxl-11{order: 11 !important;}.order-xxl-12{order: 12 !important;}.m-xxl-0{margin: 0 !important;}.m-xxl-1{margin: 0.25rem !important;}.m-xxl-2{margin: 0.5rem !important;}.m-xxl-3{margin: 1rem !important;}.m-xxl-4{margin: 1.5rem !important;}.m-xxl-5{margin: 3rem !important;}.m-xxl-auto{margin: auto !important;}.mx-xxl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xxl-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-xxl-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-xxl-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-xxl-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-xxl-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-xxl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xxl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xxl-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-xxl-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-xxl-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-xxl-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-xxl-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-xxl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xxl-0{margin-top: 0 !important;}.mt-xxl-1{margin-top: 0.25rem !important;}.mt-xxl-2{margin-top: 0.5rem !important;}.mt-xxl-3{margin-top: 1rem !important;}.mt-xxl-4{margin-top: 1.5rem !important;}.mt-xxl-5{margin-top: 3rem !important;}.mt-xxl-auto{margin-top: auto !important;}.me-xxl-0{margin-right: 0 !important;}.me-xxl-1{margin-right: 0.25rem !important;}.me-xxl-2{margin-right: 0.5rem !important;}.me-xxl-3{margin-right: 1rem !important;}.me-xxl-4{margin-right: 1.5rem !important;}.me-xxl-5{margin-right: 3rem !important;}.me-xxl-auto{margin-right: auto !important;}.mb-xxl-0{margin-bottom: 0 !important;}.mb-xxl-1{margin-bottom: 0.25rem !important;}.mb-xxl-2{margin-bottom: 0.5rem !important;}.mb-xxl-3{margin-bottom: 1rem !important;}.mb-xxl-4{margin-bottom: 1.5rem !important;}.mb-xxl-5{margin-bottom: 3rem !important;}.mb-xxl-auto{margin-bottom: auto !important;}.ms-xxl-0{margin-left: 0 !important;}.ms-xxl-1{margin-left: 0.25rem !important;}.ms-xxl-2{margin-left: 0.5rem !important;}.ms-xxl-3{margin-left: 1rem !important;}.ms-xxl-4{margin-left: 1.5rem !important;}.ms-xxl-5{margin-left: 3rem !important;}.ms-xxl-auto{margin-left: auto !important;}.p-xxl-0{padding: 0 !important;}.p-xxl-1{padding: 0.25rem !important;}.p-xxl-2{padding: 0.5rem !important;}.p-xxl-3{padding: 1rem !important;}.p-xxl-4{padding: 1.5rem !important;}.p-xxl-5{padding: 3rem !important;}.px-xxl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xxl-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-xxl-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-xxl-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-xxl-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-xxl-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-xxl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xxl-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-xxl-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-xxl-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-xxl-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-xxl-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-xxl-0{padding-top: 0 !important;}.pt-xxl-1{padding-top: 0.25rem !important;}.pt-xxl-2{padding-top: 0.5rem !important;}.pt-xxl-3{padding-top: 1rem !important;}.pt-xxl-4{padding-top: 1.5rem !important;}.pt-xxl-5{padding-top: 3rem !important;}.pe-xxl-0{padding-right: 0 !important;}.pe-xxl-1{padding-right: 0.25rem !important;}.pe-xxl-2{padding-right: 0.5rem !important;}.pe-xxl-3{padding-right: 1rem !important;}.pe-xxl-4{padding-right: 1.5rem !important;}.pe-xxl-5{padding-right: 3rem !important;}.pb-xxl-0{padding-bottom: 0 !important;}.pb-xxl-1{padding-bottom: 0.25rem !important;}.pb-xxl-2{padding-bottom: 0.5rem !important;}.pb-xxl-3{padding-bottom: 1rem !important;}.pb-xxl-4{padding-bottom: 1.5rem !important;}.pb-xxl-5{padding-bottom: 3rem !important;}.ps-xxl-0{padding-left: 0 !important;}.ps-xxl-1{padding-left: 0.25rem !important;}.ps-xxl-2{padding-left: 0.5rem !important;}.ps-xxl-3{padding-left: 1rem !important;}.ps-xxl-4{padding-left: 1.5rem !important;}.ps-xxl-5{padding-left: 3rem !important;}.text-xxl-start{text-align: left !important;}.text-xxl-end{text-align: right !important;}.text-xxl-center{text-align: center !important;}.flex-basis-xxl-0{flex-basis: 0 !important;}.flex-basis-xxl-25{flex-basis: 25% !important;}.flex-basis-xxl-50{flex-basis: 50% !important;}.flex-basis-xxl-75{flex-basis: 75% !important;}.flex-basis-xxl-100{flex-basis: 100% !important;}.flex-basis-xxl-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.fs-1{font-size: 2.5rem !important;}.fs-2{font-size: 2rem !important;}.fs-3{font-size: 1.75rem !important;}.fs-4{font-size: 1.5rem !important;}}@media print{.d-print-inline{display: inline !important;}.d-print-inline-block{display: inline-block !important;}.d-print-block{display: block !important;}.d-print-grid{display: grid !important;}.d-print-table{display: table !important;}.d-print-table-row{display: table-row !important;}.d-print-table-cell{display: table-cell !important;}.d-print-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-print-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-print-none{display: none !important;}.d-print-contents{display: contents !important;}}

/* /web/static/src/scss/bootstrap_review.scss */
 .alert{clear: both;}.bg-100{background-color: #f8f9fa !important; color: #000;}.bg-100 .text-muted, .o_colored_level .bg-100 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-100:hover, a.bg-100:focus, button.bg-100:hover, button.bg-100:focus{background-color: #dae0e5 !important; color: #000;}.text-100{color: #f8f9fa !important;}a.text-100:hover, a.text-100:focus{color: #bdc6d0 !important;}.bg-200{background-color: #e9ecef !important; color: #000;}.bg-200 .text-muted, .o_colored_level .bg-200 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-200:hover, a.bg-200:focus, button.bg-200:hover, button.bg-200:focus{background-color: #cbd3da !important; color: #000;}.text-200{color: #e9ecef !important;}a.text-200:hover, a.text-200:focus{color: #aeb9c4 !important;}.bg-300{background-color: #dee2e6 !important; color: #000;}.bg-300 .text-muted, .o_colored_level .bg-300 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-300:hover, a.bg-300:focus, button.bg-300:hover, button.bg-300:focus{background-color: #c1c9d0 !important; color: #000;}.text-300{color: #dee2e6 !important;}a.text-300:hover, a.text-300:focus{color: #a4afba !important;}.bg-400{background-color: #ced4da !important; color: #000;}.bg-400 .text-muted, .o_colored_level .bg-400 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-400:hover, a.bg-400:focus, button.bg-400:hover, button.bg-400:focus{background-color: #b1bbc4 !important; color: #000;}.text-400{color: #ced4da !important;}a.text-400:hover, a.text-400:focus{color: #94a1ae !important;}.bg-500{background-color: #adb5bd !important; color: #000;}.bg-500 .text-muted, .o_colored_level .bg-500 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-500:hover, a.bg-500:focus, button.bg-500:hover, button.bg-500:focus{background-color: #919ca6 !important; color: #000;}.text-500{color: #adb5bd !important;}a.text-500:hover, a.text-500:focus{color: #748290 !important;}.bg-600{background-color: #6c757d !important; color: #fff;}.bg-600 .text-muted, .o_colored_level .bg-600 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-600:hover, a.bg-600:focus, button.bg-600:hover, button.bg-600:focus{background-color: #545b62 !important; color: #fff;}.text-600{color: #6c757d !important;}a.text-600:hover, a.text-600:focus{color: #3d4246 !important;}.bg-700{background-color: #495057 !important; color: #fff;}.bg-700 .text-muted, .o_colored_level .bg-700 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-700:hover, a.bg-700:focus, button.bg-700:hover, button.bg-700:focus{background-color: #32373b !important; color: #fff;}.text-700{color: #495057 !important;}a.text-700:hover, a.text-700:focus{color: #1a1d20 !important;}.bg-800{background-color: #343a40 !important; color: #fff;}.bg-800 .text-muted, .o_colored_level .bg-800 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-800:hover, a.bg-800:focus, button.bg-800:hover, button.bg-800:focus{background-color: #1d2124 !important; color: #fff;}.text-800{color: #343a40 !important;}a.text-800:hover, a.text-800:focus{color: #060708 !important;}.bg-900{background-color: #212529 !important; color: #fff;}.bg-900 .text-muted, .o_colored_level .bg-900 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-900:hover, a.bg-900:focus, button.bg-900:hover, button.bg-900:focus{background-color: #0a0c0d !important; color: #fff;}.text-900{color: #212529 !important;}a.text-900:hover, a.text-900:focus{color: black !important;}.text-primary{color: #0d6efd !important;}a.text-primary:hover, a.text-primary:focus{color: #0143a3 !important;}.text-secondary{color: #6c757d !important;}a.text-secondary:hover, a.text-secondary:focus{color: #3d4246 !important;}.text-success{color: #198754 !important;}a.text-success:hover, a.text-success:focus{color: #09311e !important;}.text-info{color: #0dcaf0 !important;}a.text-info:hover, a.text-info:focus{color: #08798f !important;}.text-warning{color: #ffc107 !important;}a.text-warning:hover, a.text-warning:focus{color: #a07800 !important;}.text-danger{color: #dc3545 !important;}a.text-danger:hover, a.text-danger:focus{color: #921925 !important;}.text-light{color: #f8f9fa !important;}a.text-light:hover, a.text-light:focus{color: #bdc6d0 !important;}.text-dark{color: #212529 !important;}a.text-dark:hover, a.text-dark:focus{color: black !important;}.card-body{background-color: rgba(255, 255, 255, 0.9) !important; color: #000;}.card-body .text-muted, .o_colored_level .card-body .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.card-body:first-child{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-body:last-child{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card-body.row{background-color: transparent !important;}.accordion .collapsing > .card-body:first-child, .accordion .collapse.show > .card-body:first-child{margin-top: 1px;}.toast-header{background-clip: border-box;}.toast-body{background-color: rgba(255, 255, 255, 0.93) !important; color: #000;}.toast-body .text-muted, .o_colored_level .toast-body .text-muted{color: rgba(0, 0, 0, 0.7) !important;}@media (min-width: 576px){:not(.s_popup) > .modal .modal-dialog{height: 100%; padding: 1.75rem 0; margin: 0 auto;}:not(.s_popup) > .modal .modal-content{max-height: 100%;}:not(.s_popup) > .modal .modal-header, :not(.s_popup) > .modal .modal-footer{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}:not(.s_popup) > .modal .modal-body{overflow: auto; min-height: 0;}}.modal-backdrop{display: none;}.modal:not([data-bs-backdrop="false"]){background-color: rgba(0, 0, 0, 0.5);}.form-check .form-check-input:not(:disabled), .form-check .form-check-input:not(:disabled) + label, .form-select .form-check-input:not(:disabled), .form-select .form-check-input:not(:disabled) + label{cursor: pointer;}.form-check:hover, .form-check:hover .form-check-input:not(:disabled), .form-select:hover, .form-select:hover .form-check-input:not(:disabled){border-color: #0d6efd;}.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"]{right: auto;}.popover{right: auto;}.btn-fill-primary, .btn-primary{color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.btn-fill-primary:hover, .btn-primary:hover{color: #fff; background-color: #0b5ed7; border-color: #0a58ca;}.btn-check:focus + .btn-fill-primary, .btn-check:focus + .btn-primary, .btn-fill-primary:focus, .btn-primary:focus{color: #fff; background-color: #0b5ed7; border-color: #0a58ca; box-shadow: 0 0 0 0.25rem rgba(49, 132, 253, 0.5);}.btn-check:checked + .btn-fill-primary, .btn-check:checked + .btn-primary, .btn-check:active + .btn-fill-primary, .btn-check:active + .btn-primary, .btn-fill-primary:active, .btn-primary:active, .btn-fill-primary.active, .active.btn-primary, .show > .btn-fill-primary.dropdown-toggle, .show > .dropdown-toggle.btn-primary{color: #fff; background-color: #0a58ca; border-color: #0a53be;}.btn-check:checked + .btn-fill-primary:focus, .btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-fill-primary:focus, .btn-check:active + .btn-primary:focus, .btn-fill-primary:active:focus, .btn-primary:active:focus, .btn-fill-primary.active:focus, .active.btn-primary:focus, .show > .btn-fill-primary.dropdown-toggle:focus, .show > .dropdown-toggle.btn-primary:focus{box-shadow: 0 0 0 0.25rem rgba(49, 132, 253, 0.5);}.btn-fill-primary:disabled, .btn-primary:disabled, .btn-fill-primary.disabled, .disabled.btn-primary{color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.btn-fill-secondary, .btn-secondary{color: #fff; background-color: #6c757d; border-color: #6c757d;}.btn-fill-secondary:hover, .btn-secondary:hover{color: #fff; background-color: #5c636a; border-color: #565e64;}.btn-check:focus + .btn-fill-secondary, .btn-check:focus + .btn-secondary, .btn-fill-secondary:focus, .btn-secondary:focus{color: #fff; background-color: #5c636a; border-color: #565e64; box-shadow: 0 0 0 0.25rem rgba(130, 138, 145, 0.5);}.btn-check:checked + .btn-fill-secondary, .btn-check:checked + .btn-secondary, .btn-check:active + .btn-fill-secondary, .btn-check:active + .btn-secondary, .btn-fill-secondary:active, .btn-secondary:active, .btn-fill-secondary.active, .active.btn-secondary, .show > .btn-fill-secondary.dropdown-toggle, .show > .dropdown-toggle.btn-secondary{color: #fff; background-color: #565e64; border-color: #51585e;}.btn-check:checked + .btn-fill-secondary:focus, .btn-check:checked + .btn-secondary:focus, .btn-check:active + .btn-fill-secondary:focus, .btn-check:active + .btn-secondary:focus, .btn-fill-secondary:active:focus, .btn-secondary:active:focus, .btn-fill-secondary.active:focus, .active.btn-secondary:focus, .show > .btn-fill-secondary.dropdown-toggle:focus, .show > .dropdown-toggle.btn-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(130, 138, 145, 0.5);}.btn-fill-secondary:disabled, .btn-secondary:disabled, .btn-fill-secondary.disabled, .disabled.btn-secondary{color: #fff; background-color: #6c757d; border-color: #6c757d;}.btn-fill-success, .btn-success{color: #fff; background-color: #198754; border-color: #198754;}.btn-fill-success:hover, .btn-success:hover{color: #fff; background-color: #157347; border-color: #146c43;}.btn-check:focus + .btn-fill-success, .btn-check:focus + .btn-success, .btn-fill-success:focus, .btn-success:focus{color: #fff; background-color: #157347; border-color: #146c43; box-shadow: 0 0 0 0.25rem rgba(60, 153, 110, 0.5);}.btn-check:checked + .btn-fill-success, .btn-check:checked + .btn-success, .btn-check:active + .btn-fill-success, .btn-check:active + .btn-success, .btn-fill-success:active, .btn-success:active, .btn-fill-success.active, .active.btn-success, .show > .btn-fill-success.dropdown-toggle, .show > .dropdown-toggle.btn-success{color: #fff; background-color: #146c43; border-color: #13653f;}.btn-check:checked + .btn-fill-success:focus, .btn-check:checked + .btn-success:focus, .btn-check:active + .btn-fill-success:focus, .btn-check:active + .btn-success:focus, .btn-fill-success:active:focus, .btn-success:active:focus, .btn-fill-success.active:focus, .active.btn-success:focus, .show > .btn-fill-success.dropdown-toggle:focus, .show > .dropdown-toggle.btn-success:focus{box-shadow: 0 0 0 0.25rem rgba(60, 153, 110, 0.5);}.btn-fill-success:disabled, .btn-success:disabled, .btn-fill-success.disabled, .disabled.btn-success{color: #fff; background-color: #198754; border-color: #198754;}.btn-fill-info, .btn-info{color: #000; background-color: #0dcaf0; border-color: #0dcaf0;}.btn-fill-info:hover, .btn-info:hover{color: #000; background-color: #31d2f2; border-color: #25cff2;}.btn-check:focus + .btn-fill-info, .btn-check:focus + .btn-info, .btn-fill-info:focus, .btn-info:focus{color: #000; background-color: #31d2f2; border-color: #25cff2; box-shadow: 0 0 0 0.25rem rgba(11, 172, 204, 0.5);}.btn-check:checked + .btn-fill-info, .btn-check:checked + .btn-info, .btn-check:active + .btn-fill-info, .btn-check:active + .btn-info, .btn-fill-info:active, .btn-info:active, .btn-fill-info.active, .active.btn-info, .show > .btn-fill-info.dropdown-toggle, .show > .dropdown-toggle.btn-info{color: #000; background-color: #3dd5f3; border-color: #25cff2;}.btn-check:checked + .btn-fill-info:focus, .btn-check:checked + .btn-info:focus, .btn-check:active + .btn-fill-info:focus, .btn-check:active + .btn-info:focus, .btn-fill-info:active:focus, .btn-info:active:focus, .btn-fill-info.active:focus, .active.btn-info:focus, .show > .btn-fill-info.dropdown-toggle:focus, .show > .dropdown-toggle.btn-info:focus{box-shadow: 0 0 0 0.25rem rgba(11, 172, 204, 0.5);}.btn-fill-info:disabled, .btn-info:disabled, .btn-fill-info.disabled, .disabled.btn-info{color: #000; background-color: #0dcaf0; border-color: #0dcaf0;}.btn-fill-warning, .btn-warning{color: #000; background-color: #ffc107; border-color: #ffc107;}.btn-fill-warning:hover, .btn-warning:hover{color: #000; background-color: #ffca2c; border-color: #ffc720;}.btn-check:focus + .btn-fill-warning, .btn-check:focus + .btn-warning, .btn-fill-warning:focus, .btn-warning:focus{color: #000; background-color: #ffca2c; border-color: #ffc720; box-shadow: 0 0 0 0.25rem rgba(217, 164, 6, 0.5);}.btn-check:checked + .btn-fill-warning, .btn-check:checked + .btn-warning, .btn-check:active + .btn-fill-warning, .btn-check:active + .btn-warning, .btn-fill-warning:active, .btn-warning:active, .btn-fill-warning.active, .active.btn-warning, .show > .btn-fill-warning.dropdown-toggle, .show > .dropdown-toggle.btn-warning{color: #000; background-color: #ffcd39; border-color: #ffc720;}.btn-check:checked + .btn-fill-warning:focus, .btn-check:checked + .btn-warning:focus, .btn-check:active + .btn-fill-warning:focus, .btn-check:active + .btn-warning:focus, .btn-fill-warning:active:focus, .btn-warning:active:focus, .btn-fill-warning.active:focus, .active.btn-warning:focus, .show > .btn-fill-warning.dropdown-toggle:focus, .show > .dropdown-toggle.btn-warning:focus{box-shadow: 0 0 0 0.25rem rgba(217, 164, 6, 0.5);}.btn-fill-warning:disabled, .btn-warning:disabled, .btn-fill-warning.disabled, .disabled.btn-warning{color: #000; background-color: #ffc107; border-color: #ffc107;}.btn-fill-danger, .btn-danger{color: #fff; background-color: #dc3545; border-color: #dc3545;}.btn-fill-danger:hover, .btn-danger:hover{color: #fff; background-color: #bb2d3b; border-color: #b02a37;}.btn-check:focus + .btn-fill-danger, .btn-check:focus + .btn-danger, .btn-fill-danger:focus, .btn-danger:focus{color: #fff; background-color: #bb2d3b; border-color: #b02a37; box-shadow: 0 0 0 0.25rem rgba(225, 83, 97, 0.5);}.btn-check:checked + .btn-fill-danger, .btn-check:checked + .btn-danger, .btn-check:active + .btn-fill-danger, .btn-check:active + .btn-danger, .btn-fill-danger:active, .btn-danger:active, .btn-fill-danger.active, .active.btn-danger, .show > .btn-fill-danger.dropdown-toggle, .show > .dropdown-toggle.btn-danger{color: #fff; background-color: #b02a37; border-color: #a52834;}.btn-check:checked + .btn-fill-danger:focus, .btn-check:checked + .btn-danger:focus, .btn-check:active + .btn-fill-danger:focus, .btn-check:active + .btn-danger:focus, .btn-fill-danger:active:focus, .btn-danger:active:focus, .btn-fill-danger.active:focus, .active.btn-danger:focus, .show > .btn-fill-danger.dropdown-toggle:focus, .show > .dropdown-toggle.btn-danger:focus{box-shadow: 0 0 0 0.25rem rgba(225, 83, 97, 0.5);}.btn-fill-danger:disabled, .btn-danger:disabled, .btn-fill-danger.disabled, .disabled.btn-danger{color: #fff; background-color: #dc3545; border-color: #dc3545;}.btn-fill-light, .btn-light{color: #000; background-color: #f8f9fa; border-color: #f8f9fa;}.btn-fill-light:hover, .btn-light:hover{color: #000; background-color: #f9fafb; border-color: #f9fafb;}.btn-check:focus + .btn-fill-light, .btn-check:focus + .btn-light, .btn-fill-light:focus, .btn-light:focus{color: #000; background-color: #f9fafb; border-color: #f9fafb; box-shadow: 0 0 0 0.25rem rgba(211, 212, 213, 0.5);}.btn-check:checked + .btn-fill-light, .btn-check:checked + .btn-light, .btn-check:active + .btn-fill-light, .btn-check:active + .btn-light, .btn-fill-light:active, .btn-light:active, .btn-fill-light.active, .active.btn-light, .show > .btn-fill-light.dropdown-toggle, .show > .dropdown-toggle.btn-light{color: #000; background-color: #f9fafb; border-color: #f9fafb;}.btn-check:checked + .btn-fill-light:focus, .btn-check:checked + .btn-light:focus, .btn-check:active + .btn-fill-light:focus, .btn-check:active + .btn-light:focus, .btn-fill-light:active:focus, .btn-light:active:focus, .btn-fill-light.active:focus, .active.btn-light:focus, .show > .btn-fill-light.dropdown-toggle:focus, .show > .dropdown-toggle.btn-light:focus{box-shadow: 0 0 0 0.25rem rgba(211, 212, 213, 0.5);}.btn-fill-light:disabled, .btn-light:disabled, .btn-fill-light.disabled, .disabled.btn-light{color: #000; background-color: #f8f9fa; border-color: #f8f9fa;}.btn-fill-dark, .btn-dark{color: #fff; background-color: #212529; border-color: #212529;}.btn-fill-dark:hover, .btn-dark:hover{color: #fff; background-color: #1c1f23; border-color: #1a1e21;}.btn-check:focus + .btn-fill-dark, .btn-check:focus + .btn-dark, .btn-fill-dark:focus, .btn-dark:focus{color: #fff; background-color: #1c1f23; border-color: #1a1e21; box-shadow: 0 0 0 0.25rem rgba(66, 70, 73, 0.5);}.btn-check:checked + .btn-fill-dark, .btn-check:checked + .btn-dark, .btn-check:active + .btn-fill-dark, .btn-check:active + .btn-dark, .btn-fill-dark:active, .btn-dark:active, .btn-fill-dark.active, .active.btn-dark, .show > .btn-fill-dark.dropdown-toggle, .show > .dropdown-toggle.btn-dark{color: #fff; background-color: #1a1e21; border-color: #191c1f;}.btn-check:checked + .btn-fill-dark:focus, .btn-check:checked + .btn-dark:focus, .btn-check:active + .btn-fill-dark:focus, .btn-check:active + .btn-dark:focus, .btn-fill-dark:active:focus, .btn-dark:active:focus, .btn-fill-dark.active:focus, .active.btn-dark:focus, .show > .btn-fill-dark.dropdown-toggle:focus, .show > .dropdown-toggle.btn-dark:focus{box-shadow: 0 0 0 0.25rem rgba(66, 70, 73, 0.5);}.btn-fill-dark:disabled, .btn-dark:disabled, .btn-fill-dark.disabled, .disabled.btn-dark{color: #fff; background-color: #212529; border-color: #212529;}.btn-outline-primary{color: #0d6efd; border-color: #0d6efd;}.btn-outline-primary:hover{color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);}.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show{color: #fff; background-color: #0d6efd; border-color: #0d6efd;}.btn-check:checked + .btn-outline-primary:focus, .btn-check:active + .btn-outline-primary:focus, .btn-outline-primary:active:focus, .btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);}.btn-outline-primary:disabled, .btn-outline-primary.disabled{color: #0d6efd; background-color: transparent;}.btn-outline-secondary{color: #6c757d; border-color: #6c757d;}.btn-outline-secondary:hover{color: #fff; background-color: #6c757d; border-color: #6c757d;}.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.5);}.btn-check:checked + .btn-outline-secondary, .btn-check:active + .btn-outline-secondary, .btn-outline-secondary:active, .btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show{color: #fff; background-color: #6c757d; border-color: #6c757d;}.btn-check:checked + .btn-outline-secondary:focus, .btn-check:active + .btn-outline-secondary:focus, .btn-outline-secondary:active:focus, .btn-outline-secondary.active:focus, .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.5);}.btn-outline-secondary:disabled, .btn-outline-secondary.disabled{color: #6c757d; background-color: transparent;}.btn-outline-success{color: #198754; border-color: #198754;}.btn-outline-success:hover{color: #fff; background-color: #198754; border-color: #198754;}.btn-check:focus + .btn-outline-success, .btn-outline-success:focus{box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.5);}.btn-check:checked + .btn-outline-success, .btn-check:active + .btn-outline-success, .btn-outline-success:active, .btn-outline-success.active, .btn-outline-success.dropdown-toggle.show{color: #fff; background-color: #198754; border-color: #198754;}.btn-check:checked + .btn-outline-success:focus, .btn-check:active + .btn-outline-success:focus, .btn-outline-success:active:focus, .btn-outline-success.active:focus, .btn-outline-success.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.5);}.btn-outline-success:disabled, .btn-outline-success.disabled{color: #198754; background-color: transparent;}.btn-outline-info{color: #0dcaf0; border-color: #0dcaf0;}.btn-outline-info:hover{color: #000; background-color: #0dcaf0; border-color: #0dcaf0;}.btn-check:focus + .btn-outline-info, .btn-outline-info:focus{box-shadow: 0 0 0 0.25rem rgba(13, 202, 240, 0.5);}.btn-check:checked + .btn-outline-info, .btn-check:active + .btn-outline-info, .btn-outline-info:active, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show{color: #000; background-color: #0dcaf0; border-color: #0dcaf0;}.btn-check:checked + .btn-outline-info:focus, .btn-check:active + .btn-outline-info:focus, .btn-outline-info:active:focus, .btn-outline-info.active:focus, .btn-outline-info.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(13, 202, 240, 0.5);}.btn-outline-info:disabled, .btn-outline-info.disabled{color: #0dcaf0; background-color: transparent;}.btn-outline-warning{color: #ffc107; border-color: #ffc107;}.btn-outline-warning:hover{color: #000; background-color: #ffc107; border-color: #ffc107;}.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus{box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);}.btn-check:checked + .btn-outline-warning, .btn-check:active + .btn-outline-warning, .btn-outline-warning:active, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show{color: #000; background-color: #ffc107; border-color: #ffc107;}.btn-check:checked + .btn-outline-warning:focus, .btn-check:active + .btn-outline-warning:focus, .btn-outline-warning:active:focus, .btn-outline-warning.active:focus, .btn-outline-warning.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);}.btn-outline-warning:disabled, .btn-outline-warning.disabled{color: #ffc107; background-color: transparent;}.btn-outline-danger{color: #dc3545; border-color: #dc3545;}.btn-outline-danger:hover{color: #fff; background-color: #dc3545; border-color: #dc3545;}.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);}.btn-check:checked + .btn-outline-danger, .btn-check:active + .btn-outline-danger, .btn-outline-danger:active, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show{color: #fff; background-color: #dc3545; border-color: #dc3545;}.btn-check:checked + .btn-outline-danger:focus, .btn-check:active + .btn-outline-danger:focus, .btn-outline-danger:active:focus, .btn-outline-danger.active:focus, .btn-outline-danger.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);}.btn-outline-danger:disabled, .btn-outline-danger.disabled{color: #dc3545; background-color: transparent;}.btn-outline-light{color: #f8f9fa; border-color: #f8f9fa;}.btn-outline-light:hover{color: #000; background-color: #f8f9fa; border-color: #f8f9fa;}.btn-check:focus + .btn-outline-light, .btn-outline-light:focus{box-shadow: 0 0 0 0.25rem rgba(248, 249, 250, 0.5);}.btn-check:checked + .btn-outline-light, .btn-check:active + .btn-outline-light, .btn-outline-light:active, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show{color: #000; background-color: #f8f9fa; border-color: #f8f9fa;}.btn-check:checked + .btn-outline-light:focus, .btn-check:active + .btn-outline-light:focus, .btn-outline-light:active:focus, .btn-outline-light.active:focus, .btn-outline-light.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(248, 249, 250, 0.5);}.btn-outline-light:disabled, .btn-outline-light.disabled{color: #f8f9fa; background-color: transparent;}.btn-outline-dark{color: #212529; border-color: #212529;}.btn-outline-dark:hover{color: #fff; background-color: #212529; border-color: #212529;}.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus{box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);}.btn-check:checked + .btn-outline-dark, .btn-check:active + .btn-outline-dark, .btn-outline-dark:active, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show{color: #fff; background-color: #212529; border-color: #212529;}.btn-check:checked + .btn-outline-dark:focus, .btn-check:active + .btn-outline-dark:focus, .btn-outline-dark:active:focus, .btn-outline-dark.active:focus, .btn-outline-dark.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);}.btn-outline-dark:disabled, .btn-outline-dark.disabled{color: #212529; background-color: transparent;}.navbar-nav.nav-pills .nav-link{padding-right: 1rem; padding-left: 1rem;}.carousel-control-next .visually-hidden{left: 50%;}

/* /base/static/src/css/description.css */
 .openerp .oe_form_sheet_width{max-width: 960px;}.o_web_client .o_form_view .oe_styling_v8 .container{width: 100%;}.openerp .oe_form .oe_styling_v8{width: 100%; padding: 0; margin: 0; font-family: "Open Sans", "Helvetica", Sans; font-weight: 300; color: #646464; background: white; font-size: 16px;}.openerp .oe_form .oe_styling_v8 .container{width: 100%;}.openerp .oe_form .oe_styling_v8 .oe_websiteonly{display: none;}.openerp .oe_form .oe_styling_v8 .oe_website_contents{background: whitesmoke; padding-bottom: 1px;}.openerp .oe_form .oe_styling_v8 b{font-weight: 600;}.openerp .oe_form .oe_styling_v8 a{color: #6D57E0; text-decoration: none;}.openerp .oe_form .oe_styling_v8 a:visited{color: #5b284f;}.openerp .oe_form .oe_styling_v8 a:hover{color: #0096EB;}.openerp .oe_form .oe_styling_v8 .oe_title_font{font-family: "Lato", "Open Sans", "Helvetica", Sans;}.openerp .oe_form .oe_styling_v8 .oe_page{background: white; overflow: hidden; -moz-border-radius: 1px; -webkit-border-radius: 1px; border-radius: 1px; -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35); -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);}.openerp .oe_form .oe_styling_v8 .oe_emph{font-weight: 400;}.openerp .oe_form .oe_styling_v8 .oe_dark{overflow: hidden; background: #FCFCFC; -moz-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset; -webkit-box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset; box-shadow: 0px 5px 9px -7px rgba(0, 0, 255, 0.5) inset, 0px -3px 9px -7px rgba(0, 0, 255, 0.5) inset;}.oe_page{margin: 0px auto 64px auto; max-width: 100%;}.oe_row{width: 100%; margin-top: 16px; margin-bottom: 16px; margin-left: auto; margin-right: auto;}.oe_row.oe_fit{width: auto;}.oe_clearfix:after, .oe_row:after{content: "."; display: block; clear: both; visibility: hidden; line-height: 0; height: 0;}[class*='oe_span']{float: left; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; padding: 0 16px;}.oe_span12{width: 100%;}.oe_span10{width: 83.33333%;}.oe_span9{width: 75%;}.oe_span8{width: 66.66667%;}.oe_span6{width: 50%;}.oe_span4{width: 33.33333%;}.oe_span3{width: 25%;}.oe_span2{width: 16.66667%;}[class*='oe_span'].oe_fit{padding-left: 0px !important; padding-right: 0px !important;}[class*='oe_span'].oe_right{float: right;}.oe_row.oe_flex [class*='oe_span']{display: inline-block; float: none; vertical-align: top; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; padding: 0 16px; width: auto;}.oe_row.oe_flex .oe_span12{max-width: 100%;}.oe_row.oe_flex .oe_span10{max-width: 83.33333%;}.oe_row.oe_flex .oe_span9{max-width: 75%;}.oe_row.oe_flex .oe_span8{max-width: 66.66667%;}.oe_row.oe_flex .oe_span6{max-width: 50%;}.oe_row.oe_flex .oe_span4{max-width: 33.33333%;}.oe_row.oe_flex .oe_span3{max-width: 25%;}.oe_row.oe_flex .oe_span2{max-width: 16.66667%;}.oe_mb0{margin-bottom: 0px !important;}.oe_mb4{margin-bottom: 4px !important;}.oe_mb8{margin-bottom: 8px !important;}.oe_mb16{margin-bottom: 16px !important;}.oe_mb32{margin-bottom: 32px !important;}.oe_mb48{margin-bottom: 48px !important;}.oe_mb64{margin-bottom: 64px !important;}.oe_mt0{margin-top: 0px !important;}.oe_mt4{margin-top: 4px !important;}.oe_mt8{margin-top: 8px !important;}.oe_mt16{margin-top: 16px !important;}.oe_mt32{margin-top: 32px !important;}.oe_mt48{margin-top: 48px !important;}.oe_mt64{margin-top: 64px !important;}.oe_rightfit{padding-right: 0px !important;}.oe_leftfit{padding-left: 0px !important;}.oe_leftalign{text-align: left;}.oe_rightalign{text-align: right;}.oe_centeralign{text-align: center;}.oe_centered{margin-left: auto; margin-right: auto;}.oe_hidden{display: none !important; opacity: 0 !important;}.oe_invisible{visibility: hidden !important;}.oe_transparent{opacity: 0 !important;}.oe_mb0{margin-bottom: 0px !important;}.oe_mb4{margin-bottom: 4px !important;}.oe_mb8{margin-bottom: 8px !important;}.oe_mb16{margin-bottom: 16px !important;}.oe_mb32{margin-bottom: 32px !important;}.oe_mb64{margin-bottom: 64px !important;}.oe_spaced{margin-top: 32px; margin-bottom: 32px;}.oe_more_spaced{margin-top: 64px; margin-bottom: 64px;}.oe_padded{padding-top: 16px; padding-bottom: 16px;}.oe_more_padded{padding-top: 32px; padding-bottom: 32px;}.oe_button{position: relative; bottom: 0; display: inline-block; cursor: pointer; -moz-user-select: -moz-none; -ms-user-select: none; -webkit-user-select: none; user-select: none;}.oe_styling_v8 .oe_button, .oe_styling_v8 a.oe_button{padding: 8px 14px; background: #8b72b6; color: white; -moz-border-radius: 2px; -webkit-border-radius: 2px; border-radius: 2px; -moz-box-shadow: 0px 2px 0px #afa8cc; -webkit-box-shadow: 0px 2px 0px #afa8cc; box-shadow: 0px 2px 0px #afa8cc; text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.44); border: solid 1px rgba(0, 0, 0, 0.09); -moz-transition-property: bottom, background; -o-transition-property: bottom, background; -webkit-transition-property: bottom, background; transition-property: bottom, background; -moz-transition-duration: 250ms; -o-transition-duration: 250ms; -webkit-transition-duration: 250ms; transition-duration: 250ms;}.oe_styling_v8 .oe_button:hover, .oe_styling_v8 a.oe_button:hover{background: #8b5bdd; color: white;}.oe_styling_v8 .oe_button:active, .oe_styling_v8 a.oe_button:active{background: #333333; bottom: -3px;}.oe_styling_v8 .oe_button.oe_big, .oe_styling_v8 a.oe_button.oe_big{font-size: 24px;}.oe_styling_v8 .oe_button.oe_bigger, .oe_styling_v8 a.oe_button.oe_bigger{font-size: 32px;}.oe_styling_v8 .oe_button.oe_small, .oe_styling_v8 a.oe_button.oe_small{font-size: 13px; padding: 2px 4px;}.oe_styling_v8 .oe_button.oe_small:active, .oe_styling_v8 a.oe_button.oe_small:active{bottom: -1px;}.oe_styling_v8 .oe_button.oe_medium, .oe_styling_v8 a.oe_button.oe_medium{padding: 5px 12px; font-size: 16px;}.oe_styling_v8 .oe_button.oe_tacky, .oe_styling_v8 a.oe_button.oe_tacky{background: #ff4444; -moz-box-shadow: 0px 2px 0px #eba8a8; -webkit-box-shadow: 0px 2px 0px #eba8a8; box-shadow: 0px 2px 0px #eba8a8;}.oe_styling_v8 .oe_button.oe_tacky:hover, .oe_styling_v8 a.oe_button.oe_tacky:hover{background: #ff1010;}.oe_styling_v8 .oe_button.oe_tacky:active, .oe_styling_v8 a.oe_button.oe_tacky:active{background: black;}.oe_styling_v8 .oe_button.oe_disabled, .oe_styling_v8 a.oe_button.oe_disabled{background: #c8c8c8; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4; cursor: default;}.oe_styling_v8 .oe_button.oe_disabled:hover, .oe_styling_v8 a.oe_button.oe_disabled:hover{background: #c8c8c8; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4;}.oe_styling_v8 .oe_button.oe_disabled:active, .oe_styling_v8 a.oe_button.oe_disabled:active{background: #c8c8c8; bottom: 0px; -moz-box-shadow: 0px 2px 0px #b4b4b4; -webkit-box-shadow: 0px 2px 0px #b4b4b4; box-shadow: 0px 2px 0px #b4b4b4;}.oe_styling_v8.oe_styling_black .oe_button{-moz-box-shadow: 0px 2px 0px #463555; -webkit-box-shadow: 0px 2px 0px #463555; box-shadow: 0px 2px 0px #463555;}.oe_styling_v8{}.oe_styling_v8 .oe_input{padding: 4px 7px; border-radius: 3px; border: solid 1px #d6d6d6; box-shadow: 0px 2px #e6e6e6; background: #fafafa; font-weight: 300; outline: none; -moz-transition: all 150ms linear; -o-transition: all 150ms linear; -webkit-transition: all 150ms linear; transition: all 150ms linear;}.oe_styling_v8 .oe_input:focus{border: solid 1px #969696; box-shadow: 0px 2px #d2d2d2;}.oe_styling_v8 .oe_input.oe_valid{background: #F2FFEC; border-color: #b1ebb6; box-shadow: 0px 2px #e1f8e1; color: #0f610f;}.oe_styling_v8 .oe_input.oe_invalid{background: #fff2f2; border-color: #EBB1B1; box-shadow: 0px 2px #F8E1E1; color: #610F0F;}.oe_styling_v8 .oe_input.oe_big{padding: 8px 14px;}.oe_styling_v8 .oe_input_label{font-weight: 300; font-size: 16px;}.oe_styling_v8 .oe_input_label.oe_big{font-size: 20px;}.oe_styling_v8 .oe_textarea{width: 300px; height: 80px;}.oe_styling_v8 .oe_form_layout_table{width: 100%;}.oe_styling_v8 .oe_form_layout_table td{padding-bottom: 16px;}.oe_styling_v8 .oe_form_layout_table td:first-child{text-align: right; padding-right: 16px;}.oe_styling_v8 .oe_slogan{color: #333333; font-family: "Lato", "Open Sans", "Helvetica", Sans; text-align: center; margin-top: 32px; margin-bottom: 32px;}.oe_styling_v8 h1.oe_slogan{font-size: 64px; font-weight: 900; margin-top: 48px; margin-bottom: 48px;}.oe_styling_v8 h2.oe_slogan{font-size: 40px; font-weight: 300;}.oe_styling_v8 h3.oe_slogan{font-size: 26px; font-weight: 300; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50); opacity: 0.5;}.oe_styling_v8 h4.oe_slogan{font-size: 24px; font-weight: 300;}.oe_styling_v8 h4.oe_slogan:before, .oe_styling_v8 h4.oe_slogan:after{margin: 0 20px; content: ""; display: inline-block; width: 100px; height: 0px; border-top: solid 1px; vertical-align: middle; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=30); opacity: 0.3;}.oe_styling_v8 h5.oe_slogan{font-weight: 300;}.oe_quote{margin: 8px; padding: 16px; background: rgba(0, 0, 0, 0.02); border: solid 1px rgba(0, 0, 0, 0.06); -moz-border-radius: 2px; -webkit-border-radius: 2px; border-radius: 2px;}.oe_quote .oe_q, .oe_quote q{margin: 10px; display: block; font-style: italic; text-align: center; font-size: 20px; color: #4e66e7;}.oe_quote .oe_q:before, .oe_quote .oe_q:after, .oe_quote q:before, .oe_quote q:after{content: '"'; font-weight: 900; filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=20); opacity: 0.2;}.oe_quote cite{display: block; font-style: normal; margin-top: 16px;}.oe_quote .oe_photo{float: left; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; margin-right: 16px;}.oe_quote .oe_author{font-size: 20px; padding-top: 6px; color: #8d7bac;}.oe_dark .oe_quote{background: white; border: 1px solid #f0f0ff;}.oe_picture{display: block; max-width: 84%; max-height: 400px; margin: 16px 8%;}.oe_screenshot{-moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2); -webkit-box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2); box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);}.oe_pic_ctr{position: relative;}.oe_pic_ctr > img.oe_picture{width: 100%; max-width: none; max-height: none; margin: 0;}.oe_pic_ctr > .oe_title{position: absolute; top: 15px; right: 38px;}.oe_styling_v8 .oe_pic_ctr > .oe_title{font-size: 64px; color: white; font-weight: 600; margin: 0; text-shadow: 0px 2px 0px #494949, 0px 2px 5px rgba(0, 0, 0, 0.33), 0px 0px 60px rgba(0, 0, 0, 0.22);}div.oe_demo{position: relative; border: 1px solid #dedede;}div.oe_demo span.oe_demo_play{top: 50%; left: 50%; width: 80px; height: 60px; margin-top: -30px; margin-left: -40px; display: block; position: absolute; background: url("/base/static/src/css/../img/layout/play-button.png") no-repeat left top transparent; pointer-events: none;}div.oe_demo img{max-width: 100%; width: 100%;}div.oe_demo div.oe_demo_footer{position: absolute; left: 0; background-color: rgba(0, 0, 0, 0.4); opacity: 0.85; bottom: -1px; width: 100%; padding-top: 7px; padding-bottom: 7px; color: white; font-size: 14px; font-weight: bold; border-bottom-left-radius: 3px; border-bottom-right-radius: 3px; pointer-events: none;}div.oe_demo:hover span.oe_demo_play{background: url("/base/static/src/css/../img/layout/play-button-over.png") no-repeat left top transparent;}.oe_styling_v8 .oe_container.oe_separator{height: 64px; margin-bottom: 16px; background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwLjAiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMC4wMiIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA=='); background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, rgba(0, 0, 0, 0)), color-stop(100%, rgba(0, 0, 0, 0.02))); background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); -moz-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; -webkit-box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; overflow-y: hidden;}.oe_row_tabs{text-align: center; margin-top: 0px; margin-bottom: 0px; padding-top: 21px;}.oe_row_tab{position: relative; min-width: 120px; padding: 8px; font-size: 20px; display: inline-block; margin: 0px -2px; border-top-left-radius: 4px; border-top-right-radius: 4px; border: solid 1px rgba(0, 0, 0, 0.1); border-bottom: none; background: #fafafa; background-image: +linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02)); box-shadow: 0px -3px 10px -5px rgba(0, 0, 0, 0.1) inset; cursor: pointer; -moz-transition: all 250ms linear; -o-transition: all 250ms linear; -webkit-transition: all 250ms linear; transition: all 250ms linear;}.oe_row_tab:hover{padding-bottom: 12px; top: -4px; background-color: white;}.oe_row_tab.oe_active{background-color: white; background-image: none; box-shadow: none; border-top-color: #8272b6; border-top-width: 2px; cursor: default;}.oe_row_tab.oe_active:hover{padding-bottom: 8px; top: 0asx;}.oe_calltoaction{height: 32px; margin-top: -32px; position: relative;}

/* /web/static/src/libs/fontawesome/css/font-awesome.css */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.315em; vertical-align: -6%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.fa-spin{animation: fa-spin 2s infinite linear;}.fa-pulse{animation: fa-spin 1s infinite steps(8);}@keyframes fa-spin{0%{transform: rotate(0deg);}100%{transform: rotate(359deg);}}.fa-rotate-90{transform: rotate(90deg);}.fa-rotate-180{transform: rotate(180deg);}.fa-rotate-270{transform: rotate(270deg);}.fa-flip-horizontal{transform: scale(-1, 1);}.fa-flip-vertical{transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.visually-hidden{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.visually-hidden-focusable:active, .visually-hidden-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/lib/odoo_ui_icons/style.css */
@font-face{font-family: 'odoo_ui_icons'; src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.oi{font-family: 'odoo_ui_icons'; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.oi-close:before{content: '\00d7'; font-family: sans-serif; font-weight: bold;}.oi-view-pivot:before{content: '\e800';}.oi-voip:before{content: '\e803';}.oi-odoo:before{content: '\e806';}.oi-search:before{content: '\e808';}.oi-group:before{content: '\e80a';}.oi-settings-adjust:before{content: '\e80c';}.oi-apps:before{content: '\e80d';}.oi-panel-right:before{content: '\e810';}.oi-launch:before{content: '\e812';}.oi-studio:before{content: '\e813';}.oi-view-kanban:before{content: '\e814';}.oi-view-cohort:before{content: '\e816';}.oi-view-list:before{content: '\e817';}

/* /web/static/fonts/fonts.scss */
 @font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Hai.ttf") format("truetype"); font-weight: 100; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: normal;}@font-face{font-family: "Lato-Hai"; src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Hai-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Hai-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Hai-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-HaiIta.ttf") format("truetype"); font-weight: 100; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.svg#Lato") format("svg"); font-weight: 100; font-style: italic;}@font-face{font-family: "Lato-HaiIta"; src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-HaiIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-HaiIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Lig.ttf") format("truetype"); font-weight: 300; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: normal;}@font-face{font-family: "Lato-Lig"; src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Lig-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Lig-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Lig-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-LigIta.ttf") format("truetype"); font-weight: 300; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.svg#Lato") format("svg"); font-weight: 300; font-style: italic;}@font-face{font-family: "Lato-LigIta"; src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-LigIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-LigIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Reg.ttf") format("truetype"); font-weight: 400; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: normal;}@font-face{font-family: "Lato-Reg"; src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Reg-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Reg-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Reg-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-RegIta.ttf") format("truetype"); font-weight: 400; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.svg#Lato") format("svg"); font-weight: 400; font-style: italic;}@font-face{font-family: "Lato-RegIta"; src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-RegIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-RegIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bol.ttf") format("truetype"); font-weight: 700; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: normal;}@font-face{font-family: "Lato-Bol"; src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bol-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bol-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bol-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BolIta.ttf") format("truetype"); font-weight: 700; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.svg#Lato") format("svg"); font-weight: 700; font-style: italic;}@font-face{font-family: "Lato-BolIta"; src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BolIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BolIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-Bla.ttf") format("truetype"); font-weight: 900; font-style: normal; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: normal;}@font-face{font-family: "Lato-Bla"; src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-Bla-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-Bla-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-Bla-webfont.svg#Roboto") format("svg");}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSans-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0400-04FF, U+0500-052F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansHebrew-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0590-05FF, U+FB1D-FB4F;}@font-face{font-family: 'Odoo Unicode Support Noto'; src: url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff2") format("woff2"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.woff") format("woff"), url("https://fonts.odoocdn.com/fonts/noto/NotoSansArabic-BlaIta.ttf") format("truetype"); font-weight: 900; font-style: italic; unicode-range: U+0600-06FF, U+0750-077F, U+08A0-08FF;}@font-face{font-family: 'Lato'; src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.svg#Lato") format("svg"); font-weight: 900; font-style: italic;}@font-face{font-family: "Lato-BlaIta"; src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot"); src: url("/web/static/fonts/./lato/Lato-BlaIta-webfont.eot?#iefix") format("embedded-opentype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.woff") format("woff"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.ttf") format("truetype"), url("/web/static/fonts/./lato/Lato-BlaIta-webfont.svg#Roboto") format("svg");}@font-face{font-family: "Montserrat"; src: url("/web/static/fonts/./google/Montserrat/Montserrat-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Open_Sans"; src: url("/web/static/fonts/./google/Open_Sans/Open_Sans-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Oswald"; src: url("/web/static/fonts/./google/Oswald/Oswald-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Raleway"; src: url("/web/static/fonts/./google/Raleway/Raleway-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Roboto"; src: url("/web/static/fonts/./google/Roboto/Roboto-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}@font-face{font-family: "Tajawal"; src: url("/web/static/fonts/./google/Tajawal/Tajawal-Regular.ttf") format("truetype"); font-weight: 400; font-style: normal;}

/* /web/static/src/webclient/actions/reports/report.scss */
 html, body{height: 100%; direction: ltr;}body{color: #000 !important; word-wrap: break-word; font-family: "Lato";}h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6{margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2;}h1, .h1{font-size: 2.5rem;}h2, .h2{font-size: 2rem;}h3, .h3{font-size: 1.75rem;}h4, .h4{font-size: 1.5rem;}h5, .h5{font-size: 1.25rem;}h6, .h6{font-size: 1rem;}p, span, strong, em{line-height: 1.5;}span.o_force_ltr{display: inline;}.o_force_ltr, .o_field_phone{unicode-bidi: embed; direction: ltr;}.border-black td, .border-black th{border-top: 1px solid black !important;}.table-sm > thead > tr > th{border-bottom: none !important;}.table-sm > tbody > tr{page-break-inside: avoid !important; border-top: none !important;}.zero_min_height{min-height: 0px !important;}.jumbotron, .panel, .carousel, section{page-break-inside: avoid;}.d-print-none{display: none;}.o_bold{font-weight: bolder;}.o_italic{font-style: italic;}.o_underline{text-decoration: underline;}div#total{page-break-inside: avoid;}div#total table tr.o_subtotal td, div#total table tr.o_total td{border-top: 1px solid black !important;}div#total table tr.o_subtotal.o_border_bottom td, div#total table tr.o_total.o_border_bottom td{border-bottom: 1px solid black !important;}table thead.o_black_border tr th{border-bottom: 2px solid black !important;}ol{margin-left: 40px; padding-left: 0;}ul .o_checklist > li{list-style: none; position: relative; margin-left: 20px;}ul .o_checklist > li:not(.oe-nested):before{content: ''; position: absolute; left: -20px; display: block; height: 14px; width: 14px; top: 1px; border: 1px solid; cursor: pointer;}ul .o_checklist > li.o_checked:after{content: "✓"; transition: opacity .5s; position: absolute; left: -18px; top: -1px; opacity: 1;}.col-auto{-webkit-box-flex: 1 !important;}.container{padding-right: 15px; padding-left: 15px;}.table-borderless tbody, .table-borderless thead, .table-borderless tfoot, .table-borderless tr, .table-borderless td, .table-borderless th{border: 0 none;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_standard.scss */
 .o_standard_footer{margin-top: 200px;}.o_standard_footer .list-inline-item{white-space: nowrap;}.o_report_layout_standard h2, .o_report_layout_standard .h2{color: black;}.o_report_layout_standard #informations strong{color: black;}.o_report_layout_standard #total strong{color: black;}.o_report_layout_standard table thead{color: black;}.o_report_layout_standard div[name=comment] p{margin-bottom: 0;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_background.scss */
 .o_background_footer, .o_background_header, .o_report_layout_striped{color: #495057;}.o_background_header{min-width: 900px; border-bottom: 1px solid #e9ecef;}.o_background_header img{max-height: 96px; max-width: 200px; margin-right: 16px;}.o_background_header h3, .o_background_header .h3{color: black; font-weight: 700; font-size: 1.25rem; max-width: 300px;}.o_background_footer .list-inline-item{white-space: nowrap;}.o_background_footer ul{border-top: 1px solid black; border-bottom: 1px solid black; padding: 4px 0; margin: 0 0 4px 0;}.o_background_footer ul li{color: black;}.o_report_layout_background{background-size: cover; background-position: bottom center; background-repeat: no-repeat; min-height: 620px;}.o_report_layout_striped{}.o_report_layout_striped strong{color: black;}.o_report_layout_striped .table{border-top: 1px solid #dee2e6;}.o_report_layout_striped .table td, .o_report_layout_striped .table th{border-top: none;}.o_report_layout_striped h2, .o_report_layout_striped .h2{color: black;}.o_report_layout_striped thead tr th{color: black;}.o_report_layout_striped tbody{color: #495057;}.o_report_layout_striped tbody tr:nth-child(odd){background-color: rgba(220, 205, 216, 0.2);}.o_report_layout_striped tbody tr.o_line_section{color: #714B67; background-color: rgba(73, 80, 87, 0.2) !important;}.o_report_layout_striped .row > div > table tr, .o_report_layout_striped div#total table tr{}.o_report_layout_striped .row > div > table tr:nth-child(odd), .o_report_layout_striped div#total table tr:nth-child(odd){background-color: transparent !important;}.o_report_layout_striped .row > div > table tr:first-child, .o_report_layout_striped .row > div > table tr:last-child, .o_report_layout_striped .row > div > table tr.o_subtotal, .o_report_layout_striped .row > div > table tr.o_total, .o_report_layout_striped div#total table tr:first-child, .o_report_layout_striped div#total table tr:last-child, .o_report_layout_striped div#total table tr.o_subtotal, .o_report_layout_striped div#total table tr.o_total{border-top: none !important;}.o_report_layout_striped .row > div > table tr:first-child td, .o_report_layout_striped .row > div > table tr:last-child td, .o_report_layout_striped .row > div > table tr.o_subtotal td, .o_report_layout_striped .row > div > table tr.o_total td, .o_report_layout_striped div#total table tr:first-child td, .o_report_layout_striped div#total table tr:last-child td, .o_report_layout_striped div#total table tr.o_subtotal td, .o_report_layout_striped div#total table tr.o_total td{border-top: 1px solid #ced4da !important;}.o_report_layout_striped .row > div > table tr:first-child strong, .o_report_layout_striped .row > div > table tr:last-child strong, .o_report_layout_striped .row > div > table tr.o_subtotal strong, .o_report_layout_striped .row > div > table tr.o_total strong, .o_report_layout_striped div#total table tr:first-child strong, .o_report_layout_striped div#total table tr:last-child strong, .o_report_layout_striped div#total table tr.o_subtotal strong, .o_report_layout_striped div#total table tr.o_total strong{color: black;}.o_in_iframe .o_background_header{min-width: 0;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_boxed.scss */
 .o_boxed_footer, .o_boxed_header, .o_report_layout_boxed{color: #495057; font-size: 15px;}.o_boxed_header{border-bottom: 1px solid #e9ecef;}.o_boxed_header img{max-height: 100px;}.o_boxed_header h4, .o_boxed_header .h4{color: #999999; font-weight: 700; text-transform: uppercase;}.o_boxed_footer{margin-top: 200px; white-space: nowrap; border-top: 3px solid black;}.o_boxed_footer ul{margin: 4px 0;}.o_report_layout_boxed{}.o_report_layout_boxed #total strong{color: black;}.o_report_layout_boxed #informations strong{color: black;}.o_report_layout_boxed > h2, .o_report_layout_boxed > .h2{text-transform: uppercase;}.o_report_layout_boxed h2 span, .o_report_layout_boxed .h2 span{color: black;}.o_report_layout_boxed table{border: 1px solid #495057;}.o_report_layout_boxed table thead{border-bottom: 2px solid #495057;}.o_report_layout_boxed table thead tr th{text-transform: uppercase; border: 1px solid #495057; color: black;}.o_report_layout_boxed table tbody{color: #495057;}.o_report_layout_boxed table tbody tr td{border-top: none; border-right: 1px solid #495057;}.o_report_layout_boxed table tbody tr.o_line_section td, .o_report_layout_boxed table tbody tr.o_line_note td, .o_report_layout_boxed table tbody tr.is-subtotal td{border-top: 1px solid #495057; border-bottom: 1px solid #495057;}.o_report_layout_boxed table tbody tr.o_line_section td{background-color: rgba(0, 0, 0, 0.7); color: #fff;}.o_report_layout_boxed table tbody tr.is-subtotal, .o_report_layout_boxed table tbody tr td.o_price_total{background-color: rgba(0, 0, 0, 0.1);}.o_report_layout_boxed .page > table:not(.o_main_table) tr td:last-child{background-color: #e9ecef; color: black;}.o_report_layout_boxed .row:not(#total) > div > table tbody tr:not(:last-child) td:last-child{background-color: #e9ecef; color: black;}.o_report_layout_boxed .row > div > table thead tr:first-child, .o_report_layout_boxed .row > div > table tr.o_subtotal, .o_report_layout_boxed div#total table thead tr:first-child, .o_report_layout_boxed div#total table tr.o_subtotal{border-bottom: 1px solid #495057;}.o_report_layout_boxed .row > div > table tr.o_subtotal td:first-child, .o_report_layout_boxed div#total table tr.o_subtotal td:first-child{border-right: none;}.o_report_layout_boxed .row > div > table tr:last-child td, .o_report_layout_boxed .row > div > table tr.o_total td, .o_report_layout_boxed div#total table tr:last-child td, .o_report_layout_boxed div#total table tr.o_total td{background-color: rgba(0, 0, 0, 0.9); color: #fff;}.o_report_layout_boxed .row > div > table tr:last-child td:first-child, .o_report_layout_boxed .row > div > table tr.o_total td:first-child, .o_report_layout_boxed div#total table tr:last-child td:first-child, .o_report_layout_boxed div#total table tr.o_total td:first-child{border-right: none;}.o_report_layout_boxed .row > div > table tr.o_total strong, .o_report_layout_boxed div#total table tr.o_total strong{color: white;}

/* /web/static/src/webclient/actions/reports/layout_assets/layout_clean.scss */
 .o_clean_footer, .o_clean_header, .o_report_layout_bold{color: #000;}.o_clean_header img{max-height: 90px; max-width: 300px;}.o_clean_footer{margin: 0 3px; margin-top: 200px; border-top: 3px solid black;}.o_clean_footer h4, .o_clean_footer .h4{color: black; font-weight: bolder;}.o_clean_footer .pagenumber{border: 3px solid black; background-color: black; color: white; padding: 4px 8px; text-align: center;}.o_report_layout_bold{}.o_report_layout_bold h1, .o_report_layout_bold .h1, .o_report_layout_bold h2, .o_report_layout_bold .h2, .o_report_layout_bold h3, .o_report_layout_bold .h3{color: black; font-weight: bolder;}.o_report_layout_bold strong{color: black;}.o_report_layout_bold table.o_main_table{margin-bottom: 0;}.o_report_layout_bold table thead{color: black;}.o_report_layout_bold table thead tr th{border-top: 3px solid black !important; text-transform: uppercase;}.o_report_layout_bold table tbody{color: #000;}.o_report_layout_bold table tbody tr:first-child td{border-top: none;}.o_report_layout_bold table tbody tr:last-child td{border-bottom: 3px solid black;}.o_report_layout_bold table tbody tr td{padding: 15px 5px;}.o_report_layout_bold #total strong{color: black;}.o_report_layout_bold .row:not(#total) > div:has(table){top: -16px;}.o_report_layout_bold .row > div > table tr:first-child td, .o_report_layout_bold .row > div > table tr.o_subtotal, .o_report_layout_bold div#total table tr:first-child td, .o_report_layout_bold div#total table tr.o_subtotal{border-top: none !important;}.o_report_layout_bold .row > div > table tr:last-child td, .o_report_layout_bold .row > div > table tr.o_total, .o_report_layout_bold div#total table tr:last-child td, .o_report_layout_bold div#total table tr.o_total{border-top: 1px solid #e9ecef !important;}

/* web/static/asset_styles_company_report.scss */
 .o_company_5_layout{font-family: Lato;}.o_company_5_layout.o_report_layout_standard h2, .o_company_5_layout.o_report_layout_standard .h2{color: black;}.o_company_5_layout.o_report_layout_standard #informations strong{color: black;}.o_company_5_layout.o_report_layout_standard #total strong{color: black;}.o_company_5_layout.o_report_layout_standard table thead{color: black;}.o_company_4_layout{font-family: Lato;}.o_company_4_layout.o_report_layout_standard h2, .o_company_4_layout.o_report_layout_standard .h2{color: black;}.o_company_4_layout.o_report_layout_standard #informations strong{color: black;}.o_company_4_layout.o_report_layout_standard #total strong{color: black;}.o_company_4_layout.o_report_layout_standard table thead{color: black;}.o_company_6_layout{font-family: Lato;}.o_company_6_layout.o_report_layout_standard h2, .o_company_6_layout.o_report_layout_standard .h2{color: black;}.o_company_6_layout.o_report_layout_standard #informations strong{color: black;}.o_company_6_layout.o_report_layout_standard #total strong{color: black;}.o_company_6_layout.o_report_layout_standard table thead{color: black;}.o_company_3_layout{font-family: Lato;}.o_company_3_layout.o_report_layout_standard h2, .o_company_3_layout.o_report_layout_standard .h2{color: black;}.o_company_3_layout.o_report_layout_standard #informations strong{color: black;}.o_company_3_layout.o_report_layout_standard #total strong{color: black;}.o_company_3_layout.o_report_layout_standard table thead{color: black;}.o_company_2_layout{font-family: Lato;}.o_company_2_layout.o_report_layout_standard h2, .o_company_2_layout.o_report_layout_standard .h2{color: #555555;}.o_company_2_layout.o_report_layout_standard #informations strong{color: #000000;}.o_company_2_layout.o_report_layout_standard #total strong{color: #555555;}.o_company_2_layout.o_report_layout_standard table thead{color: #000000;}.o_company_7_layout{font-family: Lato;}.o_company_7_layout.o_report_layout_standard h2, .o_company_7_layout.o_report_layout_standard .h2{color: #555555;}.o_company_7_layout.o_report_layout_standard #informations strong{color: #000000;}.o_company_7_layout.o_report_layout_standard #total strong{color: #555555;}.o_company_7_layout.o_report_layout_standard table thead{color: #000000;}.o_company_1_layout{font-family: Lato;}.o_company_1_layout.o_report_layout_standard h2, .o_company_1_layout.o_report_layout_standard .h2{color: #555555;}.o_company_1_layout.o_report_layout_standard #informations strong{color: #000000;}.o_company_1_layout.o_report_layout_standard #total strong{color: #555555;}.o_company_1_layout.o_report_layout_standard table thead{color: #000000;}

/* /product/static/src/scss/report_label_sheet.scss */
 .o_label_sheet{margin-left: -4mm; margin-right: -4mm; overflow: hidden; width: 210mm; height: 297mm; page-break-before: always;}.o_label_sheet.o_label_dymo{font-size: 90%; width: 57mm; height: 32mm;}.o_label_sheet div{padding: 2px 4px;}.o_label_sheet div.o_label_small_text{font-size: 60%; line-height: 130%;}.o_label_sheet div.o_label_name{background-color: ghostwhite; height: 3em; overflow: hidden;}.o_label_sheet div.o_label_full{overflow: hidden; padding: 0; margin: auto;}.o_label_sheet div.o_label_left_column{float: left; font-size: .6em; overflow: hidden; width: 40%;}.o_label_sheet div.o_label_left_column.o_label_full_with{width: 100%;}.o_label_sheet div.o_label_right_column{float: right;}.o_label_sheet div.o_label_small_barcode{font-size: .6em; padding: 0 4px; line-height: normal;}.o_label_sheet strong.o_label_price{font-size: 2em;}.o_label_sheet strong.o_label_price_medium{font-size: 1.3em; line-height: normal; padding: 0; padding-right: 2mm;}.o_label_sheet strong.o_label_price_small{font-size: 0.9em; padding: 0 4px; padding-right: 2mm;}.o_label_sheet div.o_label_extra_data{overflow: hidden; height: 2.5em; padding: 0;}.o_label_sheet div.o_label_clear{clear: both;}.o_label_sheet div.o_label_4x12{padding: 0; line-height: 1; font-size: 55%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}

/* /sale/static/src/scss/sale_report.scss */
 .sale_tbody .o_line_note{word-break: break-word;}

/* /stock/static/src/legacy_web_report/report_backend.scss */
 .o_report_iframe{position: absolute; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%; border: none;}.o_report_buttons{display: inline-block;}

/* /web/static/src/legacy/scss/views.scss */
 .o_invisible_modifier{display: none !important;}.o_status{background-color: #e9ecef; height: 12px; width: 12px; box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2);}.dropdown-item > .o_status{margin-bottom: .2em; transform: translateX(-50%);}.o_btn-link-as-button{padding: 2px; font-size: 12px;}.o_btn-link-as-button > a{margin-bottom: -4px !important; margin-left: 3px;}.o_view_nocontent{position: absolute; top: 0; left: 0; bottom: 0; right: 0; pointer-events: none; z-index: var(--o-view-nocontent-zindex, 1); display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; background-image: radial-gradient(at 50% 50%, white 0px, rgba(255, 255, 255, 0.5) 100%);}.o_view_nocontent .o_nocontent_help{pointer-events: auto; max-width: 650px; margin: auto; padding: 15px; z-index: 1000; text-align: center; color: #4A4F59; font-size: 115%;}.o_view_nocontent .o_nocontent_help > p:first-of-type{margin-top: 0; color: #212529; font-weight: bold; font-size: 125%;}.o_view_nocontent .o_nocontent_help a{cursor: pointer;}.o_view_nocontent .o_nocontent_help .o_view_nocontent_smiling_face:before{width: 120px; height: 140px; background: transparent url(/web/static/img/smiling_face.svg) no-repeat center;}.o_view_nocontent .o_nocontent_help .o_view_nocontent_neutral_face:before{width: 120px; height: 140px; background: transparent url(/web/static/img/neutral_face.svg) no-repeat center;}.o_view_nocontent .o_nocontent_help .o_empty_custom_dashboard{min-height: 327px; margin-left: -0.75rem; margin-top: -0.75rem; padding: 100px 0 0 137px; background: transparent url(/web/static/img/graph_background.png) no-repeat 0 0;}.o_legacy_view_sample_data .o_list_table{cursor: default !important;}.o_legacy_view_sample_data .o_list_table > thead .o_list_record_selector{pointer-events: none;}.o_legacy_view_sample_data .form-check{pointer-events: none !important;}.o_legacy_view_sample_data .o_nocontent_help{border-radius: 20%; background-color: white; box-shadow: 0 0 120px 100px white;}.o_legacy_view_sample_data .o_sample_data_disabled{opacity: 0.33; pointer-events: none; user-select: none;}

/* /stock/static/src/scss/report_stock_reception.scss */
 .o_report_reception .o_priority.o_priority_star{font-size: 1.35em;}.o_report_reception .o_priority.o_priority_star.fa-star{color: gold;}.o_report_reception .btn.btn-primary{height: 31px; background-color: #017e84; border-color: #017e84;}.o_report_reception .btn.btn-primary:hover:not([disabled]){background-color: #014e51;}.o_report_reception .badge{line-height: .75;}.o_report_reception .bg-primary-light{background-color: rgba(13, 110, 253, 0.5) !important; color: #000;}.o_report_reception .bg-primary-light .text-muted, .o_colored_level .o_report_reception .bg-primary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-primary-light:hover, .o_report_reception a.bg-primary-light:focus, .o_report_reception button.bg-primary-light:hover, .o_report_reception button.bg-primary-light:focus{background-color: rgba(2, 87, 213, 0.5) !important; color: #000;}.o_report_reception .bg-secondary-light{background-color: rgba(108, 117, 125, 0.5) !important; color: #000;}.o_report_reception .bg-secondary-light .text-muted, .o_colored_level .o_report_reception .bg-secondary-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-secondary-light:hover, .o_report_reception a.bg-secondary-light:focus, .o_report_reception button.bg-secondary-light:hover, .o_report_reception button.bg-secondary-light:focus{background-color: rgba(84, 91, 98, 0.5) !important; color: #000;}.o_report_reception .bg-success-light{background-color: rgba(25, 135, 84, 0.5) !important; color: #000;}.o_report_reception .bg-success-light .text-muted, .o_colored_level .o_report_reception .bg-success-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-success-light:hover, .o_report_reception a.bg-success-light:focus, .o_report_reception button.bg-success-light:hover, .o_report_reception button.bg-success-light:focus{background-color: rgba(17, 92, 57, 0.5) !important; color: #000;}.o_report_reception .bg-info-light{background-color: rgba(13, 202, 240, 0.5) !important; color: #000;}.o_report_reception .bg-info-light .text-muted, .o_colored_level .o_report_reception .bg-info-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-info-light:hover, .o_report_reception a.bg-info-light:focus, .o_report_reception button.bg-info-light:hover, .o_report_reception button.bg-info-light:focus{background-color: rgba(10, 161, 192, 0.5) !important; color: #000;}.o_report_reception .bg-warning-light{background-color: rgba(255, 193, 7, 0.5) !important; color: #000;}.o_report_reception .bg-warning-light .text-muted, .o_colored_level .o_report_reception .bg-warning-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-warning-light:hover, .o_report_reception a.bg-warning-light:focus, .o_report_reception button.bg-warning-light:hover, .o_report_reception button.bg-warning-light:focus{background-color: rgba(211, 158, 0, 0.5) !important; color: #000;}.o_report_reception .bg-danger-light{background-color: rgba(220, 53, 69, 0.5) !important; color: #000;}.o_report_reception .bg-danger-light .text-muted, .o_colored_level .o_report_reception .bg-danger-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-danger-light:hover, .o_report_reception a.bg-danger-light:focus, .o_report_reception button.bg-danger-light:hover, .o_report_reception button.bg-danger-light:focus{background-color: rgba(189, 33, 48, 0.5) !important; color: #000;}.o_report_reception .bg-light-light{background-color: rgba(248, 249, 250, 0.5) !important; color: #000;}.o_report_reception .bg-light-light .text-muted, .o_colored_level .o_report_reception .bg-light-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-light-light:hover, .o_report_reception a.bg-light-light:focus, .o_report_reception button.bg-light-light:hover, .o_report_reception button.bg-light-light:focus{background-color: rgba(218, 224, 229, 0.5) !important; color: #000;}.o_report_reception .bg-dark-light{background-color: rgba(33, 37, 41, 0.5) !important; color: #000;}.o_report_reception .bg-dark-light .text-muted, .o_colored_level .o_report_reception .bg-dark-light .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_report_reception a.bg-dark-light:hover, .o_report_reception a.bg-dark-light:focus, .o_report_reception button.bg-dark-light:hover, .o_report_reception button.bg-dark-light:focus{background-color: rgba(10, 12, 13, 0.5) !important; color: #000;}.o_label_page{margin-left: -3mm; margin-right: -3mm; overflow: hidden; page-break-before: always; padding: 1mm 0mm 0mm;}.o_label_page.o_label_dymo{font-size: 80%; width: 57mm; height: 32mm;}.o_label_page.o_label_dymo span, .o_label_page.o_label_dymo div{line-height: 1; white-space: nowrap;}.o_label_page span[itemprop="name"]{font-weight: bold;}

/* /stock/static/src/scss/report_stock_rule.scss */
 .o_report_stock_rule .o_report_stock_rule_rule{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row nowrap; flex-flow: row nowrap;}.o_report_stock_rule .o_report_stock_rule_legend{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap; max-width: 1000px;}.o_report_stock_rule .o_report_stock_rule_legend_line{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row nowrap; flex-flow: row nowrap; width: 29%; margin-right: 20px; margin-left: 20px; margin-top: 15px; min-width: 200px;}.o_report_stock_rule .o_report_stock_rule_legend_line > .o_report_stock_rule_legend_label{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 30%; min-width: 100px;}.o_report_stock_rule .o_report_stock_rule_legend_line > .o_report_stock_rule_legend_symbol{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 70%;}.o_report_stock_rule .o_report_stock_rule_putaway > p{text-align: center; color: black; font-weight: normal; font-size: 12px;}.o_report_stock_rule .o_report_stock_rule_line{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; height: 20px;}.o_report_stock_rule .o_report_stock_rule_line > line{stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_arrow{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; height: 20px; width: 20px;}.o_report_stock_rule .o_report_stock_rule_arrow > svg > line{stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_arrow > svg > polygon{fill: black; fill-opacity: 0.5; stroke: black; stroke-width: 1;}.o_report_stock_rule .o_report_stock_rule_vertical_bar{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; height: 20px; width: 2px;}.o_report_stock_rule .o_report_stock_rule_vertical_bar > svg > line{stroke: black; stroke-width: 2;}.o_report_stock_rule .o_report_stock_rule_rule_name{text-align: center;}.o_report_stock_rule .o_report_stock_rule_symbol_cell{border: none !important;}.o_report_stock_rule .o_report_stock_rule_symbol_cell > div{max-width: 200px; height: 20px;}.o_report_stock_rule .o_report_stock_rule_rule_main{height: 100%; padding-top: 2px;}.o_report_stock_rule .o_report_stock_rule_location_header{text-align: center;}.o_report_stock_rule .o_report_stock_rule_location_header > a{display: block;}.o_report_stock_rule .o_report_stock_rule_location_header > a:hover{text-decoration: none; cursor: pointer; background-color: #efefef;}.o_report_stock_rule .o_report_stock_rule_location_header > a > div{color: black;}.o_report_stock_rule .o_report_stock_rule_rule_cell{padding: 0 !important;}.o_report_stock_rule .o_report_stock_rule_rule_cell > a{display: block;}.o_report_stock_rule .o_report_stock_rule_rule_cell > a:hover{text-decoration: none; cursor: pointer; background-color: #efefef;}