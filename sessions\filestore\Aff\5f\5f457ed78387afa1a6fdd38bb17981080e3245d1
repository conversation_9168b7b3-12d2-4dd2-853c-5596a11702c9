{"version": 12, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 9, "rowNumber": 86, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}, "44": {"size": 40}, "45": {"size": 40}, "57": {"size": 40}, "58": {"size": 40}, "70": {"size": 40}, "71": {"size": 40}}, "cols": {"0": {"size": 100}, "1": {"size": 175}, "2": {"size": 100}, "3": {"size": 100}, "4": {"size": 50}, "5": {"size": 100}, "6": {"size": 175}, "7": {"size": 100}, "8": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Monthly Sales](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"state\",\"not in\",[\"draft\",\"cancel\",\"sent\"]]],\"context\":{\"group_by\":[\"date:month\"],\"graph_measure\":\"price_subtotal\",\"graph_mode\":\"line\",\"graph_groupbys\":[\"date:month\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A19": {"style": 1, "content": "[Top Quotations](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":[[\"state\",\"in\",[\"draft\",\"sent\"]]],\"context\":{\"group_by\":[]},\"modelName\":\"sale.order\",\"views\":[[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Quotations\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Quotation\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.LIST(1,1,\"name\")"}, "A22": {"style": 4, "content": "=ODOO.LIST(1,2,\"name\")"}, "A23": {"style": 3, "content": "=ODOO.LIST(1,3,\"name\")"}, "A24": {"style": 4, "content": "=ODOO.LIST(1,4,\"name\")"}, "A25": {"style": 3, "content": "=ODOO.LIST(1,5,\"name\")"}, "A26": {"style": 4, "content": "=ODOO.LIST(1,6,\"name\")"}, "A27": {"style": 3, "content": "=ODOO.LIST(1,7,\"name\")"}, "A28": {"style": 4, "content": "=ODOO.LIST(1,8,\"name\")"}, "A29": {"style": 3, "content": "=ODOO.LIST(1,9,\"name\")"}, "A30": {"style": 4, "content": "=ODOO.LIST(1,10,\"name\")"}, "A32": {"style": 1, "content": "[Top Countries](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"country_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A33": {"style": 5, "content": "=_t(\"Country\")", "border": 2}, "A34": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",1)"}, "A35": {"style": 7, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",2)"}, "A36": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",3)"}, "A37": {"style": 7, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",4)"}, "A38": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",5)"}, "A39": {"style": 7, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",6)"}, "A40": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",7)"}, "A41": {"style": 7, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",8)"}, "A42": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",9)"}, "A43": {"style": 7, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",10)"}, "A45": {"style": 1, "content": "[Top Customers](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"partner_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"partner_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"partner_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A46": {"style": 5, "content": "=_t(\"Customer\")", "border": 2}, "A47": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",1)"}, "A48": {"style": 7, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",2)"}, "A49": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",3)"}, "A50": {"style": 7, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",4)"}, "A51": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",5)"}, "A52": {"style": 7, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",6)"}, "A53": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",7)"}, "A54": {"style": 7, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",8)"}, "A55": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",9)"}, "A56": {"style": 7, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",10)"}, "A58": {"style": 1, "content": "[Top Sales Teams](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"team_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"team_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"team_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A59": {"style": 5, "content": "=_t(\"Sales Team\")", "border": 2}, "A60": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",1)"}, "A61": {"style": 7, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",2)"}, "A62": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",3)"}, "A63": {"style": 7, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",4)"}, "A64": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",5)"}, "A65": {"style": 7, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",6)"}, "A66": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",7)"}, "A67": {"style": 7, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",8)"}, "A68": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",9)"}, "A69": {"style": 7, "content": "=ODOO.PIVOT.HEADER(7,\"#team_id\",10)"}, "A71": {"style": 1, "content": "[Top Sources](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"source_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"source_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"source_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A72": {"style": 5, "content": "=_t(\"Source\")", "border": 2}, "A73": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",1)"}, "A74": {"style": 7, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",2)"}, "A75": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",3)"}, "A76": {"style": 7, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",4)"}, "A77": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",5)"}, "A78": {"style": 7, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",6)"}, "A79": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",7)"}, "A80": {"style": 7, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",8)"}, "A81": {"style": 6, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",9)"}, "A82": {"style": 7, "content": "=ODOO.PIVOT.HEADER(9,\"#source_id\",10)"}, "B19": {"style": 8, "border": 1}, "B20": {"style": 2, "content": "=_t(\"Customer\")", "border": 2}, "B21": {"style": 9, "content": "=ODOO.LIST(1,1,\"partner_id\")"}, "B22": {"content": "=ODOO.LIST(1,2,\"partner_id\")"}, "B23": {"style": 9, "content": "=ODOO.LIST(1,3,\"partner_id\")"}, "B24": {"content": "=ODOO.LIST(1,4,\"partner_id\")"}, "B25": {"style": 9, "content": "=ODOO.LIST(1,5,\"partner_id\")"}, "B26": {"content": "=ODOO.LIST(1,6,\"partner_id\")"}, "B27": {"style": 9, "content": "=ODOO.LIST(1,7,\"partner_id\")"}, "B28": {"content": "=ODOO.LIST(1,8,\"partner_id\")"}, "B29": {"style": 9, "content": "=ODOO.LIST(1,9,\"partner_id\")"}, "B30": {"content": "=ODOO.LIST(1,10,\"partner_id\")"}, "B32": {"style": 8, "border": 1}, "B33": {"style": 5, "border": 2}, "B34": {"style": 9}, "B36": {"style": 9}, "B38": {"style": 9}, "B40": {"style": 9}, "B42": {"style": 9}, "B45": {"style": 8, "border": 1}, "B46": {"style": 5, "border": 2}, "B47": {"style": 9}, "B49": {"style": 9}, "B51": {"style": 9}, "B53": {"style": 9}, "B55": {"style": 9}, "B58": {"style": 8, "border": 1}, "B59": {"style": 5, "border": 2}, "B60": {"style": 9}, "B62": {"style": 9}, "B64": {"style": 9}, "B66": {"style": 9}, "B68": {"style": 9}, "B71": {"style": 8, "border": 1}, "B72": {"style": 5, "border": 2}, "B73": {"style": 9}, "B75": {"style": 9}, "B77": {"style": 9}, "B79": {"style": 9}, "B81": {"style": 9}, "C19": {"style": 8, "border": 1}, "C20": {"style": 2, "content": "=_t(\"Salesperson\")", "border": 2}, "C21": {"style": 9, "content": "=ODOO.LIST(1,1,\"user_id\")"}, "C22": {"content": "=ODOO.LIST(1,2,\"user_id\")"}, "C23": {"style": 9, "content": "=ODOO.LIST(1,3,\"user_id\")"}, "C24": {"content": "=ODOO.LIST(1,4,\"user_id\")"}, "C25": {"style": 9, "content": "=ODOO.LIST(1,5,\"user_id\")"}, "C26": {"content": "=ODOO.LIST(1,6,\"user_id\")"}, "C27": {"style": 9, "content": "=ODOO.LIST(1,7,\"user_id\")"}, "C28": {"content": "=ODOO.LIST(1,8,\"user_id\")"}, "C29": {"style": 9, "content": "=ODOO.LIST(1,9,\"user_id\")"}, "C30": {"content": "=ODOO.LIST(1,10,\"user_id\")"}, "C32": {"style": 8, "border": 1}, "C33": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "C34": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",1)"}, "C35": {"format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",2)"}, "C36": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",3)"}, "C37": {"format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",4)"}, "C38": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",5)"}, "C39": {"format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",6)"}, "C40": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",7)"}, "C41": {"format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",8)"}, "C42": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",9)"}, "C43": {"format": 1, "content": "=ODOO.PIVOT(5,\"order_id\",\"#country_id\",10)"}, "C45": {"style": 8, "border": 1}, "C46": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "C47": {"style": 9, "content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",1)"}, "C48": {"content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",2)"}, "C49": {"style": 9, "content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",3)"}, "C50": {"content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",4)"}, "C51": {"style": 9, "content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",5)"}, "C52": {"content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",6)"}, "C53": {"style": 9, "content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",7)"}, "C54": {"content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",8)"}, "C55": {"style": 9, "content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",9)"}, "C56": {"content": "=ODOO.PIVOT(4,\"order_id\",\"#partner_id\",10)"}, "C58": {"style": 8, "border": 1}, "C59": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "C60": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",1)"}, "C61": {"format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",2)"}, "C62": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",3)"}, "C63": {"format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",4)"}, "C64": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",5)"}, "C65": {"format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",6)"}, "C66": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",7)"}, "C67": {"format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",8)"}, "C68": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",9)"}, "C69": {"format": 1, "content": "=ODOO.PIVOT(7,\"order_id\",\"#team_id\",10)"}, "C71": {"style": 8, "border": 1}, "C72": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "C73": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",1)"}, "C74": {"format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",2)"}, "C75": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",3)"}, "C76": {"format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",4)"}, "C77": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",5)"}, "C78": {"format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",6)"}, "C79": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",7)"}, "C80": {"format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",8)"}, "C81": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",9)"}, "C82": {"format": 1, "content": "=ODOO.PIVOT(9,\"order_id\",\"#source_id\",10)"}, "D19": {"style": 8, "border": 1}, "D20": {"style": 11, "content": "=_t(\"Revenue\")", "border": 2}, "D21": {"style": 9, "content": "=ODOO.LIST(1,1,\"amount_untaxed\")"}, "D22": {"content": "=ODOO.LIST(1,2,\"amount_untaxed\")"}, "D23": {"style": 9, "content": "=ODOO.LIST(1,3,\"amount_untaxed\")"}, "D24": {"content": "=ODOO.LIST(1,4,\"amount_untaxed\")"}, "D25": {"style": 9, "content": "=ODOO.LIST(1,5,\"amount_untaxed\")"}, "D26": {"content": "=ODOO.LIST(1,6,\"amount_untaxed\")"}, "D27": {"style": 9, "content": "=ODOO.LIST(1,7,\"amount_untaxed\")"}, "D28": {"content": "=ODOO.LIST(1,8,\"amount_untaxed\")"}, "D29": {"style": 9, "content": "=ODOO.LIST(1,9,\"amount_untaxed\")"}, "D30": {"content": "=ODOO.LIST(1,10,\"amount_untaxed\")"}, "D32": {"style": 8, "border": 1}, "D33": {"style": 10, "content": "=_t(\"Revenue\")", "border": 2}, "D34": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",1)"}, "D35": {"format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",2)"}, "D36": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",3)"}, "D37": {"format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",4)"}, "D38": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",5)"}, "D39": {"format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",6)"}, "D40": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",7)"}, "D41": {"format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",8)"}, "D42": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",9)"}, "D43": {"format": 3, "content": "=ODOO.PIVOT(5,\"price_subtotal\",\"#country_id\",10)"}, "D44": {"format": 3}, "D45": {"style": 8, "format": 3, "border": 1}, "D46": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "D47": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",1)"}, "D48": {"format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",2)"}, "D49": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",3)"}, "D50": {"format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",4)"}, "D51": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",5)"}, "D52": {"format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",6)"}, "D53": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",7)"}, "D54": {"format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",8)"}, "D55": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",9)"}, "D56": {"format": 3, "content": "=ODOO.PIVOT(4,\"price_subtotal\",\"#partner_id\",10)"}, "D57": {"format": 3}, "D58": {"style": 8, "format": 3, "border": 1}, "D59": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "D60": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",1)"}, "D61": {"format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",2)"}, "D62": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",3)"}, "D63": {"format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",4)"}, "D64": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",5)"}, "D65": {"format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",6)"}, "D66": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",7)"}, "D67": {"format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",8)"}, "D68": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",9)"}, "D69": {"format": 3, "content": "=ODOO.PIVOT(7,\"price_subtotal\",\"#team_id\",10)"}, "D70": {"format": 3}, "D71": {"style": 8, "format": 3, "border": 1}, "D72": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "D73": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",1)"}, "D74": {"format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",2)"}, "D75": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",3)"}, "D76": {"format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",4)"}, "D77": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",5)"}, "D78": {"format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",6)"}, "D79": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",7)"}, "D80": {"format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",8)"}, "D81": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",9)"}, "D82": {"format": 3, "content": "=ODOO.PIVOT(9,\"price_subtotal\",\"#source_id\",10)"}, "D85": {"format": 3}, "D86": {"format": 3}, "E20": {"style": 12}, "F19": {"style": 1, "content": "[Top Sales Orders](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":[[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[]},\"modelName\":\"sale.order\",\"views\":[[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Orders\"})", "border": 1}, "F20": {"style": 2, "content": "=_t(\"Order\")", "border": 2}, "F21": {"style": 3, "content": "=ODOO.LIST(2,1,\"name\")"}, "F22": {"style": 4, "content": "=ODOO.LIST(2,2,\"name\")"}, "F23": {"style": 3, "content": "=ODOO.LIST(2,3,\"name\")"}, "F24": {"style": 4, "content": "=ODOO.LIST(2,4,\"name\")"}, "F25": {"style": 3, "content": "=ODOO.LIST(2,5,\"name\")"}, "F26": {"style": 4, "content": "=ODOO.LIST(2,6,\"name\")"}, "F27": {"style": 3, "content": "=ODOO.LIST(2,7,\"name\")"}, "F28": {"style": 4, "content": "=ODOO.LIST(2,8,\"name\")"}, "F29": {"style": 3, "content": "=ODOO.LIST(2,9,\"name\")"}, "F30": {"style": 4, "content": "=ODOO.LIST(2,10,\"name\")"}, "F32": {"style": 1, "content": "[Top Products](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"product_tmpl_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "F33": {"style": 5, "content": "=_t(\"Product\")", "border": 2}, "F34": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",1)"}, "F35": {"style": 7, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",2)"}, "F36": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",3)"}, "F37": {"style": 7, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",4)"}, "F38": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",5)"}, "F39": {"style": 7, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",6)"}, "F40": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",7)"}, "F41": {"style": 7, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",8)"}, "F42": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",9)"}, "F43": {"style": 7, "content": "=ODOO.PIVOT.HEADER(6,\"#product_id\",10)"}, "F45": {"style": 1, "content": "[Top Categories](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"categ_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"categ_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"categ_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "F46": {"style": 5, "content": "=_t(\"Category\")", "border": 2}, "F47": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",1)"}, "F48": {"style": 7, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",2)"}, "F49": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",3)"}, "F50": {"style": 7, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",4)"}, "F51": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",5)"}, "F52": {"style": 7, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",6)"}, "F53": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",7)"}, "F54": {"style": 7, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",8)"}, "F55": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",9)"}, "F56": {"style": 7, "content": "=ODOO.PIVOT.HEADER(3,\"#categ_id\",10)"}, "F58": {"style": 1, "content": "[Top Salespeople](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"user_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"user_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"user_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "F59": {"style": 5, "content": "=_t(\"Salesperson\")", "border": 2}, "F60": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",1)"}, "F61": {"style": 7, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",2)"}, "F62": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",3)"}, "F63": {"style": 7, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",4)"}, "F64": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",5)"}, "F65": {"style": 7, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",6)"}, "F66": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",7)"}, "F67": {"style": 7, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",8)"}, "F68": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",9)"}, "F69": {"style": 7, "content": "=ODOO.PIVOT.HEADER(8,\"#user_id\",10)"}, "F71": {"style": 1, "content": "[Top Mediums](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"medium_id\",\"!=\",false],[\"state\",\"not in\",[\"draft\",\"sent\",\"cancel\"]]],\"context\":{\"group_by\":[\"medium_id\"],\"pivot_measures\":[\"order_id\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"medium_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "F72": {"style": 5, "content": "=_t(\"Medium\")", "border": 2}, "F73": {"style": 6, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",1)"}, "F74": {"style": 7, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",2)"}, "F75": {"style": 6, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",3)"}, "F76": {"style": 7, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",4)"}, "F77": {"style": 6, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",5)"}, "F78": {"style": 7, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",6)"}, "F79": {"style": 6, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",7)"}, "F80": {"style": 7, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",8)"}, "F81": {"style": 6, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",9)"}, "F82": {"style": 7, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",10)"}, "G19": {"style": 8, "border": 1}, "G20": {"style": 2, "content": "=_t(\"Customer\")", "border": 2}, "G21": {"style": 9, "content": "=ODOO.LIST(2,1,\"partner_id\")"}, "G22": {"content": "=ODOO.LIST(2,2,\"partner_id\")"}, "G23": {"style": 9, "content": "=ODOO.LIST(2,3,\"partner_id\")"}, "G24": {"content": "=ODOO.LIST(2,4,\"partner_id\")"}, "G25": {"style": 9, "content": "=ODOO.LIST(2,5,\"partner_id\")"}, "G26": {"content": "=ODOO.LIST(2,6,\"partner_id\")"}, "G27": {"style": 9, "content": "=ODOO.LIST(2,7,\"partner_id\")"}, "G28": {"content": "=ODOO.LIST(2,8,\"partner_id\")"}, "G29": {"style": 9, "content": "=ODOO.LIST(2,9,\"partner_id\")"}, "G30": {"content": "=ODOO.LIST(2,10,\"partner_id\")"}, "G32": {"style": 8, "border": 1}, "G33": {"style": 5, "border": 2}, "G34": {"style": 9}, "G36": {"style": 9}, "G38": {"style": 9}, "G40": {"style": 9}, "G42": {"style": 9}, "G45": {"style": 8, "border": 1}, "G46": {"style": 5, "border": 2}, "G47": {"style": 9}, "G49": {"style": 9}, "G51": {"style": 9}, "G53": {"style": 9}, "G55": {"style": 9}, "G58": {"style": 8, "border": 1}, "G59": {"style": 5, "border": 2}, "G60": {"style": 9}, "G62": {"style": 9}, "G64": {"style": 9}, "G66": {"style": 9}, "G68": {"style": 9}, "G71": {"style": 8, "border": 1}, "G72": {"style": 5, "border": 2}, "G73": {"style": 9}, "G75": {"style": 9}, "G77": {"style": 9}, "G79": {"style": 9}, "G81": {"style": 9}, "H19": {"style": 8, "border": 1}, "H20": {"style": 2, "content": "=_t(\"Salesperson\")", "border": 2}, "H21": {"style": 9, "content": "=ODOO.LIST(2,1,\"user_id\")"}, "H22": {"content": "=ODOO.LIST(2,2,\"user_id\")"}, "H23": {"style": 9, "content": "=ODOO.LIST(2,3,\"user_id\")"}, "H24": {"content": "=ODOO.LIST(2,4,\"user_id\")"}, "H25": {"style": 9, "content": "=ODOO.LIST(2,5,\"user_id\")"}, "H26": {"content": "=ODOO.LIST(2,6,\"user_id\")"}, "H27": {"style": 9, "content": "=ODOO.LIST(2,7,\"user_id\")"}, "H28": {"content": "=ODOO.LIST(2,8,\"user_id\")"}, "H29": {"style": 9, "content": "=ODOO.LIST(2,9,\"user_id\")"}, "H30": {"content": "=ODOO.LIST(2,10,\"user_id\")"}, "H32": {"style": 8, "border": 1}, "H33": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "H34": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",1)"}, "H35": {"format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",2)"}, "H36": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",3)"}, "H37": {"format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",4)"}, "H38": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",5)"}, "H39": {"format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",6)"}, "H40": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",7)"}, "H41": {"format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",8)"}, "H42": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",9)"}, "H43": {"format": 1, "content": "=ODOO.PIVOT(6,\"order_id\",\"#product_id\",10)"}, "H45": {"style": 8, "border": 1}, "H46": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "H47": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",1)"}, "H48": {"format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",2)"}, "H49": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",3)"}, "H50": {"format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",4)"}, "H51": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",5)"}, "H52": {"format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",6)"}, "H53": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",7)"}, "H54": {"format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",8)"}, "H55": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",9)"}, "H56": {"format": 1, "content": "=ODOO.PIVOT(3,\"order_id\",\"#categ_id\",10)"}, "H58": {"style": 8, "border": 1}, "H59": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "H60": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",1)"}, "H61": {"format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",2)"}, "H62": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",3)"}, "H63": {"format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",4)"}, "H64": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",5)"}, "H65": {"format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",6)"}, "H66": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",7)"}, "H67": {"format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",8)"}, "H68": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",9)"}, "H69": {"format": 1, "content": "=ODOO.PIVOT(8,\"order_id\",\"#user_id\",10)"}, "H71": {"style": 8, "border": 1}, "H72": {"style": 10, "content": "=_t(\"Orders\")", "border": 2}, "H73": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",1)"}, "H74": {"format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",2)"}, "H75": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",3)"}, "H76": {"format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",4)"}, "H77": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",5)"}, "H78": {"format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",6)"}, "H79": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",7)"}, "H80": {"format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",8)"}, "H81": {"style": 9, "format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",9)"}, "H82": {"format": 1, "content": "=ODOO.PIVOT(10,\"order_id\",\"#medium_id\",10)"}, "I19": {"style": 8, "border": 1}, "I20": {"style": 11, "content": "=_t(\"Revenue\")", "border": 2}, "I21": {"style": 9, "content": "=ODOO.LIST(2,1,\"amount_untaxed\")"}, "I22": {"content": "=ODOO.LIST(2,2,\"amount_untaxed\")"}, "I23": {"style": 9, "content": "=ODOO.LIST(2,3,\"amount_untaxed\")"}, "I24": {"content": "=ODOO.LIST(2,4,\"amount_untaxed\")"}, "I25": {"style": 9, "content": "=ODOO.LIST(2,5,\"amount_untaxed\")"}, "I26": {"content": "=ODOO.LIST(2,6,\"amount_untaxed\")"}, "I27": {"style": 9, "content": "=ODOO.LIST(2,7,\"amount_untaxed\")"}, "I28": {"content": "=ODOO.LIST(2,8,\"amount_untaxed\")"}, "I29": {"style": 9, "content": "=ODOO.LIST(2,9,\"amount_untaxed\")"}, "I30": {"content": "=ODOO.LIST(2,10,\"amount_untaxed\")"}, "I32": {"style": 8, "border": 1}, "I33": {"style": 10, "content": "=_t(\"Revenue\")", "border": 2}, "I34": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",1)"}, "I35": {"format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",2)"}, "I36": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",3)"}, "I37": {"format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",4)"}, "I38": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",5)"}, "I39": {"format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",6)"}, "I40": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",7)"}, "I41": {"format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",8)"}, "I42": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",9)"}, "I43": {"format": 3, "content": "=ODOO.PIVOT(6,\"price_subtotal\",\"#product_id\",10)"}, "I44": {"format": 3}, "I45": {"style": 8, "format": 3, "border": 1}, "I46": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "I47": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",1)"}, "I48": {"format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",2)"}, "I49": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",3)"}, "I50": {"format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",4)"}, "I51": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",5)"}, "I52": {"format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",6)"}, "I53": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",7)"}, "I54": {"format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",8)"}, "I55": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",9)"}, "I56": {"format": 3, "content": "=ODOO.PIVOT(3,\"price_subtotal\",\"#categ_id\",10)"}, "I57": {"format": 3}, "I58": {"style": 8, "format": 3, "border": 1}, "I59": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "I60": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",1)"}, "I61": {"format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",2)"}, "I62": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",3)"}, "I63": {"format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",4)"}, "I64": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",5)"}, "I65": {"format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",6)"}, "I66": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",7)"}, "I67": {"format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",8)"}, "I68": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",9)"}, "I69": {"format": 3, "content": "=ODOO.PIVOT(8,\"price_subtotal\",\"#user_id\",10)"}, "I70": {"format": 3}, "I71": {"style": 8, "format": 3, "border": 1}, "I72": {"style": 10, "format": 3, "content": "=_t(\"Revenue\")", "border": 2}, "I73": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",1)"}, "I74": {"format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",2)"}, "I75": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",3)"}, "I76": {"format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",4)"}, "I77": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",5)"}, "I78": {"format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",6)"}, "I79": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",7)"}, "I80": {"format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",8)"}, "I81": {"style": 9, "format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",9)"}, "I82": {"format": 3, "content": "=ODOO.PIVOT(10,\"price_subtotal\",\"#medium_id\",10)"}, "I85": {"format": 3}, "I86": {"format": 3}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "C7": {"border": 1}, "C8": {"border": 2}, "D7": {"border": 1}, "D8": {"border": 2}, "E7": {"border": 1}, "E8": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "G7": {"border": 1}, "G8": {"border": 2}, "H7": {"border": 1}, "H8": {"border": 2}, "I7": {"border": 1}, "I8": {"border": 2}}, "conditionalFormats": [], "figures": [{"id": "51823220-f22b-4359-8711-579a249c91bb", "x": 0, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Quotations", "type": "scorecard", "background": "", "baseline": "Data!E4", "baselineDescr": "since last period", "keyValue": "Data!D4"}}, {"id": "9a38934c-b454-4a4b-88aa-17d1b80dbf5f", "x": 210, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Orders", "type": "scorecard", "background": "", "baseline": "Data!E5", "baselineDescr": "since last period", "keyValue": "Data!D5"}}, {"id": "67858d0e-b5ba-4a3c-bf9e-c0fceaeedf65", "x": 420, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Revenue", "type": "scorecard", "background": "", "baseline": "Data!E7", "baselineDescr": "since last period", "keyValue": "Data!D7"}}, {"id": "d43375c1-73a6-42a2-8dbd-0f13c285824f", "x": 630, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Average Order", "type": "scorecard", "background": "", "baseline": "Data!E8", "baselineDescr": "since last period", "keyValue": "Data!D8"}}, {"id": "a527960b-0812-4291-baba-f6b4b5280a0d", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["date:month"], "measure": "price_subtotal", "order": null, "resModel": "sale.report"}, "searchParams": {"comparison": null, "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["state", "not in", ["draft", "cancel", "sent"]]], "groupBy": ["date:month"], "orderBy": []}, "type": "odoo_line", "verticalAxisPosition": "left"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "eae01f9c-c461-4489-ade4-957ef2459d40", "name": "Data", "colNumber": 26, "rowNumber": 103, "rows": {}, "cols": {"0": {"size": 160.46142578125}, "1": {"size": 89.46142578125}, "2": {"size": 89.46142578125}, "3": {"size": 89.46142578125}, "4": {"size": 89.46142578125}}, "merges": [], "cells": {"A1": {"style": 5, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Draft quotations\")"}, "A3": {"content": "=_t(\"Quotations sent\")"}, "A4": {"content": "=_t(\"Total quotations\")"}, "A5": {"content": "=_t(\"Orders\")"}, "A6": {"content": "=_t(\"Total orders\")"}, "A7": {"content": "=_t(\"Revenue\")"}, "A8": {"content": "=_t(\"Average order amount\")"}, "B1": {"style": 5, "content": "=_t(\"Current\")"}, "B2": {"content": "=ODOO.PIVOT(11,\"__count\",\"state\",\"draft\")"}, "B3": {"content": "=ODOO.PIVOT(11,\"__count\",\"state\",\"sent\")"}, "B4": {"content": "=B2+B3"}, "B5": {"content": "=ODOO.PIVOT(11,\"__count\",\"state\",\"sale\")"}, "B6": {"content": "=ODOO.PIVOT(11,\"__count\")"}, "B7": {"content": "=ODOO.PIVOT(11,\"amount_untaxed\")"}, "B8": {"content": "=IFERROR(B7/B6)"}, "C1": {"style": 5, "content": "=_t(\"Previous\")"}, "C2": {"content": "=ODOO.PIVOT(12,\"__count\",\"state\",\"draft\")"}, "C3": {"content": "=ODOO.PIVOT(12,\"__count\",\"state\",\"sent\")"}, "C4": {"content": "=C2+C3"}, "C5": {"content": "=ODOO.PIVOT(12,\"__count\",\"state\",\"sale\")"}, "C6": {"content": "=ODOO.PIVOT(12,\"__count\")"}, "C7": {"content": "=ODOO.PIVOT(12,\"amount_untaxed\")"}, "C8": {"content": "=IFERROR(C7/C6)"}, "D1": {"style": 5, "content": "=_t(\"Current\")"}, "D2": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "D3": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "D4": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "D5": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B5)"}, "D6": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B6)"}, "D7": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B7)"}, "D8": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(B8)"}, "E1": {"style": 5, "content": "=_t(\"Previous\")"}, "E2": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C2)"}, "E3": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C3)"}, "E4": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C4)"}, "E5": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C5)"}, "E6": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C6)"}, "E7": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C7)"}, "E8": {"style": 9, "content": "=FORMAT.LARGE.NUMBER(C8)"}, "F1": {"style": 5}, "G1": {"style": 5}, "H1": {"style": 5}, "I1": {"style": 5}, "J1": {"style": 5}, "K1": {"style": 5}, "L1": {"style": 5}, "M1": {"style": 5}, "N1": {"style": 5}, "O1": {"style": 5}, "P1": {"style": 5}, "Q1": {"style": 5}, "R1": {"style": 5}, "S1": {"style": 5}, "T1": {"style": 5}, "U1": {"style": 5}, "V1": {"style": 5}, "W1": {"style": 5}, "X1": {"style": 5}, "Y1": {"style": 5}, "Z1": {"style": 5}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true, "fillColor": ""}, "3": {"fillColor": "#f2f2f2", "textColor": "#01666b"}, "4": {"textColor": "#01666b"}, "5": {"bold": true}, "6": {"fillColor": "#f2f2f2", "textColor": "#741b47"}, "7": {"textColor": "#741b47"}, "8": {"fontSize": 16, "bold": true}, "9": {"fillColor": "#f2f2f2"}, "10": {"bold": true, "align": "right"}, "11": {"bold": true, "fillColor": "", "align": "right"}, "12": {"fillColor": ""}}, "formats": {"1": "0", "2": "[$$]#,##0", "3": "#,##0"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "chartOdooMenusReferences": {"a527960b-0812-4291-baba-f6b4b5280a0d": "sale.menu_sale_order", "51823220-f22b-4359-8711-579a249c91bb": "sale.menu_sale_quotations", "9a38934c-b454-4a4b-88aa-17d1b80dbf5f": "sale.menu_sale_order", "67858d0e-b5ba-4a3c-bf9e-c0fceaeedf65": "sale.menu_sale_report", "d43375c1-73a6-42a2-8dbd-0f13c285824f": "sale.menu_sale_report"}, "odooVersion": 4, "lists": {"1": {"columns": ["name", "partner_id", "user_id", "amount_untaxed"], "domain": ["|", ["state", "=", "draft"], ["state", "=", "sent"]], "model": "sale.order", "context": {}, "orderBy": [{"name": "amount_untaxed", "asc": false}], "id": "1", "name": "Quotations by Untaxed Amount"}, "2": {"columns": ["name", "partner_id", "user_id", "amount_untaxed"], "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "model": "sale.order", "context": {}, "orderBy": [{"name": "amount_untaxed", "asc": false}, {"name": "invoice_status", "asc": true}], "id": "2", "name": "Sales Orders by Untaxed Amount"}}, "listNextId": 3, "pivots": {"3": {"colGroupBys": [], "context": {"params": {"action": 1275, "model": "sale.report", "view_type": "pivot", "menu_id": 878, "cids": 1}, "group_by_no_leaf": 1, "group_by": []}, "domain": [["categ_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "3", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["categ_id"], "name": "Sales Analysis by Product Category", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "4": {"colGroupBys": [], "context": {"params": {"action": 1275, "model": "sale.report", "view_type": "pivot", "menu_id": 878, "cids": 1}, "group_by_no_leaf": 1, "group_by": []}, "domain": [["partner_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "4", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["partner_id"], "name": "Sales Analysis by Customer", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "5": {"colGroupBys": [], "context": {"params": {"action": 1275, "model": "sale.report", "view_type": "pivot", "menu_id": 878, "cids": 1}, "group_by_no_leaf": 1, "group_by": []}, "domain": [["country_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "5", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["country_id"], "name": "Sales Analysis by Customer Country", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "6": {"colGroupBys": [], "context": {"params": {"action": 1275, "model": "sale.report", "view_type": "pivot", "menu_id": 878, "cids": 1}, "group_by_no_leaf": 1, "group_by": []}, "domain": [["product_tmpl_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "6", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["product_id"], "name": "Sales Analysis by Product Variant", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "7": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["team_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "7", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["team_id"], "name": "Sales Analysis by Sales Team", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "8": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["user_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "8", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["user_id"], "name": "Sales Analysis by Salesperson", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "9": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["source_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "9", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["source_id"], "name": "Sales Analysis by Source", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "10": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["medium_id", "!=", false], ["state", "not in", ["draft", "sent", "cancel"]]], "id": "10", "measures": [{"field": "order_id"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["medium_id"], "name": "Sales Analysis by Medium", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "11": {"colGroupBys": [], "context": {}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "id": "11", "measures": [{"field": "__count"}, {"field": "amount_untaxed"}], "model": "sale.order", "rowGroupBys": ["state"], "name": "so stats - current", "sortedColumn": null}, "12": {"colGroupBys": [], "context": {}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "id": "12", "measures": [{"field": "__count"}, {"field": "amount_untaxed"}], "model": "sale.order", "rowGroupBys": ["state"], "name": "so stats - previous", "sortedColumn": null}}, "pivotNextId": 13, "globalFilters": [{"id": "13d30fda-b14d-4a56-b186-25468af3b1e9", "type": "date", "label": "Period", "defaultValue": "last_three_months", "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "date", "type": "datetime", "offset": 0}, "4": {"field": "date", "type": "datetime", "offset": 0}, "5": {"field": "date", "type": "datetime", "offset": 0}, "6": {"field": "date", "type": "datetime", "offset": 0}, "7": {"field": "date", "type": "datetime", "offset": 0}, "8": {"field": "date", "type": "datetime", "offset": 0}, "9": {"field": "date", "type": "datetime", "offset": 0}, "10": {"field": "date", "type": "datetime", "offset": 0}, "11": {"field": "date_order", "type": "datetime", "offset": 0}, "12": {"field": "date_order", "type": "datetime", "offset": -1}}, "listFields": {"1": {"field": "date_order", "type": "datetime", "offset": 0}, "2": {"field": "date_order", "type": "datetime", "offset": 0}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "date", "type": "datetime", "offset": 0}}}, {"id": "e6db018b-19ec-42c3-b29e-11b1e3910916", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "country_id", "type": "many2one"}, "4": {"field": "country_id", "type": "many2one"}, "5": {"field": "country_id", "type": "many2one"}, "6": {"field": "country_id", "type": "many2one"}, "7": {"field": "country_id", "type": "many2one"}, "8": {"field": "country_id", "type": "many2one"}, "9": {"field": "country_id", "type": "many2one"}, "10": {"field": "country_id", "type": "many2one"}, "11": {"field": "partner_id.country_id", "type": "many2one"}, "12": {"field": "partner_id.country_id", "type": "many2one"}}, "listFields": {"1": {"field": "partner_id.country_id", "type": "many2one"}, "2": {"field": "partner_id.country_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "country_id", "type": "many2one"}}}, {"id": "dbb716a1-5977-47ee-a53e-ca5716303433", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "product_id", "type": "many2one"}, "4": {"field": "product_id", "type": "many2one"}, "5": {"field": "product_id", "type": "many2one"}, "6": {"field": "product_id", "type": "many2one"}, "7": {"field": "product_id", "type": "many2one"}, "8": {"field": "product_id", "type": "many2one"}, "9": {"field": "product_id", "type": "many2one"}, "10": {"field": "product_id", "type": "many2one"}, "11": {"field": "order_line.product_id", "type": "many2one"}, "12": {"field": "order_line.product_id", "type": "many2one"}}, "listFields": {"1": {"field": "order_line.product_id", "type": "many2one"}, "2": {"field": "order_line.product_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "product_id", "type": "many2one"}}}, {"id": "7d95fe28-fd13-4805-948c-95742e7d6733", "type": "relation", "label": "Customer", "modelName": "res.partner", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "partner_id", "type": "many2one"}, "4": {"field": "partner_id", "type": "many2one"}, "5": {"field": "partner_id", "type": "many2one"}, "6": {"field": "partner_id", "type": "many2one"}, "7": {"field": "partner_id", "type": "many2one"}, "8": {"field": "partner_id", "type": "many2one"}, "9": {"field": "partner_id", "type": "many2one"}, "10": {"field": "partner_id", "type": "many2one"}, "11": {"field": "partner_id", "type": "many2one"}, "12": {"field": "partner_id", "type": "many2one"}}, "listFields": {"1": {"field": "partner_id", "type": "many2one"}, "2": {"field": "partner_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "partner_id", "type": "many2one"}}}, {"id": "27e323f4-cd70-4345-82fa-dfc855707bc1", "type": "relation", "label": "Category", "modelName": "product.category", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "categ_id", "type": "many2one"}, "4": {"field": "categ_id", "type": "many2one"}, "5": {"field": "categ_id", "type": "many2one"}, "6": {"field": "categ_id", "type": "many2one"}, "7": {"field": "categ_id", "type": "many2one"}, "8": {"field": "categ_id", "type": "many2one"}, "9": {"field": "categ_id", "type": "many2one"}, "10": {"field": "categ_id", "type": "many2one"}, "11": {"field": "order_line.product_id.categ_id", "type": "many2one"}, "12": {"field": "order_line.product_id.categ_id", "type": "many2one"}}, "listFields": {"1": {"field": "order_line.product_id.categ_id", "type": "many2one"}, "2": {"field": "order_line.product_id.categ_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "categ_id", "type": "many2one"}}}, {"id": "b75963ca-ab5f-4da5-9c90-67526517e5e7", "type": "relation", "label": "Sales Team", "modelName": "crm.team", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "team_id", "type": "many2one"}, "4": {"field": "team_id", "type": "many2one"}, "5": {"field": "team_id", "type": "many2one"}, "6": {"field": "team_id", "type": "many2one"}, "7": {"field": "team_id", "type": "many2one"}, "8": {"field": "team_id", "type": "many2one"}, "9": {"field": "team_id", "type": "many2one"}, "10": {"field": "team_id", "type": "many2one"}, "11": {"field": "team_id", "type": "many2one"}, "12": {"field": "team_id", "type": "many2one"}}, "listFields": {"1": {"field": "team_id", "type": "many2one"}, "2": {"field": "team_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "team_id", "type": "many2one"}}}, {"id": "4181e8e3-2e7e-42be-a88b-f24acb7d03e7", "type": "relation", "label": "Salesperson", "modelName": "res.users", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "user_id", "type": "many2one"}, "4": {"field": "user_id", "type": "many2one"}, "5": {"field": "user_id", "type": "many2one"}, "6": {"field": "user_id", "type": "many2one"}, "7": {"field": "user_id", "type": "many2one"}, "8": {"field": "user_id", "type": "many2one"}, "9": {"field": "user_id", "type": "many2one"}, "10": {"field": "user_id", "type": "many2one"}, "11": {"field": "user_id", "type": "many2one"}, "12": {"field": "user_id", "type": "many2one"}}, "listFields": {"1": {"field": "user_id", "type": "many2one"}, "2": {"field": "user_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "user_id", "type": "many2one"}}}, {"id": "ec466626-f54a-4955-9611-eaa2719f1afb", "type": "relation", "label": "Source", "modelName": "utm.source", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "source_id", "type": "many2one"}, "4": {"field": "source_id", "type": "many2one"}, "5": {"field": "source_id", "type": "many2one"}, "6": {"field": "source_id", "type": "many2one"}, "7": {"field": "source_id", "type": "many2one"}, "8": {"field": "source_id", "type": "many2one"}, "9": {"field": "source_id", "type": "many2one"}, "10": {"field": "source_id", "type": "many2one"}, "11": {"field": "source_id", "type": "many2one"}, "12": {"field": "source_id", "type": "many2one"}}, "listFields": {"1": {"field": "source_id", "type": "many2one"}, "2": {"field": "source_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "source_id", "type": "many2one"}}}, {"id": "85d94827-6ce2-429c-9775-ef646cfddb6b", "type": "relation", "label": "Medium", "modelName": "utm.medium", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"3": {"field": "medium_id", "type": "many2one"}, "4": {"field": "medium_id", "type": "many2one"}, "5": {"field": "medium_id", "type": "many2one"}, "6": {"field": "medium_id", "type": "many2one"}, "7": {"field": "medium_id", "type": "many2one"}, "8": {"field": "medium_id", "type": "many2one"}, "9": {"field": "medium_id", "type": "many2one"}, "10": {"field": "medium_id", "type": "many2one"}, "11": {"field": "medium_id", "type": "many2one"}, "12": {"field": "medium_id", "type": "many2one"}}, "listFields": {"1": {"field": "medium_id", "type": "many2one"}, "2": {"field": "medium_id", "type": "many2one"}}, "graphFields": {"a527960b-0812-4291-baba-f6b4b5280a0d": {"field": "medium_id", "type": "many2one"}}}]}