)]}'
{"version": 3, "sources": ["/web_editor/static/lib/cropperjs/cropper.css", "/web/static/lib/bootstrap/scss/_functions.scss", "/web/static/lib/bootstrap/scss/_mixins.scss", "/web/static/src/scss/mixins_forwardport.scss", "/web/static/src/scss/bs_mixins_overrides.scss", "/web/static/src/legacy/scss/utils.scss", "/web_enterprise/static/src/scss/primary_variables.scss", "/web/static/src/scss/primary_variables.scss", "/web_enterprise/static/src/core/notifications/notifications.variables.scss", "/web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss", "/web_enterprise/static/src/webclient/navbar/navbar.variables.scss", "/web/static/src/core/notifications/notification.variables.scss", "/web/static/src/search/control_panel/control_panel.variables.scss", "/web/static/src/search/search_panel/search_panel.variables.scss", "/web/static/src/views/form/form.variables.scss", "/web/static/src/views/kanban/kanban.variables.scss", "/web/static/src/webclient/burger_menu/burger_menu.variables.scss", "/web/static/src/webclient/navbar/navbar.variables.scss", "/base/static/src/scss/onboarding.variables.scss", "/mail/static/src/scss/variables/primary_variables.scss", "/web_editor/static/src/scss/web_editor.variables.scss", "/web_editor/static/src/scss/wysiwyg.variables.scss", "/portal/static/src/scss/primary_variables.scss", "/account/static/src/scss/variables.scss", "/website/static/src/scss/primary_variables.scss", "/website/static/src/scss/options/user_values.scss", "/website/static/src/scss/options/colors/user_color_palette.scss", "/website/static/src/scss/options/colors/user_gray_color_palette.scss", "/website/static/src/scss/options/colors/user_theme_color_palette.scss", "/documents/static/src/scss/documents.variables.scss", "/hr_org_chart/static/src/scss/variables.scss", "/website/static/src/snippets/s_badge/000_variables.scss", "/website/static/src/snippets/s_product_list/000_variables.scss", "/website/static/src/scss/secondary_variables.scss", "/web_enterprise/static/src/scss/secondary_variables.scss", "/web/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/bootstrap_overridden.scss", "/web/static/src/scss/pre_variables.scss", "/web/static/lib/bootstrap/scss/_variables.scss", "/web_editor/static/src/js/editor/odoo-editor/src/style.scss", "/web_editor/static/src/scss/wysiwyg.scss", "/web_editor/static/src/scss/wysiwyg_iframe.scss", "/web_editor/static/src/scss/wysiwyg_snippets.scss"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClTA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChBA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1eA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["\n/* /web_editor/static/lib/cropperjs/cropper.css */\n/*!\n * Cropper.js v1.5.5\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present Chen Fengyuan\n * Released under the MIT license\n *\n * Date: 2019-08-04T02:26:27.232Z\n */\n\n .cropper-container {\n  direction: ltr;\n  font-size: 0;\n  line-height: 0;\n  position: relative;\n  -ms-touch-action: none;\n  touch-action: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.cropper-container img {\n  display: block;\n  height: 100%;\n  image-orientation: 0deg;\n  max-height: none !important;\n  max-width: none !important;\n  min-height: 0 !important;\n  min-width: 0 !important;\n  width: 100%;\n}\n\n.cropper-wrap-box,\n.cropper-canvas,\n.cropper-drag-box,\n.cropper-crop-box,\n.cropper-modal {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.cropper-wrap-box,\n.cropper-canvas {\n  overflow: hidden;\n}\n\n.cropper-drag-box {\n  background-color: #fff;\n  opacity: 0;\n}\n\n.cropper-modal {\n  background-color: #000;\n  opacity: 0.5;\n}\n\n.cropper-view-box {\n  display: block;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  overflow: hidden;\n  width: 100%;\n}\n\n.cropper-dashed {\n  border: 0 dashed #eee;\n  display: block;\n  opacity: 0.5;\n  position: absolute;\n}\n\n.cropper-dashed.dashed-h {\n  border-bottom-width: 1px;\n  border-top-width: 1px;\n  height: calc(100% / 3);\n  left: 0;\n  top: calc(100% / 3);\n  width: 100%;\n}\n\n.cropper-dashed.dashed-v {\n  border-left-width: 1px;\n  border-right-width: 1px;\n  height: 100%;\n  left: calc(100% / 3);\n  top: 0;\n  width: calc(100% / 3);\n}\n\n.cropper-center {\n  display: block;\n  height: 0;\n  left: 50%;\n  opacity: 0.75;\n  position: absolute;\n  top: 50%;\n  width: 0;\n}\n\n.cropper-center::before,\n.cropper-center::after {\n  background-color: #eee;\n  content: ' ';\n  display: block;\n  position: absolute;\n}\n\n.cropper-center::before {\n  height: 1px;\n  left: -3px;\n  top: 0;\n  width: 7px;\n}\n\n.cropper-center::after {\n  height: 7px;\n  left: 0;\n  top: -3px;\n  width: 1px;\n}\n\n.cropper-face,\n.cropper-line,\n.cropper-point {\n  display: block;\n  height: 100%;\n  opacity: 0.1;\n  position: absolute;\n  width: 100%;\n}\n\n.cropper-face {\n  background-color: #fff;\n  left: 0;\n  top: 0;\n}\n\n.cropper-line {\n  background-color: #39f;\n}\n\n.cropper-line.line-e {\n  cursor: ew-resize;\n  right: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-n {\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n  top: -3px;\n}\n\n.cropper-line.line-w {\n  cursor: ew-resize;\n  left: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-s {\n  bottom: -3px;\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n}\n\n.cropper-point {\n  background-color: #39f;\n  height: 5px;\n  opacity: 0.75;\n  width: 5px;\n}\n\n.cropper-point.point-e {\n  cursor: ew-resize;\n  margin-top: -3px;\n  right: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-n {\n  cursor: ns-resize;\n  left: 50%;\n  margin-left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-w {\n  cursor: ew-resize;\n  left: -3px;\n  margin-top: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-s {\n  bottom: -3px;\n  cursor: s-resize;\n  left: 50%;\n  margin-left: -3px;\n}\n\n.cropper-point.point-ne {\n  cursor: nesw-resize;\n  right: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-nw {\n  cursor: nwse-resize;\n  left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-sw {\n  bottom: -3px;\n  cursor: nesw-resize;\n  left: -3px;\n}\n\n.cropper-point.point-se {\n  bottom: -3px;\n  cursor: nwse-resize;\n  height: 20px;\n  opacity: 1;\n  right: -3px;\n  width: 20px;\n}\n\n@media (min-width: 768px) {\n  .cropper-point.point-se {\n    height: 15px;\n    width: 15px;\n  }\n}\n\n@media (min-width: 992px) {\n  .cropper-point.point-se {\n    height: 10px;\n    width: 10px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cropper-point.point-se {\n    height: 5px;\n    opacity: 0.75;\n    width: 5px;\n  }\n}\n\n.cropper-point.point-se::before {\n  background-color: #39f;\n  bottom: -50%;\n  content: ' ';\n  display: block;\n  height: 200%;\n  opacity: 0;\n  position: absolute;\n  right: -50%;\n  width: 200%;\n}\n\n.cropper-invisible {\n  opacity: 0;\n}\n\n.cropper-bg {\n  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');\n}\n\n.cropper-hide {\n  display: block;\n  height: 0;\n  position: absolute;\n  width: 0;\n}\n\n.cropper-hidden {\n  display: none !important;\n}\n\n.cropper-move {\n  cursor: move;\n}\n\n.cropper-crop {\n  cursor: crosshair;\n}\n\n.cropper-disabled .cropper-drag-box,\n.cropper-disabled .cropper-face,\n.cropper-disabled .cropper-line,\n.cropper-disabled .cropper-point {\n  cursor: not-allowed;\n}\n", "\n/* /web/static/lib/bootstrap/scss/_functions.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_mixins.scss */\n\n", "\n/* /web/static/src/scss/mixins_forwardport.scss */\n\n", "\n/* /web/static/src/scss/bs_mixins_overrides.scss */\n\n", "\n/* /web/static/src/legacy/scss/utils.scss */\n\nwe-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale, .o_we_cc_preview_wrapper, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn {\n  position: relative;\n  z-index: 0;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::before, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::before, .o_we_cc_preview_wrapper::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background-image: url(\"/web/static/img/transparent.png\");\n  background-size: 10px auto;\n  border-radius: inherit;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::after, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::after, .o_we_cc_preview_wrapper::after, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background: inherit;\n  border-radius: inherit;\n}\n\n", "\n/* /web_enterprise/static/src/scss/primary_variables.scss */\n\n", "\n/* /web/static/src/scss/primary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /web/static/src/core/notifications/notification.variables.scss */\n\n", "\n/* /web/static/src/search/control_panel/control_panel.variables.scss */\n\n", "\n/* /web/static/src/search/search_panel/search_panel.variables.scss */\n\n", "\n/* /web/static/src/views/form/form.variables.scss */\n\n", "\n/* /web/static/src/views/kanban/kanban.variables.scss */\n\n", "\n/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */\n\n", "\n/* /web/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /base/static/src/scss/onboarding.variables.scss */\n\n", "\n/* /mail/static/src/scss/variables/primary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/web_editor.variables.scss */\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.variables.scss */\n\n", "\n/* /portal/static/src/scss/primary_variables.scss */\n\n", "\n/* /account/static/src/scss/variables.scss */\n\n@keyframes animate-red {\n  0% {\n    color: red;\n  }\n  100% {\n    color: inherit;\n  }\n}\n\n.animate {\n  animation: animate-red 1s ease;\n}\n\n", "\n/* /website/static/src/scss/primary_variables.scss */\n\n", "\n/* /website/static/src/scss/options/user_values.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */\n\n", "\n/* /documents/static/src/scss/documents.variables.scss */\n\n", "\n/* /hr_org_chart/static/src/scss/variables.scss */\n\n", "\n/* /website/static/src/snippets/s_badge/000_variables.scss */\n\n", "\n/* /website/static/src/snippets/s_product_list/000_variables.scss */\n\n", "\n/* /website/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/bootstrap_overridden.scss */\n\n", "\n/* /web/static/src/scss/pre_variables.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_variables.scss */\n\n", "\n/* /web_editor/static/src/js/editor/odoo-editor/src/style.scss */\n\n.odoo-editor-editable ::selection {\n  /* For color conversion over white background, use X = (Y-(1-P)*255)/P where\n            X = converted color component (R, G, B) (0 <= X <= 255)\n            Y = desired apparent color component (R, G, B) (0 <= Y <= 255)\n            P = opacity (0 <= P <=1)\n            (limitation: Y + 255P >= 255)\n        */\n  background-color: rgba(117, 167, 249, 0.5) !important;\n  /* #bad3fc equivalent when over white*/\n}\n\n.odoo-editor-editable.o_col_resize {\n  cursor: col-resize;\n}\n\n.odoo-editor-editable.o_col_resize ::selection {\n  background-color: transparent;\n}\n\n.odoo-editor-editable.o_row_resize {\n  cursor: row-resize;\n}\n\n.odoo-editor-editable.o_row_resize ::selection {\n  background-color: transparent;\n}\n\n.o_selected_table {\n  caret-color: transparent;\n}\n\n.o_selected_table ::selection {\n  background-color: transparent !important;\n}\n\n.o_selected_table .o_selected_td {\n  background-color: rgba(117, 167, 249, 0.5) !important;\n  /* #bad3fc equivalent when over white*/\n  cursor: pointer !important;\n}\n\n.o_table_ui {\n  background-color: transparent;\n  position: absolute;\n  z-index: 10;\n  padding: 0;\n}\n\n.o_table_ui:hover {\n  visibility: visible !important;\n}\n\n.o_table_ui > div {\n  position: absolute;\n}\n\n.o_table_ui .o_table_ui_menu_toggler {\n  cursor: pointer;\n  background-color: var(--o-table-ui-bg, #FFF);\n  color: var(--o-table-ui-color, #4A4F59);\n  border: 1px solid var(--o-table-ui-border, #DEE2E6);\n  border-radius: 5px;\n  padding: 2px 3px;\n}\n\n.o_table_ui .o_table_ui_menu {\n  display: none;\n  cursor: pointer;\n  background-color: var(--o-table-ui-bg, #FFF);\n  width: fit-content;\n  border: 1px solid var(--o-table-ui-border, #DEE2E6);\n  padding: 5px 0;\n  white-space: nowrap;\n}\n\n.o_table_ui .o_table_ui_menu > div:hover {\n  background-color: var(--o-table-ui-hover, #E0E2E6);\n}\n\n.o_table_ui .o_table_ui_menu span {\n  margin-right: 8px;\n  color: var(--o-table-ui-color, #4A4F59);\n}\n\n.o_table_ui .o_table_ui_menu div {\n  padding: 0 8px;\n}\n\n.o_table_ui.o_open {\n  visibility: visible !important;\n}\n\n.o_table_ui.o_open .o_table_ui_menu {\n  display: block;\n}\n\n.o_table_ui.o_open .o_table_ui_menu > div.o_hide {\n  display: none;\n}\n\n.oe-floating {\n  box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23);\n  border-radius: 4px;\n  position: absolute;\n}\n\n/* toolbar styling */\n.oe-toolbar {\n  box-sizing: border-box;\n  position: absolute;\n  visibility: hidden;\n  height: fit-content;\n  width: fit-content;\n  padding-left: 5px;\n  padding-right: 5px;\n  background: #222222;\n  color: white;\n  border-radius: 8px;\n}\n\n.oe-toolbar .toolbar-bottom::before {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 0;\n  left: var(--arrow-left-pos);\n  top: var(--arrow-top-pos);\n  border: transparent 10px solid;\n  border-bottom: #222222 10px solid;\n  z-index: 0;\n}\n\n.oe-toolbar:not(.toolbar-bottom)::before {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 0;\n  left: var(--arrow-left-pos);\n  top: var(--arrow-top-pos);\n  border: transparent 10px solid;\n  border-top: #222222 10px solid;\n  z-index: 0;\n  pointer-events: none;\n}\n\n.oe-toolbar .button-group {\n  display: inline-block;\n  margin-right: 13px;\n}\n\n.oe-toolbar .button-group:last-of-type {\n  margin-right: 0;\n}\n\n.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  position: relative;\n  box-sizing: content-box;\n  display: inline-block;\n  padding: 7px;\n  color: white;\n}\n\n.oe-toolbar .btn:not(.disabled):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not(.disabled):hover {\n  background: #868686;\n}\n\n.oe-toolbar .oe-toolbar .dropdown-menu .btn, .oe-toolbar.oe-floating .oe-toolbar .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .oe-toolbar .dropdown-menu button {\n  background: #222222;\n}\n\n.oe-toolbar .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active {\n  background: #555555;\n}\n\n.oe-toolbar .dropdown-toggle {\n  background: transparent;\n  border: none;\n  padding: 7px;\n}\n\n.oe-toolbar .dropdown-toggle[aria-expanded=\"true\"] {\n  background: #555555;\n}\n\n.oe-toolbar .dropdown-menu {\n  background: #222222;\n  min-width: max-content;\n  min-width: -webkit-max-content;\n  text-align: center;\n}\n\n.oe-toolbar .dropdown-item {\n  background: transparent;\n  color: white;\n}\n\n.oe-toolbar .dropdown-item pre, .oe-toolbar .dropdown-item h1, .oe-toolbar .dropdown-item h2, .oe-toolbar .dropdown-item h3, .oe-toolbar .dropdown-item h4, .oe-toolbar .dropdown-item h5, .oe-toolbar .dropdown-item h6, .oe-toolbar .dropdown-item blockquote {\n  margin: 0;\n  color: white;\n}\n\n.oe-toolbar .dropdown-item:hover, .oe-toolbar .dropdown-item:focus {\n  color: white;\n  background: #868686;\n}\n\n.oe-toolbar .dropdown-item.active, .oe-toolbar .dropdown-item:active {\n  color: white;\n  background: #555555;\n}\n\n.oe-toolbar li > a.dropdown-item {\n  color: white;\n}\n\n.oe-toolbar label, .oe-toolbar label span {\n  display: inline-block;\n}\n\n.oe-toolbar input[type=\"color\"] {\n  width: 0;\n  height: 0;\n  padding: 0;\n  border: none;\n  box-sizing: border-box;\n  position: absolute;\n  opacity: 0;\n  top: 100%;\n  margin: 2px 0 0;\n}\n\n.oe-toolbar #colorInputButtonGroup label {\n  margin-bottom: 0;\n}\n\n.oe-toolbar .color-indicator {\n  background-color: transparent;\n  padding-bottom: 4px;\n}\n\n.oe-toolbar .color-indicator.fore-color {\n  border-bottom: 2px solid var(--fore-color);\n  padding: 5px;\n}\n\n.oe-toolbar .color-indicator.hilite-color {\n  border-bottom: 2px solid var(--hilite-color);\n  padding: 5px;\n}\n\n.oe-toolbar #style .dropdown-menu {\n  text-align: left;\n}\n\n.oe-tablepicker-dropdown .oe-tablepicker {\n  margin: -3px 2px -6px 2px;\n}\n\n.oe-tablepicker-wrapper.oe-floating {\n  padding: 3px;\n  z-index: 1056;\n  background-color: var(--oeTablepicker__wrapper-bg, #FFF);\n}\n\n.oe-tablepicker-row {\n  line-height: 0;\n}\n\n.oe-tablepicker {\n  width: max-content;\n  width: -webkit-max-content;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell {\n  display: inline-block;\n  background-color: var(--oeTablepicker__cell-bg, #E0E2E6);\n  width: 19px;\n  height: 19px;\n  padding: 0;\n  margin-right: 3px;\n  margin-bottom: 3px;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell:last-of-type {\n  margin-right: 0;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell.active {\n  background-color: var(--oeTablepicker-color-accent, #017e84);\n}\n\n.oe-tablepicker-size {\n  text-align: center;\n  margin-top: 7px;\n}\n\n.oe-tablepicker-dropdown .oe-tablepicker-size {\n  color: white;\n}\n\n@media only screen and (max-width: 767px) {\n  .oe-toolbar {\n    position: relative;\n    visibility: visible;\n    width: 100%;\n    border-radius: 0;\n    background-color: white;\n  }\n  .oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n    color: black;\n  }\n}\n\n/* Content styling */\n.oe-powerbox-wrapper {\n  position: absolute;\n  z-index: 1055;\n  border: black;\n  background: var(--oePowerbox__wrapper-bg, #FFF);\n  color: #4A4F59;\n  max-height: 40vh;\n  box-sizing: border-box;\n  max-width: 100%;\n  box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23);\n  border-radius: 4px;\n  overflow: hidden;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar {\n  background: transparent;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar {\n  width: 10px;\n  height: 10px;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar-thumb {\n  background: var(--oePowerbox__ScrollbarThumb-background-color, #D3D1CB);\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar-track {\n  background: var(--oePowerbox__ScrollbarTrack-background-color, #EDECE9);\n}\n\n.oe-powerbox-mainWrapper {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  overflow: auto;\n  padding: 5px 0;\n  overscroll-behavior: contain;\n}\n\n.oe-powerbox-category, .oe-powerbox-noResult {\n  margin: 10px;\n  color: var(--oePowerbox__category-color, #626774);\n  font-size: 11px;\n}\n\n.oe-powerbox-category {\n  text-transform: uppercase;\n  margin: 5px 12px;\n}\n\n.oe-powerbox-noResult {\n  display: none;\n}\n\n.oe-powerbox-commandWrapper {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  padding: 6px 12px;\n  cursor: pointer;\n}\n\n.oe-powerbox-commandWrapper.active {\n  background: var(--oePowerbox__commandName-bg, #f6f7fa);\n}\n\ni.oe-powerbox-commandImg {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  height: 30px;\n  width: 30px;\n  align-items: center;\n  justify-content: center;\n  background: var(--oePowerbox__commandImg-bg, #f6f7fa);\n  color: var(--oePowerbox__commandImg-color, #353840);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  border-radius: 7px;\n  font-size: 15px;\n}\n\n.oe-powerbox-commandName {\n  font-size: 13px;\n  color: var(--oePowerbox__commandName-color, #4A4F59);\n}\n\n.oe-powerbox-commandDescription {\n  color: var(--oePowerbox__commandDescription-color, rgba(74, 79, 89, 0.76));\n  font-size: 12px;\n}\n\n.oe-powerbox-commandRightCol {\n  margin: 0 10px;\n}\n\n/* Command hints */\n.oe-hint {\n  position: relative;\n}\n\n.oe-hint:before {\n  content: attr(placeholder);\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: block;\n  color: inherit;\n  opacity: 0.4;\n  pointer-events: none;\n  text-align: inherit;\n  width: 100%;\n}\n\n/* Collaboration cursor */\n.oe-collaboration-selections-container {\n  position: absolute;\n  isolation: isolate;\n  height: 0;\n  width: 0;\n  z-index: 1;\n}\n\n.oe-collaboration-caret-top-square {\n  min-height: 5px;\n  min-width: 5px;\n  color: #fff;\n  text-shadow: 0 0 5px #000;\n  position: absolute;\n  bottom: 100%;\n  left: -4px;\n  white-space: nowrap;\n}\n\n.oe-collaboration-caret-top-square:hover {\n  border-radius: 2px;\n  padding: 0.3em 0.6em;\n}\n\n.oe-collaboration-caret-top-square:hover::before {\n  content: attr(data-client-name);\n}\n\n.oe-collaboration-caret-avatar {\n  position: absolute;\n  height: 1.5rem;\n  width: 1.5rem;\n  border-radius: 50%;\n  transition: top 0.5s, left 0.5s;\n}\n\n.oe-collaboration-caret-avatar > img {\n  height: 100%;\n  width: 100%;\n  border-radius: 50%;\n}\n\n.oe-collaboration-caret-avatar[data-overlapping-avatars]::after {\n  content: attr(data-overlapping-avatars);\n  background-color: green;\n  color: white;\n  border-radius: 50%;\n  font-size: 9px;\n  padding: 0 4px;\n  position: absolute;\n  top: 11px;\n  right: -5px;\n  z-index: 1;\n}\n\ncode.o_inline_code {\n  background-color: #c5c5c5;\n  padding: 2px;\n  margin: 2px;\n  color: black;\n  font-size: inherit;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.scss */\n\n:root {\n  --o-we-toolbar-height: 40px;\n}\n\n.o_we_command_protector {\n  font-weight: 400 !important;\n}\n\n.o_we_command_protector b, .o_we_command_protector strong {\n  font-weight: 700 !important;\n}\n\n.o_we_command_protector * {\n  font-weight: inherit !important;\n}\n\n.o_we_command_protector .btn, .o_we_command_protector .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .o_we_command_protector button {\n  text-align: unset !important;\n}\n\n.wysiwyg_iframe,\n.note-editor {\n  border: 1px solid #D9D9D9;\n  margin: 0;\n  padding: 0;\n}\n\n.colorpicker {\n  --bg: #FFF;\n  --text-rgb: 43, 43, 51;\n  --border-rgb: var(--text-rgb);\n  --tab-border-top: transparent;\n  --tab-border-bottom: #D9D9D9;\n  --btn-color-active: inset 0 0 0 2px #01bad2,\n                        inset 0 0 0 3px var(--bg),\n                        inset 0 0 0 4px rgba(var(--border-rgb), .5);\n}\n\n.colorpicker, .colorpicker input {\n  color: rgba(var(--text-rgb), 1);\n}\n\n.colorpicker label {\n  color: rgba(var(--text-rgb), 0.5);\n}\n\n.colorpicker button {\n  outline: none;\n}\n\n.colorpicker .o_we_colorpicker_switch_panel {\n  font-size: 13px;\n  border-bottom: 1px solid var(--tab-border-bottom);\n  box-shadow: inset 0 1px 0 var(--tab-border-top);\n}\n\n.colorpicker .o_we_colorpicker_switch_pane_btn, .colorpicker .o_colorpicker_reset {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n}\n\n.colorpicker .o_colorpicker_reset {\n  margin-left: auto !important;\n}\n\n.colorpicker .o_colorpicker_sections {\n  background: var(--bg);\n}\n\n.colorpicker .o_colorpicker_sections > * {\n  padding-top: 8px;\n}\n\n.colorpicker .o_colorpicker_sections > *:first-child {\n  padding-top: 0;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_hex_div:focus-within, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_rgba_div:focus-within {\n  border-color: #01bad2;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input:focus {\n  border: none;\n  outline: none;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_color_pick_area, .colorpicker .o_colorpicker_sections .o_color_slider, .colorpicker .o_colorpicker_sections .o_opacity_slider:before, .colorpicker .o_colorpicker_sections .o_hex_div, .colorpicker .o_colorpicker_sections .o_rgba_div {\n  box-shadow: inset 0 0 0 1px rgba(var(--border-rgb), 0.5);\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn {\n  position: relative;\n  float: left;\n  width: 12.5%;\n  padding-top: 10%;\n  margin: 0;\n  border: 1px solid var(--bg);\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset {\n  background-color: transparent;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset::before {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  font-family: FontAwesome !important;\n  content: \"\\f00d\" !important;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #e6586c;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.selected {\n  box-shadow: var(--btn-color-active);\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_btn_transparent::before {\n  background-color: transparent;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_section::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::after {\n  box-shadow: inherit;\n}\n\n.oe-toolbar {\n  display: grid;\n}\n\n.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.oe-toolbar .colorpicker-menu {\n  height: auto !important;\n  box-sizing: content-box;\n  min-height: fit-content;\n}\n\n.oe-toolbar .dropdown-item.active:not(.dropdown-item_active_noarrow):before, .oe-toolbar .dropdown-item.selected:not(.dropdown-item_active_noarrow):before {\n  transform: translate(-1.5em, 0);\n  height: 100%;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n.oe-toolbar.oe-floating {\n  gap: 0 0.35em;\n  grid-auto-flow: column;\n  align-items: stretch;\n  height: auto;\n  min-height: 40px;\n  max-width: none;\n  border-radius: 2px;\n  padding: 0 0.5em;\n  background-color: var(--o-we-toolbar-bg, #FFF);\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1), 0 4px 18px rgba(0, 0, 0, 0.25);\n  color: var(--o-we-toolbar-color-text, #2b2b33);\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n.oe-toolbar.oe-floating.toolbar-bottom:before {\n  border-bottom-color: var(--o-we-toolbar-bg, #FFF);\n}\n\n.oe-toolbar.oe-floating:not(.toolbar-bottom):before {\n  border-top-color: var(--o-we-toolbar-bg, #FFF);\n}\n\n.oe-toolbar.oe-floating.noarrow::before {\n  display: none;\n}\n\n.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none):before, .oe-toolbar.oe-floating .oe-toolbar-separator:before {\n  content: \"\";\n  width: 1px;\n  margin-right: calc(0.35em - 1px);\n  background: var(--o-we-toolbar-border, #D9D9D9);\n  transform: scaleY(0.6);\n}\n\n.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none) .o-dropdown-menu:not([x-placement]), .oe-toolbar.oe-floating .oe-toolbar-separator .o-dropdown-menu:not([x-placement]) {\n  margin-left: 0.35em;\n}\n\n.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .dropdown-item {\n  padding: 3.5px 7px;\n  color: var(--o-we-toolbar-color-clickable, #595964);\n}\n\n.oe-toolbar.oe-floating .btn:hover:not(.active), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:hover:not(.active), .oe-toolbar.oe-floating .dropdown-item:hover:not(.active) {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n  background-color: transparent;\n}\n\n.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active, .oe-toolbar.oe-floating .dropdown-item.active {\n  background: var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2));\n  box-shadow: inset 0 0 3px RGBA(var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)), 0.5);\n}\n\n.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  border: none;\n  border-radius: 0;\n  background: transparent;\n  font-weight: 400;\n}\n\n.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active {\n  color: var(--o-we-toolbar-color-accent, #018597);\n}\n\n.oe-toolbar.oe-floating > .btn-group > .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .oe-toolbar.oe-floating > .btn-group > button, .oe-toolbar.oe-floating > .btn-group > .colorpicker-group {\n  margin: 4px auto;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.oe-toolbar.oe-floating .show > .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > button, .oe-toolbar.oe-floating .show > .btn:hover, .oe-toolbar.oe-floating .show > .btn:focus {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .dropdown-toggle::after {\n  content: \"\";\n  display: inline-block;\n  width: 0;\n  height: 0;\n  vertical-align: middle;\n  -moz-transform: scale(0.9999);\n  border-bottom: 0;\n  border-left: 0.3em solid transparent;\n  border-right: 0.3em solid transparent;\n  border-top: 0.3em solid var(--o-caret-color, currentColor);\n  margin-left: .3em;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu {\n  margin: 0;\n  border: 0;\n  padding: 0;\n  max-height: none;\n  overflow: visible;\n  border-top: 1px solid var(--o-we-toolbar-border, #D9D9D9);\n  background-color: var(--o-we-toolbar-bg, #FFF);\n  box-shadow: 0 18px 18px rgba(0, 0, 0, 0.23);\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 2px;\n  border-bottom-left-radius: 2px;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu.show {\n  min-width: 0;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu:not(.colorpicker-menu) > li:last-child {\n  margin-bottom: 1em;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu {\n  margin-top: 0;\n  min-width: 222px !important;\n}\n\n.oe-toolbar.oe-floating .dropdown-item {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  -webkit-box-pack: start; justify-content: flex-start;\n  padding: 0 27.2px;\n  min-height: 34px;\n}\n\n.oe-toolbar.oe-floating .dropdown-item > * {\n  color: inherit;\n}\n\n.oe-toolbar.oe-floating .dropdown-item.active > *, .oe-toolbar.oe-floating .dropdown-item.active:hover, .oe-toolbar.oe-floating .dropdown-item.active:focus {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .dropdown-item.active > *:before, .oe-toolbar.oe-floating .dropdown-item.active:hover:before, .oe-toolbar.oe-floating .dropdown-item.active:focus:before {\n  top: 0;\n  transform: translate(-17px, 0);\n  line-height: 34px;\n}\n\n.oe-toolbar.oe-floating #decoration #removeFormat {\n  display: none;\n}\n\n.oe-toolbar.oe-floating #decoration .active ~ #removeFormat {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n.oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .btn, .oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #colorInputButtonGroup label:last-of-type button {\n  margin: 0 1px 0 -1px;\n}\n\n.oe-toolbar.oe-floating #colorInputButtonGroup .note-back-color-preview.dropup .dropdown-menu {\n  left: -52px;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .dropdown-toggle::after {\n  display: none;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .colorpicker-menu {\n  bottom: 100%;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  margin-bottom: -1px;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active {\n  border-bottom: 1px solid var(--o-we-toolbar-color-accent, #018597);\n  background: none;\n  box-shadow: none;\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset {\n  background: #017e84;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover {\n  color: #FFFFFF;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover {\n  background: #01666b;\n}\n\n.oe-toolbar.oe-floating .colorpicker {\n  background: var(--o-we-toolbar-bg, #FFF);\n  box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.2);\n}\n\n.oe-toolbar.oe-floating .o_image_alt {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  max-width: 150px;\n}\n\n.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell {\n  border-radius: 0;\n}\n\n.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell.active {\n  background: var(--o-we-toolbar-color-accent, #018597);\n}\n\nbody:not(.editor_has_snippets) .oe-toolbar {\n  z-index: 1056;\n}\n\n@media only screen and (max-width: 767px) {\n  .oe-toolbar {\n    background-color: white;\n  }\n  .oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n    color: black;\n  }\n  .oe-toolbar::before {\n    display: none;\n  }\n  .oe-toolbar::after {\n    display: none;\n  }\n}\n\n.oe_edited_link {\n  position: relative;\n  display: inline-block;\n}\n\n.oe_edited_link::before {\n  content: '';\n  border: dashed 3px #01bad2;\n  position: absolute;\n  inset: -5px;\n  pointer-events: none;\n}\n\n.oe_edited_link:empty::after {\n  content: \"\\00a0\\00a0\";\n}\n\n@keyframes fadeInDownSmall {\n  0% {\n    opacity: 0;\n    transform: translate(0, -5px);\n  }\n  100% {\n    opacity: 1;\n    transform: translate(0, 0);\n  }\n}\n\n@keyframes inputHighlighter {\n  from {\n    background: #017e84;\n  }\n  to {\n    width: 0;\n    background: transparent;\n  }\n}\n\n.o_we_horizontal_collapse {\n  width: 0 !important;\n  padding: 0 !important;\n  border: none !important;\n}\n\n.o_we_transition_ease {\n  transition: all ease 0.35s;\n}\n\nbody .modal .o_link_dialog input.link-style:checked + span::after {\n  content: \"\\f00c\";\n  display: inline-block;\n  font-family: FontAwesome;\n  margin-left: 2px;\n}\n\nbody .modal .o_link_dialog .o_link_dialog_preview {\n  border-left: var(--o-link-dialog-preview-border, 1px solid #DEE2E6);\n}\n\n.o_we_progressbar:last-child hr {\n  display: none;\n}\n\n.fa.o_we_selected_image::before, img.o_we_selected_image {\n  outline: 3px solid rgba(150, 150, 220, 0.3);\n}\n\n.o_we_media_author {\n  font-size: 11px;\n  position: absolute;\n  top: auto;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: center;\n  background-color: rgba(255, 255, 255, 0.7);\n}\n\n@media (max-width: 991.98px) {\n  #web_editor-top-edit {\n    position: initial !important;\n    height: initial !important;\n    top: initial !important;\n    left: initial !important;\n  }\n  .oe-toolbar.oe-floating {\n    box-shadow: 0 10px 10px -5px rgba(0, 0, 0, 0.1);\n    display: -webkit-box; display: -webkit-flex; display: flex;\n    -webkit-flex-wrap: wrap; flex-wrap: wrap;\n    margin-bottom: 1rem;\n    overflow-y: visible;\n  }\n  .oe-toolbar.oe-floating .dropdown-menu {\n    max-height: 200px;\n    overflow: auto;\n  }\n  .oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu {\n    bottom: auto;\n  }\n}\n\n.note-editable .modal:not(.o_technical_modal) {\n  top: 40px;\n  right: 0;\n  bottom: 0;\n  right: 288px;\n  width: auto;\n  height: auto;\n}\n\n.note-editable .modal:not(.o_technical_modal) .modal-dialog {\n  padding: 0.5rem 0;\n}\n\n.o_wysiwyg_wrapper {\n  position: relative;\n  margin-bottom: 11px;\n}\n\n.o_wysiwyg_resizer {\n  background: #f5f5f5;\n  height: 10px;\n  width: 100%;\n  border-left: 1px solid #D9D9D9;\n  border-bottom: 1px solid #D9D9D9;\n  border-right: 1px solid #D9D9D9;\n  cursor: row-resize;\n  padding-top: 1px;\n}\n\n.o_wysiwyg_resizer_hook {\n  width: 20px;\n  margin: 1px auto;\n  border-top: 1px solid #a9a9a9;\n}\n\n.note-editable {\n  border: 1px solid #D9D9D9;\n  overflow: auto;\n  height: 100%;\n  padding: 4px 40px 4px 4px;\n  min-height: 10px;\n  border-radius: 3px;\n}\n\n.oe-bordered-editor > .note-editable {\n  border-width: 1px;\n  padding: 4px 40px 4px 4px;\n  min-height: 180px;\n}\n\n.o_we_no_pointer_events {\n  pointer-events: none;\n}\n\n.o_we_crop_widget {\n  background-color: rgba(128, 128, 128, 0.5);\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 1024;\n}\n\n.o_we_crop_widget .o_we_cropper_wrapper {\n  position: absolute;\n}\n\n.o_we_crop_widget .o_we_crop_buttons {\n  margin-top: 0.5rem;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-wrap: wrap; flex-wrap: wrap;\n  bottom: 1rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons input[type=radio] {\n  display: none;\n}\n\n.o_we_crop_widget .o_we_crop_buttons .btn-group {\n  border-radius: 0.25rem;\n  margin: 0.1rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button, .o_we_crop_widget .o_we_crop_buttons label {\n  cursor: pointer !important;\n  padding: 0.2rem 0.3rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons label {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n.o_we_crop_widget .o_we_crop_buttons label.active {\n  background-color: #000000;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn), .o_we_crop_widget .o_we_crop_buttons label {\n  margin: 0;\n  border: none;\n  border-right: 1px solid #2b2b33;\n  background-color: #2b2b33;\n  color: #D9D9D9;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn):first-child, .o_we_crop_widget .o_we_crop_buttons label:first-child {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn):last-child, .o_we_crop_widget .o_we_crop_buttons label:last-child {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n  border-right: none;\n}\n\n[data-oe-xpath], [data-oe-xpath] [contenteditable=true] {\n  outline: none;\n}\n\n.o_transform_removal {\n  transform: none !important;\n}\n\n.o_edit_menu_popover {\n  max-width: 331.2px;\n  width: 331.2px;\n  user-select: none;\n  font-size: 12px;\n  font-weight: 400 !important;\n}\n\n.o_edit_menu_popover .fw-bold {\n  font-weight: 500 !important;\n}\n\n.o_edit_menu_popover .o_we_preview_favicon > img {\n  max-height: 16px;\n  max-width: 16px;\n}\n\n.o_edit_menu_popover .o_we_url_link {\n  width: 100px;\n}\n\n.o_edit_menu_popover .o_we_full_url {\n  word-break: break-all;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n\n.o_edit_menu_popover .o_we_full_url.o_we_webkit_box {\n  display: -webkit-box;\n}\n\n.o_edit_menu_popover .o_we_full_url:hover {\n  -webkit-line-clamp: unset;\n}\n\ntextarea.o_codeview {\n  min-height: 400px;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg_iframe.scss */\n\niframe.wysiwyg_iframe.o_fullscreen {\n  left: 0 !important;\n  right: 0 !important;\n  top: 0 !important;\n  bottom: 0 !important;\n  width: 100% !important;\n  min-height: 100% !important;\n  z-index: 1001 !important;\n  border: 0;\n}\n\n.o_wysiwyg_no_transform {\n  transform: none !important;\n}\n\nbody.o_in_iframe {\n  background-color: white;\n}\n\nbody.o_in_iframe .o_editable {\n  position: relative;\n}\n\nbody.o_in_iframe .note-editable {\n  border: none;\n  padding: 0;\n  border-radius: 0;\n}\n\nbody.o_in_iframe #oe_snippets {\n  top: 0;\n}\n\nbody.o_in_iframe .iframe-editor-wrapper {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  overflow: auto;\n}\n\nbody.o_in_iframe.oe_dropzone_active .note-editable {\n  overflow: hidden;\n}\n\nbody.o_in_iframe .iframe-utils-zone {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\nbody.o_in_iframe .note-statusbar {\n  display: none;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  margin: auto 9px auto auto;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions .btn, body.o_in_iframe #oe_snippets .email_designer_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel body.o_in_iframe #oe_snippets .email_designer_top_actions button {\n  align-items: center;\n  width: 24px;\n  height: 24px;\n  background-color: #337ab7;\n  border: 1px solid #2e6da4;\n  border-radius: 4px;\n  padding: 0;\n  margin-left: 5px;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions .o_fullscreen_btn img {\n  margin: auto;\n}\n\nbody.o_in_iframe textarea.o_codeview {\n  position: absolute;\n  font-family: 'Courier New', Courier, monospace;\n  outline: none;\n  resize: none;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 288px;\n  width: calc(100% - 288px);\n  height: 100%;\n  border: none;\n}\n\nbody.o_in_iframe .o_height_400, body.o_in_iframe .o_height_400 div.container, body.o_in_iframe .o_height_400 div.row {\n  min-height: 400px;\n}\n\nbody.o_in_iframe .o_height_800, body.o_in_iframe .o_height_800 div.container, body.o_in_iframe .o_height_800 div.row {\n  min-height: 800px;\n}\n\nbody.o_in_iframe .btn, body.o_in_iframe .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel body.o_in_iframe button {\n  user-select: auto;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg_snippets.scss */\n\n@media (max-width: 991.98px) {\n  body.editor_enable.editor_has_snippets #web_editor-top-edit {\n    position: initial !important;\n    height: initial !important;\n    top: initial !important;\n    left: initial !important;\n  }\n  body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover {\n    right: 0 !important;\n  }\n}\n\n.oe_snippet {\n  position: relative;\n  z-index: 1041;\n  width: 77px;\n  background-color: #3e3e46;\n}\n\n.oe_snippet.ui-draggable-dragging {\n  transform: rotate(-3deg) scale(1.2);\n  box-shadow: 0 5px 25px -10px black;\n  transition: transform 0.3s, box-shadow 0.3s;\n}\n\n.oe_snippet > .oe_snippet_body {\n  display: none !important;\n}\n\n.oe_snippet .oe_snippet_thumbnail {\n  width: 100%;\n}\n\n.oe_snippet .oe_snippet_thumbnail .oe_snippet_thumbnail_img {\n  width: 100%;\n  padding-top: 75%;\n  background-repeat: no-repeat;\n  background-size: contain;\n  background-position: top center;\n  overflow: hidden;\n}\n\n.oe_snippet .oe_snippet_thumbnail_title {\n  display: none;\n}\n\n.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) {\n  background-color: rgba(62, 62, 70, 0.9);\n}\n\n.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) .oe_snippet_thumbnail {\n  filter: saturate(0.7);\n  opacity: .9;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button {\n  outline: none;\n  text-decoration: none;\n  line-height: 20px;\n  cursor: pointer;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button[disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button[disabled], #oe_snippets .colorpicker [disabled].o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options [disabled].btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button[disabled], #oe_snippets > .o_we_customize_panel .oe-toolbar [disabled].btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], #oe_snippets > .o_we_customize_panel we-button[disabled], #oe_snippets > .o_we_customize_panel we-toggler[disabled], #oe_snippets > .o_we_customize_panel [disabled].o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button[disabled].o_we_link, #oe_snippets > .o_we_customize_panel [disabled]#removeFormat, #oe_snippets > .o_we_customize_panel [disabled]#oe-table-delete-table, #oe_snippets > .o_we_customize_panel [disabled].o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet [disabled].btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button[disabled], #oe_snippets .colorpicker [disabled].o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button[disabled] {\n  opacity: .5;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets #snippets_menu > button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover, #oe_snippets #snippets_menu > button:not([disabled]):hover {\n  color: #FFFFFF;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_success {\n  color: #40ad67;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_success:hover {\n  color: #40ad67;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success {\n  color: white;\n  background-color: #40ad67;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success:hover {\n  background-color: #369156;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_info {\n  color: #6999a8;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_info:hover {\n  color: #6999a8;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info {\n  color: white;\n  background-color: #6999a8;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info:hover {\n  background-color: #568695;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_warning {\n  color: #f0ad4e;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_warning:hover {\n  color: #f0ad4e;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning {\n  color: white;\n  background-color: #f0ad4e;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning:hover {\n  background-color: #ed9d2b;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_danger {\n  color: #e6586c;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_danger:hover {\n  color: #e6586c;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger {\n  color: white;\n  background-color: #e6586c;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger:hover {\n  background-color: #e1374f;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_brand_primary {\n  color: #017e84;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_brand_primary:hover {\n  color: #017e84;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary {\n  color: white;\n  background-color: #017e84;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary:hover {\n  background-color: #015a5e;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_graphic, #oe_snippets #snippets_menu > button svg .o_graphic {\n  fill: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_subdle, #oe_snippets #snippets_menu > button svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_graphic {\n  fill: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.75);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  padding: 0 6px;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  background-color: #595964;\n  color: #D9D9D9;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler svg .o_graphic {\n  fill: #D9D9D9;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler svg .o_subdle {\n  fill: rgba(217, 217, 217, 0.5);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_graphic {\n  fill: #FFFFFF;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.75);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle), #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) {\n  background-color: #2b2b33;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button {\n  display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  justify-content: center;\n  min-width: 0;\n  border: none;\n  background-color: transparent;\n  color: inherit;\n  font-weight: normal;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn > span, #oe_snippets #snippets_menu > button > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  padding: 0.6em 0.4em 0.5em;\n}\n\n#oe_snippets .colorpicker .active.o_we_colorpicker_switch_pane_btn > span, #oe_snippets #snippets_menu > button.active > span {\n  color: #FFFFFF;\n  box-shadow: inset 0 -2px 0 #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div {\n  border: 1px solid #000000;\n  border-radius: 2px;\n  background-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div:focus-within, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div:focus-within {\n  border-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input {\n  box-sizing: content-box;\n  padding: 0 6px;\n  border: none;\n  border-radius: 0;\n  background-color: transparent;\n  color: inherit;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input:focus, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input:focus {\n  outline: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div > we-button, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div > we-button {\n  border: none;\n}\n\n#oe_snippets {\n  position: absolute;\n  top: var(--o-we-toolbar-height);\n  left: auto;\n  bottom: 0;\n  right: 0;\n  position: fixed;\n  z-index: 1041;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-flow: column nowrap; flex-flow: column nowrap;\n  width: 288px;\n  border-left: 1px solid #2b2b33;\n  background-color: #2b2b33;\n  color: #D9D9D9;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 12px;\n  font-weight: 400;\n  transition: transform 400ms ease 0s;\n  transform: translateX(100%);\n}\n\n#oe_snippets *::selection {\n  background: #03e1fe;\n  color: #000000;\n}\n\n#oe_snippets .o_we_website_top_actions {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-box-pack: start; justify-content: flex-start;\n  width: 288px;\n  height: 46px;\n  min-height: 46px;\n  background-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn-group, #oe_snippets .o_we_website_top_actions .btn, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button {\n  height: 100%;\n}\n\n#oe_snippets .o_we_website_top_actions .btn, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button {\n  border: none;\n  border-radius: 0;\n  padding: 0.375rem 0.75rem;\n  font-size: 13px;\n  font-weight: 400;\n  line-height: 1;\n}\n\n#oe_snippets .o_we_website_top_actions .btn:not(.fa), #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not(.fa), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:not(.fa) {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary {\n  color: #FFFFFF;\n  background-color: #017e84;\n  border-color: #017e84;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary:hover, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:hover {\n  color: #FFFFFF;\n  background-color: #016b70;\n  border-color: #01656a;\n}\n\n.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:focus + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:focus + #oe_snippets .o_we_website_top_actions button.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:focus {\n  color: #FFFFFF;\n  background-color: #016b70;\n  border-color: #01656a;\n  box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:active, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.dropdown-toggle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-primary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #01656a;\n  border-color: #015f63;\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-primary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle:focus, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.dropdown-toggle:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-primary.disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.disabled {\n  color: #FFFFFF;\n  background-color: #017e84;\n  border-color: #017e84;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary {\n  color: #FFFFFF;\n  background-color: #141217;\n  border-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary:hover, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:hover {\n  color: #FFFFFF;\n  background-color: #110f14;\n  border-color: #100e12;\n}\n\n.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:focus + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:focus + #oe_snippets .o_we_website_top_actions button.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:focus {\n  color: #FFFFFF;\n  background-color: #110f14;\n  border-color: #100e12;\n  box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:active, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.dropdown-toggle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-secondary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #100e12;\n  border-color: #0f0e11;\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle:focus, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.dropdown-toggle:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.disabled {\n  color: #FFFFFF;\n  background-color: #141217;\n  border-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:focus, #oe_snippets .o_we_website_top_actions .btn:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:active, #oe_snippets .o_we_website_top_actions .btn:focus:active {\n  outline: none;\n  box-shadow: none !important;\n}\n\n#oe_snippets .o_we_website_top_actions .dropdown-menu {\n  left: auto;\n  right: 0;\n}\n\n#oe_snippets #snippets_menu {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  background-color: #141217;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);\n  color: #D9D9D9;\n}\n\n#oe_snippets .o_snippet_search_filter {\n  position: relative;\n  box-shadow: inset 0 -1px 0 #000000, 0 10px 10px rgba(0, 0, 0, 0.2);\n  z-index: 2;\n}\n\n#oe_snippets .o_snippet_search_filter, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input {\n  width: 100%;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input {\n  background-color: #2b2b33;\n  padding: 10px 2em 10px 10px;\n  border: 0;\n  border-bottom: 1px solid #000000;\n  color: #FFFFFF;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input::placeholder {\n  font-style: italic;\n  color: #9d9d9d;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input:focus {\n  background-color: #3e3e46;\n  outline: none;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset {\n  position: absolute;\n  top: 10px;\n  left: auto;\n  bottom: 10px;\n  right: 10px;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 6px;\n  color: #9d9d9d;\n  cursor: pointer;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:hover, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:focus, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset.focus {\n  color: #FFFFFF;\n}\n\n#oe_snippets > #o_scroll, #oe_snippets > .o_we_customize_panel {\n  min-height: 0;\n  overflow: auto;\n}\n\n#oe_snippets > #o_scroll {\n  background-color: #191922;\n  padding: 0 10px;\n  height: 100%;\n  z-index: 1;\n}\n\n#oe_snippets > #o_scroll .o_panel, #oe_snippets > #o_scroll .o_panel_header {\n  padding: 10px 0;\n}\n\n#oe_snippets > #o_scroll .o_panel_body {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-wrap: wrap; flex-wrap: wrap;\n  margin-left: -2px;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  width: 33.33333333%;\n  background-clip: padding-box;\n  border-left: 2px solid transparent;\n  margin-bottom: 2px;\n  user-select: none;\n  cursor: url(/web/static/img/openhand.cur), grab;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet .oe_snippet_thumbnail_title {\n  display: block;\n  padding: 5px;\n  text-align: center;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .o_snippet_undroppable {\n  position: absolute;\n  top: 8px;\n  left: auto;\n  bottom: auto;\n  right: 6px;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .btn.o_install_btn, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_install_btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install button.o_install_btn {\n  position: absolute;\n  top: 10px;\n  left: auto;\n  bottom: auto;\n  right: auto;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .btn.o_install_btn, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_install_btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) button.o_install_btn {\n  display: none;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install {\n  background-color: rgba(62, 62, 70, 0.2);\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .oe_snippet_thumbnail_img, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe_snippet_thumbnail_img {\n  opacity: .4;\n  filter: saturate(0) blur(1px);\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet {\n  width: 100%;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button {\n  align-items: center;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail {\n  min-width: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_title {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_img {\n  flex-shrink: 0;\n  width: 41px;\n  height: 30px;\n  padding: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button {\n  padding-top: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) button {\n  display: none;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget {\n  cursor: pointer;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget input {\n  cursor: text;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button {\n  cursor: pointer;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  line-height: 17px;\n  text-align: center;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:hover {\n  background-color: gray;\n}\n\n#oe_snippets > .o_we_customize_panel {\n  position: relative;\n  flex: 1;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar {\n  position: relative;\n  background: transparent;\n  margin-top: 8px;\n  padding: 0 10px 0 15px;\n  grid-template-areas: \"typo typo style colors\" \"size align list link\" \"options options options options\" \"options2 options2 options2 options2\";\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  grid-template-rows: minmax(22px, auto) minmax(22px, auto) auto auto;\n  row-gap: 8px;\n  column-gap: 3px;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar::before {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  padding: 2.64px 4px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options {\n  grid-area: options;\n  padding-left: 0;\n  padding-right: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_short_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_short_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_short_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_short_title.oe-table-label {\n  width: unset !important;\n  padding-right: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_long_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_long_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_long_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_long_title.oe-table-label {\n  width: fit-content !important;\n  padding-right: 10px !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .highlighted-text, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .highlighted-text {\n  color: white;\n  font-weight: bold;\n  padding: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown {\n  position: unset;\n  width: -webkit-fill-available;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle {\n  padding: 0;\n  width: inherit;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle::after, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle::after {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show {\n  position: absolute !important;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) {\n  text-align: left;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 12px;\n  font-weight: 400;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) div {\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option input::placeholder, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options input::placeholder {\n  font-style: italic;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) input, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) input {\n  width: 100% !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option + we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options + we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option + #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options + #oe-table-options {\n  grid-area: options2;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup {\n  position: static;\n  grid-area: colors;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .dropdown-toggle:after {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .colorpicker-group {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: stretch;\n  position: static;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-text-color {\n  border-right: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-fore-color {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button + button {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn-group > .btn:not(:last-of-type), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-group > button:not(:last-of-type) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #list {\n  grid-area: list;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #link {\n  grid-area: link;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #link #unlink {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size {\n  grid-area: size;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #decoration {\n  grid-area: style;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style {\n  grid-area: typo;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle {\n  justify-content: space-between;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span {\n  color: white;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span pre, #oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span blockquote {\n  padding: 0;\n  border: 0;\n  color: inherit;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify {\n  grid-area: align;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu {\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button {\n  padding: 6.6px 11px;\n  border-width: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button:hover {\n  z-index: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button + button {\n  border-left-width: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu.colorpicker-menu {\n  min-width: 0;\n  max-height: none;\n  left: 15px;\n  right: 10px;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar :not(.dropup) > .dropdown-menu.colorpicker-menu {\n  top: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel .link-custom-color-border we-input, #oe_snippets > .o_we_customize_panel .link-custom-color-border we-select {\n  max-width: max-content;\n}\n\n#oe_snippets > .o_we_customize_panel .link-custom-color-border we-toggler {\n  width: 85px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_link {\n  margin-top: 0;\n  border: 0;\n  padding: 0;\n  background: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler {\n  padding-right: 2em;\n  text-align: left;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler::after {\n  content: \"\uf0d7\";\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler > img, #oe_snippets > .o_we_customize_panel we-toggler > svg {\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler + * {\n  display: none !important;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  background-color: #141217;\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active {\n  padding-right: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active::after {\n  content: \"\uf0d8\";\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active + * {\n  display: block !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel we-toggler.active {\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after, #oe_snippets > .o_we_customize_panel we-toggler::after, #oe_snippets > .o_we_customize_panel we-toggler.active::after {\n  position: absolute;\n  top: 50%;\n  left: auto;\n  bottom: auto;\n  right: 0.5em;\n  transform: translateY(-50%);\n  width: 1em;\n  text-align: center;\n  font-family: FontAwesome;\n}\n\n#oe_snippets > .o_we_customize_panel we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label {\n  display: block;\n  text-transform: capitalize;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options {\n  position: relative;\n  display: block;\n  padding: 0 0 15px 0;\n  background-color: #3e3e46;\n  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.8);\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  padding: 3px 10px 0 15px;\n  background-color: #2b2b33;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.5);\n  font-size: 13px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > span, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  cursor: pointer;\n  color: #FFFFFF !important;\n  line-height: 32px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  margin-left: auto;\n  font-size: .9em;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group .oe_snippet_remove, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group .oe_snippet_remove {\n  font-size: 1.2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group #oe-table-options, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group #oe-table-options {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button {\n  margin-top: 0 !important;\n  margin-left: 3px;\n  padding: 0 3px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.o_we_icon_button {\n  box-sizing: content-box;\n  width: 1.29em;\n  padding: 0 0.15em !important;\n  margin-left: 6px;\n  text-align: center;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options {\n  position: relative;\n  display: block;\n  padding: 0 10px 0 15px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option .dropdown-menu, #oe_snippets > .o_we_customize_panel #oe-table-options .dropdown-menu {\n  position: static !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert {\n  background-color: #6999a8;\n  display: block;\n  padding: 6px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert we-title, #oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > we-alert .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert .oe-table-label {\n  margin-bottom: 6px;\n  text-transform: uppercase;\n  font-weight: bold;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label {\n  margin-bottom: -4px;\n  font-size: 13px;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label:not(:first-child) {\n  margin-top: 16px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon {\n  position: absolute;\n  top: 0;\n  left: -15px;\n  bottom: 0;\n  right: 100%;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 15px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget {\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  min-height: 22px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_large > div {\n  flex: 1 1 auto !important;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  min-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > .fa {\n  line-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > img {\n  margin-bottom: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > svg {\n  margin-bottom: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.fa > div {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel we-button.fa {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.fa-fw {\n  padding: 0 .5em;\n  width: 2.29em;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget {\n  min-width: 20px;\n  padding: 0;\n  border: none;\n  background: none;\n  cursor: default;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-button.o_we_checkbox_wrapper.o_we_user_value_widget > .oe-table-label {\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  min-height: 22px;\n  line-height: 22px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  width: 20px;\n  height: 12px;\n  background-color: #9d9d9d;\n  border-radius: 10rem;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox::after {\n  content: \"\";\n  display: block;\n  width: 11px;\n  height: 10px;\n  border-radius: 10rem;\n  background-color: #FFFFFF;\n  box-shadow: 0 2px 3px 0 #000000;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active we-checkbox {\n  background-color: #01bad2;\n  -webkit-box-pack: end; justify-content: flex-end;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active, #oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget:hover {\n  color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel we-selection-items .o_we_user_value_widget {\n  margin-top: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget {\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_icon_select) we-toggler {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret {\n  position: relative;\n  display: block;\n  align-self: flex-end;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after {\n  content: '';\n  position: absolute;\n  top: 100%;\n  left: auto;\n  bottom: auto;\n  right: 2em;\n  z-index: 1001;\n  transform: translateX(50%);\n  margin-top: 2px;\n  border-bottom: 7px solid #000000;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after {\n  border-bottom-color: #595964;\n  border-left-width: 7px;\n  border-right-width: 7px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_so_color_palette) + we-button:not(:hover) {\n  background: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-toggler:empty::before {\n  content: '/';\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  bottom: auto;\n  right: 0;\n  z-index: 1000;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.dropdown-menu) {\n  margin-top: 8px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.o_we_has_pager) {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty {\n  line-height: 34px;\n  background-color: #595964;\n  color: #C6C6C6;\n  padding-left: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty::before {\n  content: '/';\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > .oe-table-label {\n  line-height: 34px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button {\n  padding-left: 2em;\n  border: none;\n  background: none;\n  background-clip: padding-box;\n  background-color: #595964;\n  color: #C6C6C6;\n  border-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after {\n  content: \"\";\n  color: #9d9d9d;\n  right: auto;\n  left: 0.5em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label {\n  flex-grow: 1;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label {\n  line-height: 34px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label svg, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label svg {\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:not(.d-none) ~ we-button {\n  border-top: 1px solid transparent;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:hover {\n  background-color: #2b2b33;\n  color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active {\n  padding-left: 2em;\n  background-color: #42424c;\n  color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after {\n  content: \"\uf00c\";\n  color: #9d9d9d;\n  right: auto;\n  left: 0.5em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active:after {\n  color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  justify-content: space-between;\n  background-color: #595964;\n  margin-bottom: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header > b {\n  padding: 6px;\n  color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls > span {\n  margin: 0 6px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_next, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_prev {\n  margin: 0.3em;\n  padding: 6px;\n  cursor: pointer;\n  border: 1px solid currentColor;\n  border-radius: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page {\n  display: none;\n  width: 100%;\n  max-height: 562.5px;\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page.active {\n  display: block;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page we-button img {\n  height: 60px;\n  width: 80px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-wrap: wrap; flex-wrap: wrap;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button {\n  width: 33.33333333%;\n  padding: 0;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button img {\n  padding: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button.active {\n  border: 2px solid #40ad67 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid.o_we_fake_transparent_background we-button {\n  background-image: url(/web/static/img/transparent.png);\n  background-size: 10px auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_image_shape_remove div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button {\n  padding: 0 6px;\n  border-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button + we-button {\n  border-left: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:first-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:first-child {\n  border-top-left-radius: 2px;\n  border-bottom-left-radius: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:last-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:last-child {\n  border-top-right-radius: 2px;\n  border-bottom-right-radius: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items we-button, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items we-button {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  flex: 0 1 25%;\n  padding: 1.5px 2px;\n  text-align: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div {\n  -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;\n  width: 60px;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 0;\n  min-width: 2ch;\n  height: 20px;\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input::placeholder {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget span {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  padding-right: 6px;\n  font-size: 11px;\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: block;\n  width: 20px;\n  height: 20px;\n  border: 1px solid #000000;\n  border-radius: 10rem;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after {\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_color_preview {\n  border: 2px solid #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after {\n  right: 10px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after {\n  border-bottom-width: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget we-toggler {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix {\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table {\n  table-layout: fixed;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td, #oe_snippets > .o_we_customize_panel we-matrix table th {\n  text-align: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td we-button, #oe_snippets > .o_we_customize_panel we-matrix table th we-button {\n  display: inline-block;\n  color: inherit;\n  height: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_row, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_row {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td input, #oe_snippets > .o_we_customize_panel we-matrix table th input {\n  border: 1px solid #000000;\n  background-color: #2b2b33;\n  color: inherit;\n  font-size: 12px;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td:last-child, #oe_snippets > .o_we_customize_panel we-matrix table th:last-child {\n  width: 28px;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table tr:last-child we-button {\n  overflow: visible;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget[data-display-range-value] input[type=\"range\"] {\n  min-width: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"] {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n  height: 22px;\n  padding: 0 1px 0 0;\n  background-color: transparent;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus {\n  outline: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-webkit-slider-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-moz-range-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-ms-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-focus-outer {\n  border: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-thumb {\n  width: 10px;\n  height: 10px;\n  margin-top: -3px;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-runnable-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: #9d9d9d;\n  border-color: transparent;\n  border-radius: 10rem;\n  box-shadow: none;\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-thumb {\n  width: 10px;\n  height: 10px;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: #9d9d9d;\n  border-color: transparent;\n  border-radius: 10rem;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-progress {\n  background-color: #01bad2;\n  height: 4px;\n  border-color: transparent;\n  border-radius: 10rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-thumb {\n  width: 10px;\n  height: 10px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-left: 0;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: transparent;\n  border-color: transparent;\n  border-width: 5px;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-fill-lower {\n  background-color: #01bad2;\n  border-radius: 10rem;\n  border-radius: 1rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-fill-upper {\n  background-color: #9d9d9d;\n  border-radius: 10rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range {\n  transform: rotate(180deg);\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-moz-range-track {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-moz-range-progress {\n  background-color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-ms-fill-lower {\n  background-color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-ms-fill-upper {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-list > div {\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper {\n  width: 100%;\n  max-height: 200px;\n  overflow-y: auto;\n  margin-bottom: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table {\n  table-layout: auto;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input::-webkit-outer-spin-button,\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input::-webkit-inner-spin-button {\n  -webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;\n  margin: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input[type=number] {\n  -moz--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input {\n  width: 100%;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  padding: 0 6px;\n  background-color: #2b2b33;\n  color: inherit;\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table tr {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  border: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td {\n  flex-grow: 1;\n  padding-bottom: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td:not(.o_we_list_record_name) {\n  flex-grow: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td we-button.o_we_checkbox_wrapper {\n  margin: 0 0 0 0.3em;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_user_value_widget {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget {\n  margin-top: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div {\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div > * {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search {\n  background-color: #595964;\n  flex-grow: 1 !important;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  margin-bottom: 1px;\n  border-radius: 2px;\n  padding: .25em .5em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search::before {\n  content: \"\\f002\";\n  font-size: 1.2em;\n  padding-right: .5em;\n  font-family: FontAwesome;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  color: inherit;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  background-color: #2b2b33;\n  padding: 1px 6px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input:focus {\n  outline: none;\n  border-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input::placeholder {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search_more {\n  color: var(--o-cc1-btn-primary);\n  margin-top: 1px;\n  width: 100%;\n  cursor: pointer;\n  padding-left: 2em;\n  line-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_create {\n  margin-top: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2m we-list, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list > div, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list we-select {\n  margin-top: 0;\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row {\n  position: relative;\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_user_value_widget {\n  margin-top: 0;\n  min-width: 4em;\n}\n\n#oe_snippets > .o_we_customize_panel we-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row we-button-group.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button-group.o_we_user_value_widget {\n  min-width: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-row > div > :not(.d-none) ~ *, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div > :not(.d-none) ~ * {\n  margin-left: 3px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row we-select.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-select.o_we_user_value_widget {\n  position: static;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_we_full_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_full_row.oe-table-row > div {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title .oe-table-label {\n  width: unset !important;\n  padding-right: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title .oe-table-label {\n  width: fit-content !important;\n  padding-right: 10px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row {\n  margin-top: 15px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_design_tab_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_design_tab_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row .oe-table-label {\n  font-weight: 600;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel we-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > .oe-table-label {\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div {\n  -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;\n  min-width: 0;\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) {\n  -webkit-flex-flow: row nowrap; flex-flow: row nowrap;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > .oe-table-label {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  width: 105.2px;\n  padding-right: 6px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > div {\n  margin-top: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse {\n  position: relative;\n  display: block;\n  padding-left: 15px;\n  padding-right: 10px;\n  margin-right: -10px;\n  margin-left: -15px;\n  border-top: 4px solid transparent;\n  padding-bottom: 4px;\n  margin-bottom: -4px;\n  background-clip: padding-box;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse > :first-child, #oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler {\n  margin-top: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  width: 15px;\n  height: 22px;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  background: none;\n  border: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after {\n  content: '\\f0da';\n  position: static;\n  transform: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active::after {\n  content: '\\f0d7';\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active + * {\n  background: none;\n  border: none;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse.active {\n  box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.5), inset 0 -1px 0 rgba(255, 255, 255, 0.2);\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-toggler.o_we_collapse_toggler {\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active .o_we_collapse_toggler {\n  background-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler {\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_sublevel_3 > we-title::before, #oe_snippets > .o_we_customize_panel .o_we_sublevel_2 > we-title::before, #oe_snippets > .o_we_customize_panel .o_we_sublevel_1 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before {\n  content: \"\u2514\";\n  display: inline-block;\n  margin-right: 0.4em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_sublevel_2 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label {\n  padding-left: 0.6em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_sublevel_3 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label {\n  padding-left: 1.2em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_image_weight {\n  margin-left: 12px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_tag {\n  background-color: #000000;\n  white-space: nowrap;\n  padding: 1.5px 3px;\n  border-radius: 3px;\n  font-size: 0.85em;\n}\n\n#oe_snippets > .o_we_invisible_el_panel {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  max-height: 220px;\n  overflow-y: auto;\n  margin-top: auto;\n  padding: 10px;\n  background-color: #191922;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_panel_header {\n  padding: 8px 0;\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry {\n  padding: 8px 6px;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry:hover {\n  background-color: #2b2b33;\n}\n\n#oe_snippets.o_we_backdrop > .o_we_customize_panel {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets.o_we_backdrop > .o_we_customize_panel::after {\n  content: \"\";\n  position: -webkit-sticky;\n  position: sticky;\n  top: auto;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  display: block;\n  height: 100vh;\n  margin-top: -100vh;\n  pointer-events: none;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n#oe_snippets.o_we_backdrop .o_we_widget_opened {\n  z-index: 1000;\n}\n\n.o_we_cc_preview_wrapper {\n  font-family: sans-serif !important;\n  font-size: 15px !important;\n  padding: 8px 8px 6.4px;\n}\n\n.o_we_cc_preview_wrapper > * {\n  margin-bottom: 0 !important;\n  line-height: 1 !important;\n}\n\n.o_we_color_combination_btn_text {\n  color: inherit !important;\n  font-family: inherit !important;\n  font-size: 0.8em !important;\n  margin-top: 0.5em !important;\n}\n\n.o_we_color_combination_btn_title {\n  margin-top: 0 !important;\n  font-size: 1.3em !important;\n}\n\n.o_we_color_combination_btn_btn {\n  padding: 0.2em 3px 0.3em !important;\n  border-radius: 2px !important;\n  font-size: 0.8em !important;\n}\n\n.o_we_border_preview {\n  display: inline-block;\n  width: 999px;\n  max-width: 100%;\n  margin-bottom: 2px;\n  border-width: 4px;\n  border-bottom: none !important;\n}\n\n#oe_snippets .colorpicker {\n  --bg: #3e3e46;\n  --text-rgb: 217, 217, 217;\n  --border-rgb: var(--text-rgb);\n  --tab-border-top: rgba(255, 255, 255, .2);\n  --tab-border-bottom: #191922;\n  --btn-color-active: inset 0 0 0 1px #3e3e46,\n                        inset 0 0 0 3px #01bad2,\n                        inset 0 0 0 4px white;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn {\n  -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_reset {\n  border: 0;\n  background-color: transparent;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn {\n  float: none;\n  width: 100%;\n  padding: 0;\n  margin: 0;\n  border: 0;\n  background-color: transparent;\n  background-clip: padding-box;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn + .o_we_color_combination_btn {\n  margin-top: -4px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected > .o_we_cc_preview_wrapper {\n  box-shadow: 0 0 0 1px #40ad67 !important;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected .o_we_color_combination_btn_title::before {\n  content: \"\\f00c\";\n  margin-right: 8px;\n  font-size: 0.8em;\n  font-family: FontAwesome;\n  color: #40ad67;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn .o_we_cc_preview_wrapper:after {\n  bottom: -1px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor {\n  font-size: 12px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn {\n  color: #ffffff;\n  background-color: #3e3e46;\n  float: none;\n  box-sizing: border-box;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input {\n  border: 1px solid black;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input input {\n  outline: none;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input:focus-within {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale {\n  cursor: copy;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale div {\n  height: 20px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi {\n  display: grid;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range] {\n  pointer-events: none;\n  focusable: false;\n  grid-column: 1/span 2;\n  grid-row: 3;\n  background: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n  cursor: ew-resize;\n}\n\n@supports (-moz-appearance: none) {\n  #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range] {\n    margin-top: 2px;\n  }\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-webkit-slider-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n  box-shadow: 0px 0px 0px #000000;\n  height: 20px;\n  width: 12px;\n  border-radius: 5px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-webkit-slider-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-moz-range-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  box-shadow: 0px 0px 0px #000000;\n  height: 18px;\n  width: 10px;\n  border-radius: 5px;\n  margin-top: 3px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-moz-range-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-ms-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  box-shadow: 0px 0px 0px #000000;\n  height: 20px;\n  width: 12px;\n  border-radius: 5px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-ms-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_remove_color {\n  font-size: 14px !important;\n  text-align: center !important;\n  padding: 0;\n}\n\n@keyframes dropZoneInsert {\n  to {\n    background-color: rgba(113, 75, 103, 0.3);\n  }\n}\n\n.oe_drop_zone {\n  background-color: rgba(113, 75, 103, 0.15);\n  animation: dropZoneInsert 1s linear 0s infinite alternate;\n}\n\n.oe_drop_zone.oe_insert {\n  position: relative;\n  z-index: 1040;\n  width: 100%;\n  border: 2px dashed #2b2b33;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_insert {\n  min-width: 30px;\n  height: 30px;\n  min-height: 30px;\n  margin: -15px 0;\n  padding: 0;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_insert.oe_vertical {\n  width: 30px;\n  float: left;\n  margin: 0 -15px;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_drop_zone_danger {\n  background-color: rgba(230, 88, 108, 0.15);\n  color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators {\n  position: relative;\n  z-index: 1040;\n  pointer-events: none;\n}\n\n#oe_manipulators .oe_overlay {\n  position: absolute;\n  top: auto;\n  left: auto;\n  bottom: auto;\n  right: auto;\n  display: none;\n  border-color: #01bad2;\n  background: transparent;\n  text-align: center;\n  font-size: 16px;\n  transition: opacity 400ms linear 0s;\n}\n\n#oe_manipulators .oe_overlay.o_overlay_hidden {\n  opacity: 0;\n  transition: none;\n}\n\n#oe_manipulators .oe_overlay.oe_active {\n  display: block;\n  z-index: 1;\n}\n\n#oe_manipulators .oe_overlay > .o_handles {\n  position: absolute;\n  top: -10000px;\n  left: 0;\n  bottom: auto;\n  right: 0;\n  border-color: inherit;\n  pointer-events: auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles:hover > .o_handle {\n  background-color: rgba(1, 186, 210, 0.05);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle {\n  position: relative;\n  border: 0 solid transparent;\n  border-color: inherit;\n  transition: background 300ms ease 0s;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w {\n  position: absolute;\n  top: 10000px;\n  left: 0;\n  bottom: -10000px;\n  right: auto;\n  width: 8px;\n  border-width: 2px;\n  border-right-width: 0;\n  cursor: e-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w.o_grid_handle {\n  cursor: ew-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after {\n  position: absolute;\n  top: 50%;\n  left: 40%;\n  bottom: auto;\n  right: auto;\n  margin-top: -10px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w.o_grid_handle:after {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.e {\n  position: absolute;\n  top: 10000px;\n  left: auto;\n  bottom: -10000px;\n  right: 0;\n  width: 8px;\n  border-right-width: 2px;\n  cursor: w-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.e.o_grid_handle {\n  cursor: ew-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after {\n  position: absolute;\n  top: 50%;\n  left: auto;\n  bottom: auto;\n  right: 40%;\n  margin-top: -10px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.e.o_grid_handle:after {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n {\n  position: absolute;\n  top: 10000px;\n  left: 0;\n  bottom: auto;\n  right: 0;\n  height: 8px;\n  border-top-width: 2px;\n  cursor: ns-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after {\n  position: absolute;\n  top: 40%;\n  left: 50%;\n  bottom: auto;\n  right: auto;\n  margin-left: -10px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle:after {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s {\n  position: absolute;\n  top: auto;\n  left: 0;\n  bottom: -10000px;\n  right: 0;\n  height: 8px;\n  border-bottom-width: 2px;\n  cursor: ns-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after {\n  position: absolute;\n  top: auto;\n  left: 50%;\n  bottom: 40%;\n  right: auto;\n  margin-left: -10px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle:after {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne {\n  position: absolute;\n  top: 10002px;\n  left: auto;\n  bottom: auto;\n  right: 2px;\n  height: 8px;\n  cursor: nesw-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.se {\n  position: absolute;\n  top: auto;\n  left: auto;\n  bottom: -10002px;\n  right: 2px;\n  height: 8px;\n  cursor: nwse-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.se:after {\n  position: absolute;\n  top: 50%;\n  left: auto;\n  bottom: auto;\n  right: 40%;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw {\n  position: absolute;\n  top: auto;\n  left: 2px;\n  bottom: -10001px;\n  right: auto;\n  height: 8px;\n  cursor: nesw-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw:after {\n  position: absolute;\n  top: auto;\n  left: 40%;\n  bottom: 40%;\n  right: auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw {\n  position: absolute;\n  top: 9998px;\n  left: 3px;\n  bottom: auto;\n  right: auto;\n  height: 8px;\n  cursor: nwse-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw:after {\n  position: absolute;\n  top: auto;\n  left: 50%;\n  bottom: 40%;\n  right: auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne, #oe_manipulators .oe_overlay > .o_handles > .o_handle.sw {\n  content: \"\\f065\";\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw, #oe_manipulators .oe_overlay > .o_handles > .o_handle.se {\n  content: \"\\f065\";\n  transform: rotate(90deg);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle::after {\n  z-index: 1;\n  display: block;\n  width: 20px;\n  height: 20px;\n  border: solid 1px #01606c;\n  line-height: 18px;\n  font-size: 14px;\n  font-family: FontAwesome;\n  background-color: #018d9f;\n  color: white;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start:after {\n  background-color: rgba(89, 89, 100, 0.6);\n  border-color: rgba(0, 0, 0, 0.2);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active {\n  background-color: rgba(1, 186, 210, 0.2);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover::after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active::after {\n  border-color: #018d9f;\n  background-color: #01606c;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after {\n  content: \"\\f07e\";\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after {\n  content: \"\\f07d\";\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.sw:after {\n  content: \"\\f065\";\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.se:after {\n  content: \"\\f065\";\n  transform: rotate(180deg);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.e:after {\n  content: '\\f061';\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.s:after {\n  content: '\\f063';\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.e:after {\n  content: '\\f060';\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.s:after {\n  content: '\\f062';\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly {\n  cursor: auto !important;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:after {\n  display: none !important;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:hover {\n  opacity: 0.5;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap {\n  position: absolute;\n  top: 10000px;\n  left: 50%;\n  bottom: auto;\n  right: auto;\n  transform: translate(-50%, -110%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap.o_we_hidden_overlay_options {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button {\n  margin: 0 1px 0;\n  min-width: 22px;\n  padding: 0 3px;\n  color: #FFFFFF;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.fa-trash {\n  background-color: #a05968;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.fa-trash:not(.oe_snippet_remove) {\n  opacity: 0.5;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_move_handle {\n  cursor: move;\n  width: 30px;\n  height: 22px;\n  background-image: url(\"/web_editor/static/src/img/snippets_options/o_overlay_move_drag.svg\");\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_send_back {\n  width: 30px;\n  height: 22px;\n  background-image: url(\"/web_editor/static/src/img/snippets_options/bring-backward.svg\");\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_bring_front {\n  width: 30px;\n  height: 22px;\n  background-image: url(\"/web_editor/static/src/img/snippets_options/bring-forward.svg\");\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button {\n  opacity: 0.6;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button.focus {\n  opacity: 1;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover {\n  border-color: #2b2c34;\n  background-color: #2b2b33;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover.fa-trash {\n  border-color: #2c2b33;\n  background-color: #e6586c;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover.fa-trash:not(.oe_snippet_remove) {\n  opacity: 0.5;\n}\n\n#oe_manipulators .oe_overlay.o_top_cover > .o_handles > .o_overlay_options_wrap {\n  top: auto;\n  bottom: -10000px;\n  transform: translate(-50%, 110%);\n}\n\n#oe_manipulators .oe_overlay.o_we_overlay_preview {\n  pointer-events: none;\n}\n\n#oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles > .o_handle::after, #oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles .o_overlay_options_wrap {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay {\n  background-color: rgba(0, 0, 0, 0.7);\n  pointer-events: auto;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content {\n  cursor: url(/web/static/img/openhand.cur), grab;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content .o_we_grabbing {\n  cursor: grabbing;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary {\n  color: #FFFFFF;\n  background-color: #017e84;\n  border-color: #017e84;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:hover {\n  color: #FFFFFF;\n  background-color: #016b70;\n  border-color: #01656a;\n}\n\n.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus {\n  color: #FFFFFF;\n  background-color: #016b70;\n  border-color: #01656a;\n  box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #01656a;\n  border-color: #015f63;\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.disabled {\n  color: #FFFFFF;\n  background-color: #017e84;\n  border-color: #017e84;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary {\n  color: #000000;\n  background-color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:hover {\n  color: #000000;\n  background-color: #ea7182;\n  border-color: #e9697b;\n}\n\n.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus {\n  color: #000000;\n  background-color: #ea7182;\n  border-color: #e9697b;\n  box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle {\n  color: #000000;\n  background-color: #eb7989;\n  border-color: #e9697b;\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.disabled {\n  color: #000000;\n  background-color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_overlay_background > * {\n  display: block !important;\n  top: 0 !important;\n  right: 0 !important;\n  bottom: 0 !important;\n  left: 0 !important;\n  transform: none !important;\n  max-width: unset !important;\n  max-height: unset !important;\n}\n\n#oe_manipulators .o_edit_menu_popover {\n  pointer-events: auto;\n}\n\n.oe_overlay.ui-draggable-dragging .o_handles {\n  display: none;\n}\n\n.s-resize-important * {\n  cursor: s-resize !important;\n}\n\n.n-resize-important * {\n  cursor: n-resize !important;\n}\n\n.e-resize-important * {\n  cursor: e-resize !important;\n}\n\n.w-resize-important * {\n  cursor: w-resize !important;\n}\n\n.move-important * {\n  cursor: move !important;\n}\n\n.dropdown-menu label .o_switch {\n  margin: 0;\n  padding: 2px 0;\n}\n\n.text-input-group {\n  position: relative;\n  margin-bottom: 45px;\n  /* LABEL ======================================= */\n  /* active state */\n  /* BOTTOM BARS ================================= */\n  /* active state */\n  /* HIGHLIGHTER ================================== */\n  /* active state */\n}\n\n.text-input-group input {\n  font-size: 18px;\n  padding: 10px 10px 10px 5px;\n  display: block;\n  width: 300px;\n  border: none;\n  border-bottom: 1px solid #757575;\n}\n\n.text-input-group input:focus {\n  outline: none;\n}\n\n.text-input-group label {\n  color: #999;\n  font-size: 18px;\n  font-weight: normal;\n  position: absolute;\n  top: 10px;\n  left: 5px;\n  bottom: auto;\n  right: auto;\n  pointer-events: none;\n  transition: 0.2s ease all;\n}\n\n.text-input-group input:focus ~ label,\n.text-input-group input:valid ~ label {\n  top: -20px;\n  font-size: 14px;\n  color: #5264AE;\n}\n\n.text-input-group .bar {\n  position: relative;\n  display: block;\n  width: 300px;\n}\n\n.text-input-group .bar:before,\n.text-input-group .bar:after {\n  content: '';\n  height: 2px;\n  width: 0;\n  bottom: 1px;\n  position: absolute;\n  top: auto;\n  left: auto;\n  bottom: auto;\n  right: auto;\n  background: #5264AE;\n  transition: 0.2s ease all;\n}\n\n.text-input-group .bar:before {\n  left: 50%;\n}\n\n.text-input-group .bar:after {\n  right: 50%;\n}\n\n.text-input-group input:focus ~ .bar:before,\n.text-input-group input:focus ~ .bar:after {\n  width: 50%;\n}\n\n.text-input-group .highlight {\n  position: absolute;\n  top: 25%;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  height: 60%;\n  width: 100px;\n  pointer-events: none;\n  opacity: 0.5;\n}\n\n.text-input-group input:focus ~ .highlight {\n  animation: inputHighlighter 0.3s ease;\n}\n\n.oe_snippet_body {\n  opacity: 0;\n  animation: fadeInDownSmall 700ms forwards;\n}\n\n.o_container_preview {\n  outline: 2px dashed #01bad2;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape] {\n  padding: 0 !important;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape].active {\n  border: 1px solid #40ad67 !important;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape] div {\n  width: 100%;\n}\n\nwe-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content {\n  width: 100%;\n  height: 75px;\n}\n\n.o_we_shape_animated_label {\n  position: absolute;\n  top: 0;\n  left: auto;\n  bottom: auto;\n  right: 0;\n  padding: 0 6px;\n  background: #40ad67;\n  color: white;\n}\n\n.o_we_shape_animated_label > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  max-width: 0;\n}\n\nwe-button[data-img-size] {\n  position: relative;\n}\n\nwe-button[data-img-size]::before, we-button[data-img-size]::after {\n  padding: 0 6px;\n  background: #40ad67;\n  color: white;\n  text-align: center;\n}\n\nwe-button[data-img-size]::before {\n  font-family: \"FontAwesome\" !important;\n  content: \"\\f065\";\n  position: absolute;\n  top: 0;\n  left: inherit;\n  bottom: inherit;\n  right: 0;\n}\n\nwe-button[data-img-size]::after {\n  content: attr(data-img-size);\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: inherit;\n  right: 0;\n  transform: translateX(100%);\n  transition: 0.2s ease all;\n}\n\nwe-button[data-img-size]:hover::after {\n  transform: translateX(0%);\n}\n\nwe-button:hover .o_we_shape_animated_label > span {\n  max-width: 150px;\n  transition: max-width 0.5s ease 0s;\n}\n\n.o_we_ui_loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 1041;\n  background-color: rgba(0, 0, 0, 0.2);\n  color: #FFFFFF;\n}\n\n#oe_manipulators > .o_we_ui_loading {\n  position: fixed;\n}\n\n.o_we_force_no_transition {\n  transition: none !important;\n}\n\nwe-button.o_grid {\n  min-width: fit-content;\n  padding-left: 4.5px !important;\n  padding-right: 4.5px !important;\n}\n\nwe-select.o_grid we-toggler {\n  width: fit-content !important;\n}\n\nwe-button-group.o_grid we-selection-items {\n  width: fit-content !important;\n}\n\n.o_we_background_grid {\n  padding: 0 !important;\n}\n\n.o_we_background_grid .o_we_cell {\n  fill: #714B67;\n  fill-opacity: .2;\n  stroke: #2b2b33;\n  stroke-width: 1px;\n  filter: drop-shadow(-1px -1px 0px rgba(255, 255, 255, 0.6));\n}\n\n.o_we_drag_helper {\n  padding: 0;\n  border: 2px solid #01bad2;\n}\n\n@keyframes highlightPadding {\n  from {\n    border: solid rgba(1, 186, 210, 0.2);\n    border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);\n  }\n  to {\n    border: solid rgba(1, 186, 210, 0);\n    border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);\n  }\n}\n\n.o_we_padding_highlight > .o_grid_item {\n  position: relative;\n}\n\n.o_we_padding_highlight > .o_grid_item::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  animation: highlightPadding 2s;\n  pointer-events: none;\n}"], "file": "/web/assets/1230-040984f/web_editor.assets_wysiwyg.css", "sourceRoot": "../../../"}