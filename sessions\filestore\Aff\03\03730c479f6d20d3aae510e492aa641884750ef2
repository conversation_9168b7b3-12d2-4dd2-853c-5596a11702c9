{"version": 5, "sheets": [{"id": "dc957ee9-52f8-4fef-9c58-0ac5c002292f", "name": "Dashboard", "colNumber": 26, "rowNumber": 97, "rows": {}, "cols": {"0": {"size": 113}, "1": {"size": 113}, "2": {"size": 113}, "3": {"size": 113}, "4": {"size": 113}, "5": {"size": 113}, "6": {"size": 113}, "7": {"size": 113}}, "merges": ["A30:C31", "E30:G31", "A56:C57", "A39:D40", "A1:K2"], "cells": {"A30": {"content": "Opportunities by Stage", "style": 8}, "A31": {"content": "", "style": 8}, "B30": {"content": "", "style": 8}, "B31": {"content": "", "style": 8}, "C30": {"content": "", "style": 8}, "C31": {"content": "", "style": 8}, "E30": {"content": "Lost Opportunities by Stage", "style": 8}, "E31": {"content": "", "style": 8}, "F30": {"content": "", "style": 8}, "F31": {"content": "", "style": 8}, "G30": {"content": "", "style": 8}, "G31": {"content": "", "style": 8}, "B32": {"content": "=PIVOT.HEADER(\"1\",\"measure\",\"__count\")", "style": 4}, "C32": {"content": "=PIVOT.HEADER(\"1\",\"measure\",\"expected_revenue\")", "style": 4}, "A33": {"content": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "style": 3}, "B33": {"content": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "format": "#,##0.00"}, "C33": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "format": "#,##0.00"}, "A34": {"content": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "style": 3}, "B34": {"content": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "format": "#,##0.00"}, "C34": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "format": "#,##0.00"}, "A35": {"content": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "style": 3}, "B35": {"content": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "format": "#,##0.00"}, "C35": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "format": "#,##0.00"}, "A36": {"content": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "style": 3}, "B36": {"content": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "format": "#,##0.00"}, "C36": {"content": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "format": "#,##0.00"}, "A37": {"content": "=PIVOT.HEADER(\"1\")", "style": 3}, "B37": {"content": "=PIVOT(\"1\",\"__count\")", "format": "#,##0.00"}, "C37": {"content": "=PIVOT(\"1\",\"expected_revenue\")", "format": "#,##0.00"}, "F32": {"content": "=PIVOT.HEADER(\"4\",\"measure\",\"__count\")", "style": 4}, "G32": {"content": "=PIVOT.HEADER(\"4\",\"measure\",\"expected_revenue\")", "style": 4}, "E33": {"content": "=PIVOT.HEADER(\"4\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "style": 3}, "F33": {"content": "=PIVOT(\"4\",\"__count\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "format": "#,##0.00"}, "G33": {"content": "=PIVOT(\"4\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "format": "#,##0.00"}, "E34": {"content": "=PIVOT.HEADER(\"4\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",2))", "style": 3}, "F34": {"content": "=PIVOT(\"4\",\"__count\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",2))", "format": "#,##0.00"}, "G34": {"content": "=PIVOT(\"4\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",2))", "format": "#,##0.00"}, "E35": {"content": "=PIVOT.HEADER(\"4\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",3))", "style": 3}, "F35": {"content": "=PIVOT(\"4\",\"__count\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",3))", "format": "#,##0.00"}, "G35": {"content": "=PIVOT(\"4\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",3))", "format": "#,##0.00"}, "E36": {"content": "=PIVOT.HEADER(\"4\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",4))", "style": 3}, "F36": {"content": "=PIVOT(\"4\",\"__count\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",4))", "format": "#,##0.00"}, "G36": {"content": "=PIVOT(\"4\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",4))", "format": "#,##0.00"}, "E37": {"content": "=PIVOT.HEADER(\"4\")", "style": 3}, "F37": {"content": "=PIVOT(\"4\",\"__count\")", "format": "#,##0.00"}, "G37": {"content": "=PIVOT(\"4\",\"expected_revenue\")", "format": "#,##0.00"}, "A39": {"content": "Actuals vs Target", "style": 8}, "A32": {"content": "", "style": 2}, "E32": {"content": "", "style": 2}, "B41": {"content": "Actuals", "style": 4}, "A42": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B42": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A43": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B43": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A44": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B44": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A45": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B45": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A46": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B46": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A47": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B47": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A48": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B48": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A49": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B49": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A50": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B50": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A51": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B51": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A52": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B52": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A53": {"content": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "style": 3}, "B53": {"content": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "format": "#,##0.00"}, "A54": {"content": "=PIVOT.HEADER(\"2\")", "style": 3}, "B54": {"content": "=sum(B42:B53)", "format": "#,##0.00"}, "A56": {"content": "Lost Reasons", "style": 8}, "A57": {"content": "", "style": 8}, "B56": {"content": "", "style": 8}, "B57": {"content": "", "style": 8}, "C56": {"content": "", "style": 8}, "C57": {"content": "", "style": 8}, "A41": {"content": "", "style": 2}, "B58": {"content": "=PIVOT.HEADER(\"3\",\"measure\",\"__count\")", "style": 4}, "C58": {"content": "=PIVOT.HEADER(\"3\",\"measure\",\"expected_revenue\")", "style": 4}, "A59": {"content": "=PIVOT.HEADER(\"3\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",1))", "style": 3}, "B59": {"content": "=PIVOT(\"3\",\"__count\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",1))", "format": "#,##0.00"}, "C59": {"content": "=PIVOT(\"3\",\"expected_revenue\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",1))", "format": "#,##0.00"}, "A60": {"content": "=PIVOT.HEADER(\"3\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",2))", "style": 3}, "B60": {"content": "=PIVOT(\"3\",\"__count\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",2))", "format": "#,##0.00"}, "C60": {"content": "=PIVOT(\"3\",\"expected_revenue\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",2))", "format": "#,##0.00"}, "A61": {"content": "=PIVOT.HEADER(\"3\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",3))", "style": 3}, "B61": {"content": "=PIVOT(\"3\",\"__count\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",3))", "format": "#,##0.00"}, "C61": {"content": "=PIVOT(\"3\",\"expected_revenue\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",3))", "format": "#,##0.00"}, "A62": {"content": "=PIVOT.HEADER(\"3\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",4))", "style": 3}, "B62": {"content": "=PIVOT(\"3\",\"__count\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",4))", "format": "#,##0.00"}, "C62": {"content": "=PIVOT(\"3\",\"expected_revenue\",\"lost_reason_id\",PIVOT.POSITION(\"3\",\"lost_reason_id\",4))", "format": "#,##0.00"}, "A63": {"content": "=PIVOT.HEADER(\"3\")", "style": 3}, "B63": {"content": "=PIVOT(\"3\",\"__count\")", "format": "#,##0.00"}, "C63": {"content": "=PIVOT(\"3\",\"expected_revenue\")", "format": "#,##0.00"}, "A58": {"content": "", "style": 2}, "C41": {"content": "Target", "style": 17}, "D41": {"content": "Perf.", "style": 17}, "C42": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C43": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C44": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C45": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C46": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C47": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C48": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C49": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C50": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C51": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C52": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C53": {"content": "10000", "style": 20, "format": "#,##0.00"}, "C54": {"content": "=sum(C42:C53)", "format": "#,##0.00"}, "D42": {"content": "=iferror(B42/C42,0)", "format": "0.00%"}, "D43": {"content": "=iferror(B43/C43,0)", "format": "0.00%"}, "D44": {"content": "=iferror(B44/C44,0)", "format": "0.00%"}, "D45": {"content": "=iferror(B45/C45,0)", "format": "0.00%"}, "D46": {"content": "=iferror(B46/C46,0)", "format": "0.00%"}, "D47": {"content": "=iferror(B47/C47,0)", "format": "0.00%"}, "D48": {"content": "=iferror(B48/C48,0)", "format": "0.00%"}, "D49": {"content": "=iferror(B49/C49,0)", "format": "0.00%"}, "D50": {"content": "=iferror(B50/C50,0)", "format": "0.00%"}, "D51": {"content": "=iferror(B51/C51,0)", "format": "0.00%"}, "D52": {"content": "=iferror(B52/C52,0)", "format": "0.00%"}, "D53": {"content": "=iferror(B53/C53,0)", "format": "0.00%"}, "D54": {"content": "=iferror(B54/C54,0)", "format": "0.00%"}, "A1": {"content": "=\"CRM Dashboard - \"&FILTER.VALUE(\"Year\")", "style": 18}, "L1": {"content": "", "style": 19}, "L2": {"content": "", "style": 19}, "M1": {"content": "", "style": 19}, "M2": {"content": "", "style": 19}, "N1": {"content": "", "style": 19}, "N2": {"content": "", "style": 19}, "O1": {"content": "", "style": 19}, "O2": {"content": "", "style": 19}}, "conditionalFormats": [{"rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 9684093}}, "ranges": ["D42:D54"], "id": "b2b15f40-0d23-4ad7-8f44-6305685a47eb"}], "figures": [{"id": "877bde0f-16d0-48fb-abf7-6b3526a05d98", "data": {"title": "Actuals vs Target", "type": "bar", "dataSets": [{"labelCell": "B41", "dataRange": "B42:B53"}, {"labelCell": "C41", "dataRange": "C42:C53"}], "labelRange": "A42:A53", "sheetId": "dc957ee9-52f8-4fef-9c58-0ac5c002292f"}, "x": 0, "y": 44, "height": 318, "width": 779, "tag": "chart"}, {"id": "cf8cc197-6ad9-4ffc-981d-1bde264ffe7c", "data": {"title": "Expected Revenue by Stage", "type": "bar", "dataSets": [{"labelCell": "C32", "dataRange": "C33:C36"}], "labelRange": "A33:A36", "sheetId": "dc957ee9-52f8-4fef-9c58-0ac5c002292f"}, "x": 0, "y": 371, "height": 269, "width": 393, "tag": "chart"}, {"id": "979e6431-5019-4403-88cb-fd616c90f079", "data": {"title": "Opportunities by Stage", "type": "bar", "dataSets": [{"labelCell": "B32", "dataRange": "B33:B36"}], "labelRange": "A33:A36", "sheetId": "dc957ee9-52f8-4fef-9c58-0ac5c002292f"}, "x": 401, "y": 371, "height": 269, "width": 378, "tag": "chart"}, {"id": "297488ab-63ab-42bd-b99d-4b1003d5e23d", "data": {"title": "Loss Reasons", "type": "pie", "dataSets": [{"labelCell": "B58", "dataRange": "B59:B62"}], "labelRange": "A59:A62", "sheetId": "dc957ee9-52f8-4fef-9c58-0ac5c002292f"}, "x": 788, "y": 44, "height": 318, "width": 392, "tag": "chart"}, {"id": "1bdb22be-5f1b-4137-89e9-ae330047876a", "data": {"title": "Lost Opportunities by Stage", "type": "bar", "dataSets": [{"labelCell": "F32", "dataRange": "F33:F36"}], "labelRange": "E33:E36", "sheetId": "dc957ee9-52f8-4fef-9c58-0ac5c002292f"}, "x": 788, "y": 372, "height": 268, "width": 392, "tag": "chart"}]}], "activeSheet": "dc957ee9-52f8-4fef-9c58-0ac5c002292f", "entities": {}, "styles": {"0": {"fillColor": "white", "textColor": "black", "fontSize": 11}, "2": {"fillColor": "#f2f2f2"}, "3": {"fillColor": "#f2f2f2", "bold": true}, "4": {"fillColor": "#f2f2f2", "textColor": "#756f6f"}, "5": {"fillColor": "#cfe2f3"}, "6": {"fillColor": "#cfe2f3", "fontSize": 12}, "7": {"fillColor": "#cfe2f3", "fontSize": 12, "bold": true}, "8": {"fillColor": "#cfe2f3", "fontSize": 12, "bold": true, "align": "center"}, "9": {"fillColor": "#cfe2f3", "align": "center"}, "10": {"fillColor": "#cfe2f3", "align": "center", "bold": true}, "11": {"fontSize": 12}, "12": {"fontSize": 12, "bold": true}, "13": {"fillColor": "#cfe2f3", "bold": true}, "14": {"fillColor": "#f3f3f3"}, "15": {"fillColor": "#f3f3f3", "textColor": "#434343"}, "16": {"fillColor": "#666666", "textColor": "#434343"}, "17": {"fillColor": "#f3f3f3", "textColor": "#666666"}, "18": {"fillColor": "#cfe2f3", "fontSize": 14, "bold": true, "align": "center"}, "19": {"fillColor": "#ffffff", "fontSize": 14, "bold": true, "align": "center"}, "20": {"fillColor": "#fff2cc"}}, "borders": {}, "pivots": {"1": {"model": "crm.lead", "rowGroupBys": ["stage_id"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": [["type", "=", "opportunity"]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 1, "isLoaded": false, "promise": {}}, "2": {"model": "crm.lead", "rowGroupBys": ["date_deadline:month"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 2, "isLoaded": false, "promise": {}}, "3": {"model": "crm.lead", "rowGroupBys": ["lost_reason_id"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], "&", ["active", "=", false], ["probability", "=", 0]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 3, "isLoaded": false, "promise": {}}, "4": {"model": "crm.lead", "rowGroupBys": ["stage_id"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], "&", ["active", "=", false], ["probability", "=", 0]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 4, "isLoaded": false, "promise": {}}}, "globalFilters": [{"id": "b481232d-a842-45c6-9686-184d290decce", "label": "Year", "type": "date", "rangeType": "year", "fields": {"1": {"field": "date_deadline", "type": "date"}, "2": {"field": "date_deadline", "type": "date"}, "3": {"field": "date_closed", "type": "datetime"}, "4": {"field": "date_closed", "type": "datetime"}}, "defaultValue": {"year": "this_year"}}]}