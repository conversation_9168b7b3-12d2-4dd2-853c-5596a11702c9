
/*************************************************
*  Filepath: /web_enterprise/static/src/main.js  *
*  Lines: 18                                     *
*************************************************/
odoo.define('@web_enterprise/main', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { startWebClient } = require("@web/start");
const { WebClientEnterprise } = require("@web_enterprise/webclient/webclient");

/**
 * This file starts the enterprise webclient. In the manifest, it replaces
 * the community main.js to load a different webclient class
 * (WebClientEnterprise instead of WebClient)
 */

startWebClient(WebClientEnterprise);

return __exports;
});
;

/***************************************
*  Filepath: /web/static/src/start.js  *
*  Lines: 82                           *
***************************************/
odoo.define('@web/start', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { makeEnv, startServices } = require("@web/env");
const { legacySetupProm } = require("@web/legacy/legacy_setup");
const { mapLegacyEnvToWowlEnv } = require("@web/legacy/utils");
const { localization } = require("@web/core/l10n/localization");
const { session } = require("@web/session");
const { renderToString } = require("@web/core/utils/render");
const { setLoadXmlDefaultApp, templates } = require("@web/core/assets");
const { hasTouch } = require("@web/core/browser/feature_detection");

const { App, whenReady } = require("@odoo/owl");

/**
 * Function to start a webclient.
 * It is used both in community and enterprise in main.js.
 * It's meant to be webclient flexible so we can have a subclass of
 * webclient in enterprise with added features.
 *
 * @param {Component} Webclient
 */
__exports.startWebClient = startWebClient; async function startWebClient(Webclient) {
    odoo.info = {
        db: session.db,
        server_version: session.server_version,
        server_version_info: session.server_version_info,
        isEnterprise: session.server_version_info.slice(-1)[0] === "e",
    };
    odoo.isReady = false;

    // setup environment
    const env = makeEnv();
    await startServices(env);

    // start web client
    await whenReady();
    const legacyEnv = await legacySetupProm;
    mapLegacyEnvToWowlEnv(legacyEnv, env);
    const app = new App(Webclient, {
        env,
        templates,
        dev: env.debug,
        translatableAttributes: ["data-tooltip"],
        translateFn: env._t,
    });
    renderToString.app = app;
    setLoadXmlDefaultApp(app);
    const root = await app.mount(document.body);
    const classList = document.body.classList;
    if (localization.direction === "rtl") {
        classList.add("o_rtl");
    }
    if (env.services.user.userId === 1) {
        classList.add("o_is_superuser");
    }
    if (env.debug) {
        classList.add("o_debug");
    }
    if (hasTouch()) {
        classList.add("o_touch_device");
    }
    // delete odoo.debug; // FIXME: some legacy code rely on this
    odoo.__WOWL_DEBUG__ = { root };
    odoo.isReady = true;

    // Update Favicons
    const favicon = `/web/image/res.company/${env.services.company.currentCompany.id}/favicon`;
    const icons = document.querySelectorAll("link[rel*='icon']");
    const msIcon = document.querySelector("meta[name='msapplication-TileImage']");
    for (const icon of icons) {
        icon.href = favicon;
    }
    if (msIcon) {
        msIcon.content = favicon;
    }
}

return __exports;
});
;

/*****************************************************
*  Filepath: /web/static/src/legacy/legacy_setup.js  *
*  Lines: 67                                         *
*****************************************************/
odoo.define('@web/legacy/legacy_setup', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module alias=web.legacySetup **/

const { registry } = require("@web/core/registry");
const {
    makeLegacyNotificationService,
    makeLegacyRpcService,
    makeLegacySessionService,
    makeLegacyDialogMappingService,
    makeLegacyCrashManagerService,
    makeLegacyCommandService,
    makeLegacyDropdownService,
} = require("@web/legacy/utils");
const { makeLegacyActionManagerService } = require("@web/legacy/backend_utils");
const AbstractService = require("web.AbstractService");
const legacyEnv = require("web.env");
const session = require("web.session");
const makeLegacyWebClientService = require("web.pseudo_web_client");
const { templates } = require("@web/core/assets");

const { Component, whenReady } = require("@odoo/owl");

let legacySetupResolver;
const legacySetupProm = __exports.legacySetupProm = new Promise((resolve) => {
    legacySetupResolver = resolve;
});

// build the legacy env and set it on Component (this was done in main.js,
// with the starting of the webclient)
(async () => {
    AbstractService.prototype.deployServices(legacyEnv);
    Component.env = legacyEnv;
    const legacyActionManagerService = makeLegacyActionManagerService(legacyEnv);
    const serviceRegistry = registry.category("services");
    serviceRegistry.add("legacy_action_manager", legacyActionManagerService);
    // add a service to redirect rpc events triggered on the bus in the
    // legacy env on the bus in the wowl env
    const legacyRpcService = makeLegacyRpcService(legacyEnv);
    serviceRegistry.add("legacy_rpc", legacyRpcService);
    const legacySessionService = makeLegacySessionService(legacyEnv, session);
    serviceRegistry.add("legacy_session", legacySessionService);
    const legacyWebClientService = makeLegacyWebClientService(legacyEnv);
    serviceRegistry.add("legacy_web_client", legacyWebClientService);
    serviceRegistry.add("legacy_notification", makeLegacyNotificationService(legacyEnv));
    serviceRegistry.add("legacy_crash_manager", makeLegacyCrashManagerService(legacyEnv));
    const legacyDialogMappingService = makeLegacyDialogMappingService(legacyEnv);
    serviceRegistry.add("legacy_dialog_mapping", legacyDialogMappingService);
    const legacyCommandService = makeLegacyCommandService(legacyEnv);
    serviceRegistry.add("legacy_command", legacyCommandService);
    serviceRegistry.add("legacy_dropdown", makeLegacyDropdownService(legacyEnv));
    const wowlToLegacyServiceMappers = registry.category("wowlToLegacyServiceMappers").getEntries();
    for (const [legacyServiceName, wowlToLegacyServiceMapper] of wowlToLegacyServiceMappers) {
        serviceRegistry.add(legacyServiceName, wowlToLegacyServiceMapper(legacyEnv));
    }
    await Promise.all([whenReady(), session.is_bound]);
    legacyEnv.templates = templates;
    legacySetupResolver(legacyEnv);
})();

return __exports;
});

odoo.define(`web.legacySetup`, async function(require) {
                        return require('@web/legacy/legacy_setup')[Symbol.for("default")];
                        });
;

/***************************************
*  Filepath: /mail/static/src/main.js  *
*  Lines: 35                           *
***************************************/
odoo.define('@mail/main', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { ChatWindowManagerContainer } = require('@mail/components/chat_window_manager_container/chat_window_manager_container');
const { DialogManagerContainer } = require('@mail/components/dialog_manager_container/dialog_manager_container');
const { DiscussContainer } = require('@mail/components/discuss_container/discuss_container');
const { PopoverManagerContainer } = require('@mail/components/popover_manager_container/popover_manager_container');
const { messagingService } = require('@mail/services/messaging_service');
const { systrayService } = require('@mail/services/systray_service');
const { makeMessagingToLegacyEnv } = require('@mail/utils/make_messaging_to_legacy_env');

const { registry } = require('@web/core/registry');

const messagingValuesService = {
    start() {
        return {};
    }
};

const serviceRegistry = registry.category('services');
serviceRegistry.add('messaging', messagingService);
serviceRegistry.add('messagingValues', messagingValuesService);
serviceRegistry.add('systray_service', systrayService);
serviceRegistry.add('messaging_service_to_legacy_env', makeMessagingToLegacyEnv(owl.Component.env));

registry.category('actions').add('mail.action_discuss', DiscussContainer);

registry.category('main_components').add('DialogManagerContainer', { Component: DialogManagerContainer });
registry.category('main_components').add('ChatWindowManagerContainer', { Component: ChatWindowManagerContainer });
registry.category('main_components').add('PopoverManagerContainer', { Component: PopoverManagerContainer });

return __exports;
});


//# sourceMappingURL=/web/assets/1223-41a5e4a/web.assets_backend_prod_only.js.map