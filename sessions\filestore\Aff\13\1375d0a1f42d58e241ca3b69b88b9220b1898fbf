{"version": 12, "sheets": [{"id": "Sheet1", "name": "Dashboard", "colNumber": 7, "rowNumber": 70, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}, "44": {"size": 40}, "45": {"size": 40}, "57": {"size": 40}, "58": {"size": 40}}, "cols": {"0": {"size": 275}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 275}, "5": {"size": 100}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Leads by Month](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"type\",\"=\",\"opportunity\"]],\"context\":{\"group_by\":[\"create_date:month\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"create_date:month\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A19": {"style": 1, "content": "[Top Countries](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"country_id\",\"!=\",false]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Country\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",1)"}, "A22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",2)"}, "A23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",3)"}, "A24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",4)"}, "A25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",5)"}, "A26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",6)"}, "A27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",7)"}, "A28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",8)"}, "A29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",9)"}, "A30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(7,\"#country_id\",10)"}, "A32": {"style": 1, "content": "[Top Lost Reasons](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],\"&\",\"&\",[\"active\",\"=\",false],[\"probability\",\"=\",0],[\"lost_reason_id\",\"!=\",false]],\"context\":{\"group_by\":[\"lost_reason_id\"],\"pivot_measures\":[\"__count\",\"expected_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"lost_reason_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "A33": {"style": 2, "content": "=_t(\"Lost Reason\")", "border": 2}, "A34": {"style": 3, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",1)"}, "A35": {"style": 4, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",2)"}, "A36": {"style": 3, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",3)"}, "A37": {"style": 4, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",4)"}, "A38": {"style": 3, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",5)"}, "A39": {"style": 4, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",6)"}, "A40": {"style": 3, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",7)"}, "A41": {"style": 4, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",8)"}, "A42": {"style": 3, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",9)"}, "A43": {"style": 4, "content": "=ODOO.PIVOT.HEADER(9,\"#lost_reason_id\",10)"}, "A45": {"style": 1, "content": "[Top Campaigns](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"campaign_id\",\"!=\",false]],\"context\":{\"group_by\":[\"campaign_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"campaign_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "A46": {"style": 2, "content": "=_t(\"Campaign\")", "border": 2}, "A47": {"style": 3, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",1)"}, "A48": {"style": 4, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",2)"}, "A49": {"style": 3, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",3)"}, "A50": {"style": 4, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",4)"}, "A51": {"style": 3, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",5)"}, "A52": {"style": 4, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",6)"}, "A53": {"style": 3, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",7)"}, "A54": {"style": 4, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",8)"}, "A55": {"style": 3, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",9)"}, "A56": {"style": 4, "content": "=ODOO.PIVOT.HEADER(11,\"#campaign_id\",10)"}, "A58": {"style": 1, "content": "[Top Sales Teams](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"team_id\",\"!=\",false]],\"context\":{\"group_by\":[\"team_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "A59": {"style": 2, "content": "=_t(\"Sales Team\")", "border": 2}, "A60": {"style": 3, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",1)"}, "A61": {"style": 4, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",2)"}, "A62": {"style": 3, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",3)"}, "A63": {"style": 4, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",4)"}, "A64": {"style": 3, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",5)"}, "A65": {"style": 4, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",6)"}, "A66": {"style": 3, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",7)"}, "A67": {"style": 4, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",8)"}, "A68": {"style": 3, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",9)"}, "A69": {"style": 4, "content": "=ODOO.PIVOT.HEADER(13,\"#team_id\",10)"}, "B7": {"style": 5, "border": 1}, "B20": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "B21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",1)"}, "B22": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",2)"}, "B23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",3)"}, "B24": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",4)"}, "B25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",5)"}, "B26": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",6)"}, "B27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",7)"}, "B28": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",8)"}, "B29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",9)"}, "B30": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#country_id\",10)"}, "B33": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "B34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",1)"}, "B35": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",2)"}, "B36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",3)"}, "B37": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",4)"}, "B38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",5)"}, "B39": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",6)"}, "B40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",7)"}, "B41": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",8)"}, "B42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",9)"}, "B43": {"format": 1, "content": "=ODOO.PIVOT(9,\"__count\",\"#lost_reason_id\",10)"}, "B44": {"format": 1}, "B46": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "B47": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",1)"}, "B48": {"format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",2)"}, "B49": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",3)"}, "B50": {"format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",4)"}, "B51": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",5)"}, "B52": {"format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",6)"}, "B53": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",7)"}, "B54": {"format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",8)"}, "B55": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",9)"}, "B56": {"format": 1, "content": "=ODOO.PIVOT(11,\"__count\",\"#campaign_id\",10)"}, "B59": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "B60": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",1)"}, "B61": {"format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",2)"}, "B62": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",3)"}, "B63": {"format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",4)"}, "B64": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",5)"}, "B65": {"format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",6)"}, "B66": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",7)"}, "B67": {"format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",8)"}, "B68": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",9)"}, "B69": {"format": 1, "content": "=ODOO.PIVOT(13,\"__count\",\"#team_id\",10)"}, "C7": {"style": 5, "border": 1}, "C20": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "C21": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",1)"}, "C22": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",2)"}, "C23": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",3)"}, "C24": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",4)"}, "C25": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",5)"}, "C26": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",6)"}, "C27": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",7)"}, "C28": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",8)"}, "C29": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",9)"}, "C30": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#country_id\",10)"}, "C33": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "C34": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",1)"}, "C35": {"format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",2)"}, "C36": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",3)"}, "C37": {"format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",4)"}, "C38": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",5)"}, "C39": {"format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",6)"}, "C40": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",7)"}, "C41": {"format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",8)"}, "C42": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",9)"}, "C43": {"format": 2, "content": "=ODOO.PIVOT(9,\"expected_revenue\",\"#lost_reason_id\",10)"}, "C44": {"format": 3}, "C46": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "C47": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",1)"}, "C48": {"format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",2)"}, "C49": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",3)"}, "C50": {"format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",4)"}, "C51": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",5)"}, "C52": {"format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",6)"}, "C53": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",7)"}, "C54": {"format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",8)"}, "C55": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",9)"}, "C56": {"format": 2, "content": "=ODOO.PIVOT(11,\"prorated_revenue\",\"#campaign_id\",10)"}, "C59": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "C60": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",1)"}, "C61": {"format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",2)"}, "C62": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",3)"}, "C63": {"format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",4)"}, "C64": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",5)"}, "C65": {"format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",6)"}, "C66": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",7)"}, "C67": {"format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",8)"}, "C68": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",9)"}, "C69": {"format": 2, "content": "=ODOO.PIVOT(13,\"prorated_revenue\",\"#team_id\",10)"}, "D7": {"style": 5, "border": 1}, "E7": {"style": 5, "border": 1}, "E19": {"style": 1, "content": "[Top Tags](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"tag_ids\",\"!=\",false]],\"context\":{\"group_by\":[\"tag_ids\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"tag_ids\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "E20": {"style": 2, "content": "=_t(\"Tag\")", "border": 2}, "E21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",1)"}, "E22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",2)"}, "E23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",3)"}, "E24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",4)"}, "E25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",5)"}, "E26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",6)"}, "E27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",7)"}, "E28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",8)"}, "E29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",9)"}, "E30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(8,\"#tag_ids\",10)"}, "E32": {"style": 1, "content": "[Top Mediums](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"medium_id\",\"!=\",false]],\"context\":{\"group_by\":[\"medium_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"medium_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "E33": {"style": 2, "content": "=_t(\"Medium\")", "border": 2}, "E34": {"style": 3, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",1)"}, "E35": {"style": 4, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",2)"}, "E36": {"style": 3, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",3)"}, "E37": {"style": 4, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",4)"}, "E38": {"style": 3, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",5)"}, "E39": {"style": 4, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",6)"}, "E40": {"style": 3, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",7)"}, "E41": {"style": 4, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",8)"}, "E42": {"style": 3, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",9)"}, "E43": {"style": 4, "content": "=ODOO.PIVOT.HEADER(10,\"#medium_id\",10)"}, "E45": {"style": 1, "content": "[Top Sources](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"source_id\",\"!=\",false]],\"context\":{\"group_by\":[\"source_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"source_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "E46": {"style": 2, "content": "=_t(\"Source\")", "border": 2}, "E47": {"style": 3, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",1)"}, "E48": {"style": 4, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",2)"}, "E49": {"style": 3, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",3)"}, "E50": {"style": 4, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",4)"}, "E51": {"style": 3, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",5)"}, "E52": {"style": 4, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",6)"}, "E53": {"style": 3, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",7)"}, "E54": {"style": 4, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",8)"}, "E55": {"style": 3, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",9)"}, "E56": {"style": 4, "content": "=ODOO.PIVOT.HEADER(12,\"#source_id\",10)"}, "E58": {"style": 1, "content": "[Top Salespeople](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",\"|\",[\"active\",\"=\",true],[\"active\",\"=\",false],[\"user_id\",\"!=\",false]],\"context\":{\"group_by\":[\"user_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"user_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\"})", "border": 1}, "E59": {"style": 2, "content": "=_t(\"Salesperson\")", "border": 2}, "E60": {"style": 3, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",1)"}, "E61": {"style": 4, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",2)"}, "E62": {"style": 3, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",3)"}, "E63": {"style": 4, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",4)"}, "E64": {"style": 3, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",5)"}, "E65": {"style": 4, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",6)"}, "E66": {"style": 3, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",7)"}, "E67": {"style": 4, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",8)"}, "E68": {"style": 3, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",9)"}, "E69": {"style": 4, "content": "=ODOO.PIVOT.HEADER(14,\"#user_id\",10)"}, "F7": {"style": 5, "border": 1}, "F20": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "F21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",1)"}, "F22": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",2)"}, "F23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",3)"}, "F24": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",4)"}, "F25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",5)"}, "F26": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",6)"}, "F27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",7)"}, "F28": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",8)"}, "F29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",9)"}, "F30": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#tag_ids\",10)"}, "F33": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "F34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",1)"}, "F35": {"format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",2)"}, "F36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",3)"}, "F37": {"format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",4)"}, "F38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",5)"}, "F39": {"format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",6)"}, "F40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",7)"}, "F41": {"format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",8)"}, "F42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",9)"}, "F43": {"format": 1, "content": "=ODOO.PIVOT(10,\"__count\",\"#medium_id\",10)"}, "F46": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "F47": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",1)"}, "F48": {"format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",2)"}, "F49": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",3)"}, "F50": {"format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",4)"}, "F51": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",5)"}, "F52": {"format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",6)"}, "F53": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",7)"}, "F54": {"format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",8)"}, "F55": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",9)"}, "F56": {"format": 1, "content": "=ODOO.PIVOT(12,\"__count\",\"#source_id\",10)"}, "F59": {"style": 6, "content": "=_t(\"# Leads\")", "border": 2}, "F60": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",1)"}, "F61": {"format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",2)"}, "F62": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",3)"}, "F63": {"format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",4)"}, "F64": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",5)"}, "F65": {"format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",6)"}, "F66": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",7)"}, "F67": {"format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",8)"}, "F68": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",9)"}, "F69": {"format": 1, "content": "=ODOO.PIVOT(14,\"__count\",\"#user_id\",10)"}, "G7": {"style": 5, "border": 1}, "G20": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "G21": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",1)"}, "G22": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",2)"}, "G23": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",3)"}, "G24": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",4)"}, "G25": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",5)"}, "G26": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",6)"}, "G27": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",7)"}, "G28": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",8)"}, "G29": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",9)"}, "G30": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#tag_ids\",10)"}, "G33": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "G34": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",1)"}, "G35": {"format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",2)"}, "G36": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",3)"}, "G37": {"format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",4)"}, "G38": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",5)"}, "G39": {"format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",6)"}, "G40": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",7)"}, "G41": {"format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",8)"}, "G42": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",9)"}, "G43": {"format": 2, "content": "=ODOO.PIVOT(10,\"prorated_revenue\",\"#medium_id\",10)"}, "G46": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "G47": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",1)"}, "G48": {"format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",2)"}, "G49": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",3)"}, "G50": {"format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",4)"}, "G51": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",5)"}, "G52": {"format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",6)"}, "G53": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",7)"}, "G54": {"format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",8)"}, "G55": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",9)"}, "G56": {"format": 2, "content": "=ODOO.PIVOT(12,\"prorated_revenue\",\"#source_id\",10)"}, "G59": {"style": 6, "content": "=_t(\"Revenue\")", "border": 2}, "G60": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",1)"}, "G61": {"format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",2)"}, "G62": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",3)"}, "G63": {"format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",4)"}, "G64": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",5)"}, "G65": {"format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",6)"}, "G66": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",7)"}, "G67": {"format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",8)"}, "G68": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",9)"}, "G69": {"format": 2, "content": "=ODOO.PIVOT(14,\"prorated_revenue\",\"#user_id\",10)"}, "A8": {"border": 2}, "B8": {"border": 2}, "B19": {"border": 1}, "B32": {"border": 1}, "B45": {"border": 1}, "B58": {"border": 1}, "C8": {"border": 2}, "C19": {"border": 1}, "C32": {"border": 1}, "C45": {"border": 1}, "C58": {"border": 1}, "D8": {"border": 2}, "E8": {"border": 2}, "F8": {"border": 2}, "F19": {"border": 1}, "F32": {"border": 1}, "F45": {"border": 1}, "F58": {"border": 1}, "G8": {"border": 2}, "G19": {"border": 1}, "G32": {"border": 1}, "G45": {"border": 1}, "G58": {"border": 1}}, "conditionalFormats": [], "figures": [{"id": "b9b862dd-1d57-47d9-b46c-ffcf4f8be638", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["create_date:month"], "measure": "__count", "order": null, "resModel": "crm.lead"}, "searchParams": {"comparison": null, "context": {"params": {"menu_id": 428, "cids": 1, "action": 607, "model": "crm.lead", "view_type": "kanban"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "groupBy": ["create_date:month"], "orderBy": []}, "type": "odoo_line", "verticalAxisPosition": "left"}}, {"id": "26d0db83-eced-423a-83ba-143e890a458b", "x": 0, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Close Rate", "type": "scorecard", "background": "", "baseline": "Data!E4", "baselineDescr": "last period", "keyValue": "Data!D4"}}, {"id": "1a364b24-21a0-41fe-8bd3-061efa4f1b1f", "x": 202, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Average Deal Size", "type": "scorecard", "background": "", "baseline": "Data!E5", "baselineDescr": "since last period", "keyValue": "Data!D5"}}, {"id": "070e9913-f280-47fd-99c5-d0675a9e2cc2", "x": 404, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Revenue", "type": "scorecard", "background": "", "baseline": "Data!E6", "baselineDescr": "since last period", "keyValue": "Data!D6"}}, {"id": "9cd65dd7-0333-4fa3-bc21-fa53de78dfbf", "x": 606, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Days to Win", "type": "scorecard", "background": "", "baseline": "Data!E9", "baselineDescr": "last period", "keyValue": "Data!D9"}}, {"id": "8c6ddf6e-2c14-4e32-9499-1b3608f05d1b", "x": 808, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Days to Assign", "type": "scorecard", "background": "", "baseline": "Data!E10", "baselineDescr": "last period", "keyValue": "Data!D10"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "d5999256-9e45-4b90-bce8-511f03087dfa", "name": "Data", "colNumber": 26, "rowNumber": 104, "rows": {"4": {"size": 23}}, "cols": {"0": {"size": 188}, "1": {"size": 157}, "2": {"size": 157}, "3": {"size": 157}, "4": {"size": 157}}, "merges": [], "cells": {"A1": {"style": 8, "content": "=_t(\"KPI\")"}, "A2": {"style": 8, "content": "=_t(\"Won leads\")"}, "A3": {"style": 8, "content": "=_t(\"Total leads\")"}, "A4": {"style": 8, "content": "=_t(\"Close rate\")"}, "A5": {"style": 8, "content": "=_t(\"Average deal size\")"}, "A6": {"style": 8, "content": "=_t(\"Revenue\")"}, "A7": {"style": 8, "content": "=_t(\"Total days to win\")"}, "A8": {"style": 8, "content": "=_t(\"Total days to assign\")"}, "A9": {"style": 8, "content": "=_t(\"Days to win\")"}, "A10": {"style": 8, "content": "=_t(\"Days to assign\")"}, "B1": {"style": 8, "content": "=_t(\"Current period\")"}, "B2": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\",\"won_status\",\"won\")"}, "B3": {"style": 7, "content": "=ODOO.PIVOT(1,\"__count\")"}, "B4": {"style": 7, "format": 4, "content": "=IFERROR(B2/B3)"}, "B5": {"style": 7, "content": "=IFERROR(B6/B2)"}, "B6": {"content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"won_status\",\"won\")"}, "B7": {"style": 7, "content": "=ODOO.PIVOT(5,\"day_close\",\"won_status\",\"won\")"}, "B8": {"style": 7, "content": "=ODOO.PIVOT(5,\"day_open\")"}, "B9": {"style": 7, "content": "=IFERROR(B7/B2)"}, "B10": {"style": 7, "content": "=IFERROR(B8/B3)"}, "C1": {"style": 8, "content": "=_t(\"Previous period\")"}, "C2": {"style": 7, "content": "=ODOO.PIVOT(2,\"__count\",\"won_status\",\"won\")"}, "C3": {"style": 7, "content": "=ODOO.PIVOT(2,\"__count\")"}, "C4": {"style": 7, "format": 4, "content": "=IFERROR(C2/C3)"}, "C5": {"style": 7, "content": "=IFERROR(C6/C2)"}, "C6": {"style": 7, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"won_status\",\"won\")"}, "C7": {"style": 7, "content": "=ODOO.PIVOT(6,\"day_close\",\"won_status\",\"won\")"}, "C8": {"style": 7, "content": "=ODOO.PIVOT(6,\"day_open\")"}, "C9": {"style": 7, "content": "=IFERROR(C7/C2)"}, "C10": {"style": 7, "content": "=IFERROR(C8/C3)"}, "D1": {"style": 8, "content": "=_t(\"Current period\")"}, "D2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "D3": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "D4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "D5": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B5)"}, "D6": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B6)"}, "D7": {"style": 7, "content": "=ODOO.PIVOT(5,\"day_close\",\"won_status\",\"won\")"}, "D8": {"style": 7, "content": "=ODOO.PIVOT(5,\"day_open\")"}, "D9": {"style": 7, "content": "=IFERROR(CONCATENATE(ROUND(B9,1),_t(\" days\")))"}, "D10": {"style": 7, "content": "=IFERROR(CONCATENATE(ROUND(B10,1),_t(\" days\")))"}, "E1": {"style": 8, "content": "=_t(\"Previous period\")"}, "E2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C2)"}, "E3": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C3)"}, "E4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C4)"}, "E5": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C5)"}, "E6": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C6)"}, "E7": {"style": 7, "content": "=ODOO.PIVOT(6,\"day_close\",\"won_status\",\"won\")"}, "E8": {"style": 7, "content": "=ODOO.PIVOT(6,\"day_open\")"}, "E9": {"style": 7, "format": 1, "content": "=IFERROR(ROUND(C9,1))"}, "E10": {"style": 7, "format": 1, "content": "=IFERROR(ROUND(C10,1))"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true}, "3": {"fillColor": "#f8f9fa", "textColor": "#741b47"}, "4": {"textColor": "#741b47"}, "5": {"bold": true, "fontSize": 16}, "6": {"bold": true, "align": "right"}, "7": {"fillColor": "#f8f9fa"}, "8": {"bold": true, "fillColor": "#f8f9fa"}}, "formats": {"1": "0", "2": "#,##0", "3": "#,##0.00", "4": "0.00%"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "chartOdooMenusReferences": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": "crm.crm_menu_root", "26d0db83-eced-423a-83ba-143e890a458b": "crm.crm_opportunity_report_menu_lead", "1a364b24-21a0-41fe-8bd3-061efa4f1b1f": "crm.crm_opportunity_report_menu_lead", "070e9913-f280-47fd-99c5-d0675a9e2cc2": "crm.crm_opportunity_report_menu_lead", "9cd65dd7-0333-4fa3-bc21-fa53de78dfbf": "crm.crm_opportunity_report_menu_lead", "8c6ddf6e-2c14-4e32-9499-1b3608f05d1b": "crm.crm_opportunity_report_menu_lead"}, "odooVersion": 4, "lists": {}, "listNextId": 1, "pivots": {"1": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "1", "measures": [{"field": "__count"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "close rate - current", "sortedColumn": null}, "2": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "2", "measures": [{"field": "__count"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "close rate - previous", "sortedColumn": null}, "3": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "3", "measures": [{"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "average deal size - current", "sortedColumn": null}, "4": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "4", "measures": [{"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "average deal size - previous", "sortedColumn": null}, "5": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "5", "measures": [{"field": "day_open"}, {"field": "day_close"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "days to - current", "sortedColumn": null}, "6": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["|", ["active", "=", true], ["active", "=", false]], "id": "6", "measures": [{"field": "day_open"}, {"field": "day_close"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "days to - previous", "sortedColumn": null}, "7": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["country_id", "!=", false]], "id": "7", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["country_id"], "name": "top countries", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "8": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["tag_ids", "!=", false]], "id": "8", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["tag_ids"], "name": "top tags", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "9": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], "&", "&", ["active", "=", false], ["probability", "=", 0], ["lost_reason_id", "!=", false]], "id": "9", "measures": [{"field": "__count"}, {"field": "expected_revenue"}], "model": "crm.lead", "rowGroupBys": ["lost_reason_id"], "name": "top lost reasons", "sortedColumn": {"groupId": [[], []], "measure": "expected_revenue", "order": "desc"}}, "10": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["medium_id", "!=", false]], "id": "10", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["medium_id"], "name": "top medium", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "11": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["campaign_id", "!=", false]], "id": "11", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["campaign_id"], "name": "to campaign", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "12": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["source_id", "!=", false]], "id": "12", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["source_id"], "name": "top source", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "13": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["team_id", "!=", false]], "id": "13", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["team_id"], "name": "top sales team", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "14": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", "|", ["active", "=", true], ["active", "=", false], ["user_id", "!=", false]], "id": "14", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["user_id"], "name": "top salesperson", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}}, "pivotNextId": 15, "globalFilters": [{"id": "77bc36ef-1319-4784-891f-950545059abc", "type": "date", "label": "Period", "defaultValue": "last_year", "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "create_date", "type": "date", "offset": 0}, "2": {"field": "create_date", "type": "date", "offset": -1}, "3": {"field": "create_date", "type": "date", "offset": 0}, "4": {"field": "create_date", "type": "date", "offset": -1}, "5": {"field": "create_date", "type": "date", "offset": 0}, "6": {"field": "create_date", "type": "date", "offset": -1}, "7": {"field": "create_date", "type": "date", "offset": 0}, "8": {"field": "create_date", "type": "date", "offset": 0}, "9": {"field": "create_date", "type": "date", "offset": 0}, "10": {"field": "create_date", "type": "date", "offset": 0}, "11": {"field": "create_date", "type": "date", "offset": 0}, "12": {"field": "create_date", "type": "date", "offset": 0}, "13": {"field": "create_date", "type": "date", "offset": 0}, "14": {"field": "create_date", "type": "date", "offset": 0}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "create_date", "type": "date", "offset": 0}}}, {"id": "dafc928e-1d51-4f33-9975-fc3bb76f5d9d", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "country_id", "type": "many2one"}, "2": {"field": "country_id", "type": "many2one"}, "3": {"field": "country_id", "type": "many2one"}, "4": {"field": "country_id", "type": "many2one"}, "5": {"field": "country_id", "type": "many2one"}, "6": {"field": "country_id", "type": "many2one"}, "7": {"field": "country_id", "type": "many2one"}, "8": {"field": "country_id", "type": "many2one"}, "9": {"field": "country_id", "type": "many2one"}, "10": {"field": "country_id", "type": "many2one"}, "11": {"field": "country_id", "type": "many2one"}, "12": {"field": "country_id", "type": "many2one"}, "13": {"field": "country_id", "type": "many2one"}, "14": {"field": "country_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "country_id", "type": "many2one"}}}, {"id": "53afc2d9-ddfa-445a-9beb-ecfc2e8a90ce", "type": "relation", "label": "Tags", "modelName": "crm.tag", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "tag_ids", "type": "many2many"}, "2": {"field": "tag_ids", "type": "many2many"}, "3": {"field": "tag_ids", "type": "many2many"}, "4": {"field": "tag_ids", "type": "many2many"}, "5": {"field": "tag_ids", "type": "many2many"}, "6": {"field": "tag_ids", "type": "many2many"}, "7": {"field": "tag_ids", "type": "many2many"}, "8": {"field": "tag_ids", "type": "many2many"}, "9": {"field": "tag_ids", "type": "many2many"}, "10": {"field": "tag_ids", "type": "many2many"}, "11": {"field": "tag_ids", "type": "many2many"}, "12": {"field": "tag_ids", "type": "many2many"}, "13": {"field": "tag_ids", "type": "many2many"}, "14": {"field": "tag_ids", "type": "many2many"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "tag_ids", "type": "many2many"}}}, {"id": "43ab094a-2100-4110-a618-7b2513d38901", "type": "relation", "label": "Lost Reason", "modelName": "crm.lost.reason", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "lost_reason_id", "type": "many2one"}, "2": {"field": "lost_reason_id", "type": "many2one"}, "3": {"field": "lost_reason_id", "type": "many2one"}, "4": {"field": "lost_reason_id", "type": "many2one"}, "5": {"field": "lost_reason_id", "type": "many2one"}, "6": {"field": "lost_reason_id", "type": "many2one"}, "7": {"field": "lost_reason_id", "type": "many2one"}, "8": {"field": "lost_reason_id", "type": "many2one"}, "9": {"field": "lost_reason_id", "type": "many2one"}, "10": {"field": "lost_reason_id", "type": "many2one"}, "11": {"field": "lost_reason_id", "type": "many2one"}, "12": {"field": "lost_reason_id", "type": "many2one"}, "13": {"field": "lost_reason_id", "type": "many2one"}, "14": {"field": "lost_reason_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "lost_reason_id", "type": "many2one"}}}, {"id": "be8c3d56-d33f-4878-a33f-63271d52c3c5", "type": "relation", "label": "Medium", "modelName": "utm.medium", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "medium_id", "type": "many2one"}, "2": {"field": "medium_id", "type": "many2one"}, "3": {"field": "medium_id", "type": "many2one"}, "4": {"field": "medium_id", "type": "many2one"}, "5": {"field": "medium_id", "type": "many2one"}, "6": {"field": "medium_id", "type": "many2one"}, "7": {"field": "medium_id", "type": "many2one"}, "8": {"field": "medium_id", "type": "many2one"}, "9": {"field": "medium_id", "type": "many2one"}, "10": {"field": "medium_id", "type": "many2one"}, "11": {"field": "medium_id", "type": "many2one"}, "12": {"field": "medium_id", "type": "many2one"}, "13": {"field": "medium_id", "type": "many2one"}, "14": {"field": "medium_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "medium_id", "type": "many2one"}}}, {"id": "79d2d4b4-b905-4622-9923-5702e6a8b8bf", "type": "relation", "label": "Campaign", "modelName": "utm.campaign", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "campaign_id", "type": "many2one"}, "2": {"field": "campaign_id", "type": "many2one"}, "3": {"field": "campaign_id", "type": "many2one"}, "4": {"field": "campaign_id", "type": "many2one"}, "5": {"field": "campaign_id", "type": "many2one"}, "6": {"field": "campaign_id", "type": "many2one"}, "7": {"field": "campaign_id", "type": "many2one"}, "8": {"field": "campaign_id", "type": "many2one"}, "9": {"field": "campaign_id", "type": "many2one"}, "10": {"field": "campaign_id", "type": "many2one"}, "11": {"field": "campaign_id", "type": "many2one"}, "12": {"field": "campaign_id", "type": "many2one"}, "13": {"field": "campaign_id", "type": "many2one"}, "14": {"field": "campaign_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "campaign_id", "type": "many2one"}}}, {"id": "d5c6c38d-d612-4273-8cb0-bbe37351e7a9", "type": "relation", "label": "Source", "modelName": "utm.source", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "source_id", "type": "many2one"}, "2": {"field": "source_id", "type": "many2one"}, "3": {"field": "source_id", "type": "many2one"}, "4": {"field": "source_id", "type": "many2one"}, "5": {"field": "source_id", "type": "many2one"}, "6": {"field": "source_id", "type": "many2one"}, "7": {"field": "source_id", "type": "many2one"}, "8": {"field": "source_id", "type": "many2one"}, "9": {"field": "source_id", "type": "many2one"}, "10": {"field": "source_id", "type": "many2one"}, "11": {"field": "source_id", "type": "many2one"}, "12": {"field": "source_id", "type": "many2one"}, "13": {"field": "source_id", "type": "many2one"}, "14": {"field": "source_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "source_id", "type": "many2one"}}}, {"id": "e06a6095-d07b-4b2f-b737-a2f5b3b4ab7c", "type": "relation", "label": "Sales Team", "modelName": "crm.team", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "team_id", "type": "many2one"}, "2": {"field": "team_id", "type": "many2one"}, "3": {"field": "team_id", "type": "many2one"}, "4": {"field": "team_id", "type": "many2one"}, "5": {"field": "team_id", "type": "many2one"}, "6": {"field": "team_id", "type": "many2one"}, "7": {"field": "team_id", "type": "many2one"}, "8": {"field": "team_id", "type": "many2one"}, "9": {"field": "team_id", "type": "many2one"}, "10": {"field": "team_id", "type": "many2one"}, "11": {"field": "team_id", "type": "many2one"}, "12": {"field": "team_id", "type": "many2one"}, "13": {"field": "team_id", "type": "many2one"}, "14": {"field": "team_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "team_id", "type": "many2one"}}}, {"id": "e6c83660-136f-4665-a809-ba4d1f735de4", "type": "relation", "label": "Salesperson", "modelName": "res.users", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "user_id", "type": "many2one"}, "2": {"field": "user_id", "type": "many2one"}, "3": {"field": "user_id", "type": "many2one"}, "4": {"field": "user_id", "type": "many2one"}, "5": {"field": "user_id", "type": "many2one"}, "6": {"field": "user_id", "type": "many2one"}, "7": {"field": "user_id", "type": "many2one"}, "8": {"field": "user_id", "type": "many2one"}, "9": {"field": "user_id", "type": "many2one"}, "10": {"field": "user_id", "type": "many2one"}, "11": {"field": "user_id", "type": "many2one"}, "12": {"field": "user_id", "type": "many2one"}, "13": {"field": "user_id", "type": "many2one"}, "14": {"field": "user_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"b9b862dd-1d57-47d9-b46c-ffcf4f8be638": {"field": "user_id", "type": "many2one"}}}]}