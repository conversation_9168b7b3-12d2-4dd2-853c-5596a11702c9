/*!
 * Signature Pad v4.1.4 | https://github.com/szimek/signature_pad
 * (c) 2022 Szy<PERSON>ak | Released under the MIT license
 */

/*!
 * dist/inputmask
 * https://github.com/RobinHerbots/Inputmask
 * Copyright (c) 2010 - 2023 Robin Herbots
 * Licensed under the MIT license
 * Version: 5.0.8
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2021 <PERSON>
 * MIT license
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2022 <PERSON>
 * MIT licensed
 */

/*! @formio/choices.js v10.2.1 | © 2024 <PERSON> | https://github.com/jshjohnson/Choices#readme */

/*! @license DOMPurify 3.0.5 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.5/LICENSE */

/*! Native Promise Only
    v0.8.1 (c) Kyle Simpson
    MIT License: http://getify.mit-license.org
*/

/*! formiojs v4.20.0 | https://unpkg.com/formiojs@4.20.0/LICENSE.txt */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */

/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */

//! Copyright (c) JS Foundation and other contributors

//! github.com/moment/moment-timezone

//! license : MIT

//! moment-timezone.js

//! moment.js

//! version : 0.5.40
