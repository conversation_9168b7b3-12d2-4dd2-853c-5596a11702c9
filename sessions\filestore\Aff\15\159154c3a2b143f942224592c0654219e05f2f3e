
/* /web/static/lib/qunit/qunit-2.9.1.css */
 #qunit-tests, #qunit-header, #qunit-banner, #qunit-testrunner-toolbar, #qunit-filteredTest, #qunit-userAgent, #qunit-testresult{font-family: "Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial, sans-serif;}#qunit-testrunner-toolbar, #qunit-filteredTest, #qunit-userAgent, #qunit-testresult, #qunit-tests li{font-size: small;}#qunit-tests{font-size: smaller;}#qunit-tests, #qunit-header, #qunit-banner, #qunit-filteredTest, #qunit-userAgent, #qunit-testresult, #qunit-modulefilter{margin: 0; padding: 0;}#qunit-header{padding: 0.5em 0 0.5em 1em; color: #8699A4; background-color: #0D3349; font-size: 1.5em; line-height: 1em; font-weight: 400; border-radius: 5px 5px 0 0;}#qunit-header a{text-decoration: none; color: #C2CCD1;}#qunit-header a:hover, #qunit-header a:focus{color: #FFF;}#qunit-banner{height: 5px;}#qunit-filteredTest{padding: 0.5em 1em 0.5em 1em; color: #366097; background-color: #F4FF77;}#qunit-userAgent{padding: 0.5em 1em 0.5em 1em; color: #FFF; background-color: #2B81AF; text-shadow: rgba(0, 0, 0, 0.5) 2px 2px 1px;}#qunit-testrunner-toolbar{padding: 0.5em 1em 0.5em 1em; color: #5E740B; background-color: #EEE;}#qunit-testrunner-toolbar .clearfix{height: 0; clear: both;}#qunit-testrunner-toolbar label{display: inline-block;}#qunit-testrunner-toolbar input[type=checkbox], #qunit-testrunner-toolbar input[type=radio]{margin: 3px; vertical-align: -2px;}#qunit-testrunner-toolbar input[type=text]{box-sizing: border-box; height: 1.6em;}.qunit-url-config, .qunit-filter, #qunit-modulefilter{display: inline-block; line-height: 2.1em;}.qunit-filter, #qunit-modulefilter{float: right; position: relative; margin-left: 1em;}.qunit-url-config label{margin-right: 0.5em;}#qunit-modulefilter-search{box-sizing: border-box; width: 400px;}#qunit-modulefilter-search-container:after{position: absolute; right: 0.3em; content: "\25bc"; color: black;}#qunit-modulefilter-dropdown{box-sizing: border-box; width: 400px; position: absolute; right: 0; top: 50%; margin-top: 0.8em; border: 1px solid #D3D3D3; border-top: none; border-radius: 0 0 .25em .25em; color: #000; background-color: #F5F5F5; z-index: 99;}#qunit-modulefilter-dropdown a{color: inherit; text-decoration: none;}#qunit-modulefilter-dropdown .clickable.checked{font-weight: bold; color: #000; background-color: #D2E0E6;}#qunit-modulefilter-dropdown .clickable:hover{color: #FFF; background-color: #0D3349;}#qunit-modulefilter-actions{display: block; overflow: auto; font: smaller/1.5em sans-serif;}#qunit-modulefilter-dropdown #qunit-modulefilter-actions > *{box-sizing: border-box; max-height: 2.8em; display: block; padding: 0.4em;}#qunit-modulefilter-dropdown #qunit-modulefilter-actions > button{float: right; font: inherit;}#qunit-modulefilter-dropdown #qunit-modulefilter-actions > :last-child{padding-left: 3px;}#qunit-modulefilter-dropdown-list{max-height: 200px; overflow-y: auto; margin: 0; border-top: 2px groove threedhighlight; padding: 0.4em 0 0; font: smaller/1.5em sans-serif;}#qunit-modulefilter-dropdown-list li{white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}#qunit-modulefilter-dropdown-list .clickable{display: block; padding-left: 0.15em;}#qunit-tests{list-style-position: inside;}#qunit-tests li{padding: 0.4em 1em 0.4em 1em; border-bottom: 1px solid #FFF; list-style-position: inside;}#qunit-tests > li{display: none;}#qunit-tests li.running, #qunit-tests li.pass, #qunit-tests li.fail, #qunit-tests li.skipped, #qunit-tests li.aborted{display: list-item;}#qunit-tests.hidepass{position: relative;}#qunit-tests.hidepass li.running, #qunit-tests.hidepass li.pass:not(.todo){visibility: hidden; position: absolute; width: 0; height: 0; padding: 0; border: 0; margin: 0;}#qunit-tests li strong{cursor: pointer;}#qunit-tests li.skipped strong{cursor: default;}#qunit-tests li a{padding: 0.5em; color: #C2CCD1; text-decoration: none;}#qunit-tests li p a{padding: 0.25em; color: #6B6464;}#qunit-tests li a:hover, #qunit-tests li a:focus{color: #000;}#qunit-tests li .runtime{float: right; font-size: smaller;}.qunit-assert-list{margin-top: 0.5em; padding: 0.5em; background-color: #FFF; border-radius: 5px;}.qunit-source{margin: 0.6em 0 0.3em;}.qunit-collapsed{display: none;}#qunit-tests table{border-collapse: collapse; margin-top: 0.2em;}#qunit-tests th{text-align: right; vertical-align: top; padding: 0 0.5em 0 0;}#qunit-tests td{vertical-align: top;}#qunit-tests pre{margin: 0; white-space: pre-wrap; word-wrap: break-word;}#qunit-tests del{color: #374E0C; background-color: #E0F2BE; text-decoration: none;}#qunit-tests ins{color: #500; background-color: #FFCACA; text-decoration: none;}#qunit-tests b.counts{color: #000;}#qunit-tests b.passed{color: #5E740B;}#qunit-tests b.failed{color: #710909;}#qunit-tests li li{padding: 5px; background-color: #FFF; border-bottom: none; list-style-position: inside;}#qunit-tests li li.pass{color: #3C510C; background-color: #FFF; border-left: 10px solid #C6E746;}#qunit-tests .pass{color: #528CE0; background-color: #D2E0E6;}#qunit-tests .pass .test-name{color: #366097;}#qunit-tests .pass .test-actual, #qunit-tests .pass .test-expected{color: #999;}#qunit-banner.qunit-pass{background-color: #C6E746;}#qunit-tests li li.fail{color: #710909; background-color: #FFF; border-left: 10px solid #EE5757; white-space: pre;}#qunit-tests > li:last-child{border-radius: 0 0 5px 5px;}#qunit-tests .fail{color: #000; background-color: #EE5757;}#qunit-tests .fail .test-name, #qunit-tests .fail .module-name{color: #000;}#qunit-tests .fail .test-actual{color: #EE5757;}#qunit-tests .fail .test-expected{color: #008000;}#qunit-banner.qunit-fail{background-color: #EE5757;}#qunit-tests .aborted{color: #000; background-color: orange;}#qunit-tests .skipped{background-color: #EBECE9;}#qunit-tests .qunit-todo-label, #qunit-tests .qunit-skipped-label{background-color: #F4FF77; display: inline-block; font-style: normal; color: #366097; line-height: 1.8em; padding: 0 0.5em; margin: -0.4em 0.4em -0.4em 0;}#qunit-tests .qunit-todo-label{background-color: #EEE;}#qunit-testresult{color: #2B81AF; background-color: #D2E0E6; border-bottom: 1px solid #FFF;}#qunit-testresult .clearfix{height: 0; clear: both;}#qunit-testresult .module-name{font-weight: 700;}#qunit-testresult-display{padding: 0.5em 1em 0.5em 1em; width: 85%; float:left;}#qunit-testresult-controls{padding: 0.5em 1em 0.5em 1em; width: 10%; float:left;}#qunit-fixture{position: absolute; top: -10000px; left: -10000px; width: 1000px; height: 1000px;}

/* /web/static/lib/fullcalendar/core/main.css */
 .fc{direction: ltr; text-align: left;}.fc-rtl{text-align: right;}body .fc{font-size: 1em;}.fc-highlight{background: #bce8f1; opacity: 0.3;}.fc-bgevent{background: #8fdf82; opacity: 0.3;}.fc-nonbusiness{background: #d7d7d7;}.fc-popover{position: absolute; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);}.fc-popover .fc-header{display: flex; flex-direction: row; justify-content: space-between; align-items: center; padding: 2px 4px;}.fc-rtl .fc-popover .fc-header{flex-direction: row-reverse;}.fc-popover .fc-header .fc-title{margin: 0 2px;}.fc-popover .fc-header .fc-close{cursor: pointer; opacity: 0.65; font-size: 1.1em;}.fc-divider{border-style: solid; border-width: 1px;}hr.fc-divider{height: 0; margin: 0; padding: 0 0 2px; border-width: 1px 0;}.fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-mirror-skeleton{position: absolute; top: 0; left: 0; right: 0;}.fc-bg{bottom: 0;}.fc-bg table{height: 100%;}.fc table{width: 100%; box-sizing: border-box; table-layout: fixed; border-collapse: collapse; border-spacing: 0; font-size: 1em;}.fc th{text-align: center;}.fc th, .fc td{border-style: solid; border-width: 1px; padding: 0; vertical-align: top;}.fc td.fc-today{border-style: double;}a[data-goto]{cursor: pointer;}a[data-goto]:hover{text-decoration: underline;}.fc .fc-row{border-style: solid; border-width: 0;}.fc-row table{border-left: 0 hidden transparent; border-right: 0 hidden transparent; border-bottom: 0 hidden transparent;}.fc-row:first-child table{border-top: 0 hidden transparent;}.fc-row{position: relative;}.fc-row .fc-bg{z-index: 1;}.fc-row .fc-bgevent-skeleton, .fc-row .fc-highlight-skeleton{bottom: 0;}.fc-row .fc-bgevent-skeleton table, .fc-row .fc-highlight-skeleton table{height: 100%;}.fc-row .fc-highlight-skeleton td, .fc-row .fc-bgevent-skeleton td{border-color: transparent;}.fc-row .fc-bgevent-skeleton{z-index: 2;}.fc-row .fc-highlight-skeleton{z-index: 3;}.fc-row .fc-content-skeleton{position: relative; z-index: 4; padding-bottom: 2px;}.fc-row .fc-mirror-skeleton{z-index: 5;}.fc .fc-row .fc-content-skeleton table, .fc .fc-row .fc-content-skeleton td, .fc .fc-row .fc-mirror-skeleton td{background: none; border-color: transparent;}.fc-row .fc-content-skeleton td, .fc-row .fc-mirror-skeleton td{border-bottom: 0;}.fc-row .fc-content-skeleton tbody td, .fc-row .fc-mirror-skeleton tbody td{border-top: 0;}.fc-scroller{-webkit-overflow-scrolling: touch;}.fc-scroller > .fc-day-grid, .fc-scroller > .fc-time-grid{position: relative; width: 100%;}.fc-event{position: relative; display: block; font-size: 0.85em; line-height: 1.4; border-radius: 3px; border: 1px solid #3788d8;}.fc-event, .fc-event-dot{background-color: #3788d8;}.fc-event, .fc-event:hover{color: #fff; text-decoration: none;}.fc-event[href], .fc-event.fc-draggable{cursor: pointer;}.fc-not-allowed, .fc-not-allowed .fc-event{cursor: not-allowed;}.fc-event .fc-content{position: relative; z-index: 2;}.fc-event .fc-resizer{position: absolute; z-index: 4;}.fc-event .fc-resizer{display: none;}.fc-event.fc-allow-mouse-resize .fc-resizer, .fc-event.fc-selected .fc-resizer{display: block;}.fc-event.fc-selected .fc-resizer:before{content: ""; position: absolute; z-index: 9999; top: 50%; left: 50%; width: 40px; height: 40px; margin-left: -20px; margin-top: -20px;}.fc-event.fc-selected{z-index: 9999 !important; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);}.fc-event.fc-selected:after{content: ""; position: absolute; z-index: 1; top: -1px; right: -1px; bottom: -1px; left: -1px; background: #000; opacity: 0.25;}.fc-event.fc-dragging.fc-selected{box-shadow: 0 2px 7px rgba(0, 0, 0, 0.3);}.fc-event.fc-dragging:not(.fc-selected){opacity: 0.75;}.fc-h-event.fc-selected:before{content: ""; position: absolute; z-index: 3; top: -10px; bottom: -10px; left: 0; right: 0;}.fc-ltr .fc-h-event.fc-not-start, .fc-rtl .fc-h-event.fc-not-end{margin-left: 0; border-left-width: 0; padding-left: 1px; border-top-left-radius: 0; border-bottom-left-radius: 0;}.fc-ltr .fc-h-event.fc-not-end, .fc-rtl .fc-h-event.fc-not-start{margin-right: 0; border-right-width: 0; padding-right: 1px; border-top-right-radius: 0; border-bottom-right-radius: 0;}.fc-ltr .fc-h-event .fc-start-resizer, .fc-rtl .fc-h-event .fc-end-resizer{cursor: w-resize; left: -1px;}.fc-ltr .fc-h-event .fc-end-resizer, .fc-rtl .fc-h-event .fc-start-resizer{cursor: e-resize; right: -1px;}.fc-h-event.fc-allow-mouse-resize .fc-resizer{width: 7px; top: -1px; bottom: -1px;}.fc-h-event.fc-selected .fc-resizer{border-radius: 4px; border-width: 1px; width: 6px; height: 6px; border-style: solid; border-color: inherit; background: #fff; top: 50%; margin-top: -4px;}.fc-ltr .fc-h-event.fc-selected .fc-start-resizer, .fc-rtl .fc-h-event.fc-selected .fc-end-resizer{margin-left: -4px;}.fc-ltr .fc-h-event.fc-selected .fc-end-resizer, .fc-rtl .fc-h-event.fc-selected .fc-start-resizer{margin-right: -4px;}.fc-day-grid-event{margin: 1px 2px 0; padding: 0 1px;}tr:first-child > td > .fc-day-grid-event{margin-top: 2px;}.fc-mirror-skeleton tr:first-child > td > .fc-day-grid-event{margin-top: 0;}.fc-day-grid-event .fc-content{white-space: nowrap; overflow: hidden;}.fc-day-grid-event .fc-time{font-weight: bold;}.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer, .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer{margin-left: -2px;}.fc-ltr .fc-day-grid-event.fc-allow-mouse-resize .fc-end-resizer, .fc-rtl .fc-day-grid-event.fc-allow-mouse-resize .fc-start-resizer{margin-right: -2px;}a.fc-more{margin: 1px 3px; font-size: 0.85em; cursor: pointer; text-decoration: none;}a.fc-more:hover{text-decoration: underline;}.fc-limited{display: none;}.fc-day-grid .fc-row{z-index: 1;}.fc-more-popover{z-index: 2; width: 220px;}.fc-more-popover .fc-event-container{padding: 10px;}.fc-now-indicator{position: absolute; border: 0 solid red;}.fc-unselectable{-webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -webkit-touch-callout: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row, .fc-unthemed .fc-content, .fc-unthemed .fc-popover, .fc-unthemed .fc-list-view, .fc-unthemed .fc-list-heading td{border-color: #ddd;}.fc-unthemed .fc-popover{background-color: #fff;}.fc-unthemed .fc-divider, .fc-unthemed .fc-popover .fc-header, .fc-unthemed .fc-list-heading td{background: #eee;}.fc-unthemed td.fc-today{background: #fcf8e3;}.fc-unthemed .fc-disabled-day{background: #d7d7d7; opacity: 0.3;}@font-face{font-family: "fcicons"; src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype"); font-weight: normal; font-style: normal;}.fc-icon{font-family: "fcicons" !important; speak: none; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fc-icon-chevron-left:before{content: "";}.fc-icon-chevron-right:before{content: "";}.fc-icon-chevrons-left:before{content: "";}.fc-icon-chevrons-right:before{content: "";}.fc-icon-minus-square:before{content: "";}.fc-icon-plus-square:before{content: "";}.fc-icon-x:before{content: "";}.fc-icon{display: inline-block; width: 1em; height: 1em; text-align: center;}.fc-button{border-radius: 0; overflow: visible; text-transform: none; margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}.fc-button:focus{outline: 1px dotted; outline: 5px auto -webkit-focus-ring-color;}.fc-button{-webkit-appearance: button;}.fc-button:not(:disabled){cursor: pointer;}.fc-button::-moz-focus-inner{padding: 0; border-style: none;}.fc-button{display: inline-block; font-weight: 400; color: #212529; text-align: center; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: transparent; border: 1px solid transparent; padding: 0.4em 0.65em; font-size: 1em; line-height: 1.5; border-radius: 0.25em;}.fc-button:hover{color: #212529; text-decoration: none;}.fc-button:focus{outline: 0; -webkit-box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25); box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);}.fc-button:disabled{opacity: 0.65;}.fc-button-primary{color: #fff; background-color: #2C3E50; border-color: #2C3E50;}.fc-button-primary:hover{color: #fff; background-color: #1e2b37; border-color: #1a252f;}.fc-button-primary:focus{-webkit-box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5); box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);}.fc-button-primary:disabled{color: #fff; background-color: #2C3E50; border-color: #2C3E50;}.fc-button-primary:not(:disabled):active, .fc-button-primary:not(:disabled).fc-button-active{color: #fff; background-color: #1a252f; border-color: #151e27;}.fc-button-primary:not(:disabled):active:focus, .fc-button-primary:not(:disabled).fc-button-active:focus{-webkit-box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5); box-shadow: 0 0 0 0.2rem rgba(76, 91, 106, 0.5);}.fc-button .fc-icon{vertical-align: middle; font-size: 1.5em;}.fc-button-group{position: relative; display: -webkit-inline-box; display: -ms-inline-flexbox; display: inline-flex; vertical-align: middle;}.fc-button-group > .fc-button{position: relative; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto;}.fc-button-group > .fc-button:hover{z-index: 1;}.fc-button-group > .fc-button:focus, .fc-button-group > .fc-button:active, .fc-button-group > .fc-button.fc-button-active{z-index: 1;}.fc-button-group > .fc-button:not(:first-child){margin-left: -1px;}.fc-button-group > .fc-button:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.fc-button-group > .fc-button:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.fc-unthemed .fc-popover{border-width: 1px; border-style: solid;}.fc-unthemed .fc-list-item:hover td{background-color: #f5f5f5;}.fc-toolbar{display: flex; justify-content: space-between; align-items: center;}.fc-toolbar.fc-header-toolbar{margin-bottom: 1.5em;}.fc-toolbar.fc-footer-toolbar{margin-top: 1.5em;}.fc-toolbar > * > :not(:first-child){margin-left: 0.75em;}.fc-toolbar h2{font-size: 1.75em; margin: 0;}.fc-view-container{position: relative;}.fc-view-container *, .fc-view-container *:before, .fc-view-container *:after{-webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box;}.fc-view, .fc-view > table{position: relative; z-index: 1;}@media print{.fc{max-width: 100% !important;}.fc-event{background: #fff !important; color: #000 !important; page-break-inside: avoid;}.fc-event .fc-resizer{display: none;}.fc th, .fc td, .fc hr, .fc thead, .fc tbody, .fc-row{border-color: #ccc !important; background: #fff !important;}.fc-bg, .fc-bgevent-skeleton, .fc-highlight-skeleton, .fc-mirror-skeleton, .fc-bgevent-container, .fc-business-container, .fc-highlight-container, .fc-mirror-container{display: none;}.fc tbody .fc-row{height: auto !important; min-height: 0 !important;}.fc tbody .fc-row .fc-content-skeleton{position: static; padding-bottom: 0 !important;}.fc tbody .fc-row .fc-content-skeleton tbody tr:last-child td{padding-bottom: 1em;}.fc tbody .fc-row .fc-content-skeleton table{height: 1em;}.fc-more-cell, .fc-more{display: none !important;}.fc tr.fc-limited{display: table-row !important;}.fc td.fc-limited{display: table-cell !important;}.fc-popover{display: none;}.fc-time-grid{min-height: 0 !important;}.fc-timeGrid-view .fc-axis{display: none;}.fc-slats, .fc-time-grid hr{display: none !important;}.fc-time-grid .fc-content-skeleton{position: static;}.fc-time-grid .fc-content-skeleton table{height: 4em;}.fc-time-grid .fc-event-container{margin: 0 !important;}.fc-time-grid .fc-event{position: static !important; margin: 3px 2px !important;}.fc-time-grid .fc-event.fc-not-end{border-bottom-width: 1px !important;}.fc-time-grid .fc-event.fc-not-end:after{content: "...";}.fc-time-grid .fc-event.fc-not-start{border-top-width: 1px !important;}.fc-time-grid .fc-event.fc-not-start:before{content: "...";}.fc-time-grid .fc-event .fc-time{white-space: normal !important;}.fc-time-grid .fc-event .fc-time span{display: none;}.fc-time-grid .fc-event .fc-time:after{content: attr(data-full);}.fc-scroller, .fc-day-grid-container, .fc-time-grid-container{overflow: visible !important; height: auto !important;}.fc-row{border: 0 !important; margin: 0 !important;}.fc-button-group, .fc button{display: none;}}

/* /web/static/lib/fullcalendar/daygrid/main.css */
 .fc-dayGridWeek-view .fc-content-skeleton, .fc-dayGridDay-view .fc-content-skeleton{padding-bottom: 1em;}.fc-dayGrid-view .fc-body .fc-row{min-height: 4em;}.fc-row.fc-rigid{overflow: hidden;}.fc-row.fc-rigid .fc-content-skeleton{position: absolute; top: 0; left: 0; right: 0;}.fc-day-top.fc-other-month{opacity: 0.3;}.fc-dayGrid-view .fc-week-number, .fc-dayGrid-view .fc-day-number{padding: 2px;}.fc-dayGrid-view th.fc-week-number, .fc-dayGrid-view th.fc-day-number{padding: 0 2px;}.fc-ltr .fc-dayGrid-view .fc-day-top .fc-day-number{float: right;}.fc-rtl .fc-dayGrid-view .fc-day-top .fc-day-number{float: left;}.fc-ltr .fc-dayGrid-view .fc-day-top .fc-week-number{float: left; border-radius: 0 0 3px 0;}.fc-rtl .fc-dayGrid-view .fc-day-top .fc-week-number{float: right; border-radius: 0 0 0 3px;}.fc-dayGrid-view .fc-day-top .fc-week-number{min-width: 1.5em; text-align: center; background-color: #f2f2f2; color: #808080;}.fc-dayGrid-view td.fc-week-number{text-align: center;}.fc-dayGrid-view td.fc-week-number > *{display: inline-block; min-width: 1.25em;}

/* /web/static/lib/fullcalendar/timegrid/main.css */
 .fc-timeGrid-view .fc-day-grid{position: relative; z-index: 2;}.fc-timeGrid-view .fc-day-grid .fc-row{min-height: 3em;}.fc-timeGrid-view .fc-day-grid .fc-row .fc-content-skeleton{padding-bottom: 1em;}.fc .fc-axis{vertical-align: middle; padding: 0 4px; white-space: nowrap;}.fc-ltr .fc-axis{text-align: right;}.fc-rtl .fc-axis{text-align: left;}.fc-time-grid-container, .fc-time-grid{position: relative; z-index: 1;}.fc-time-grid{min-height: 100%;}.fc-time-grid table{border: 0 hidden transparent;}.fc-time-grid > .fc-bg{z-index: 1;}.fc-time-grid .fc-slats, .fc-time-grid > hr{position: relative; z-index: 2;}.fc-time-grid .fc-content-col{position: relative;}.fc-time-grid .fc-content-skeleton{position: absolute; z-index: 3; top: 0; left: 0; right: 0;}.fc-time-grid .fc-business-container{position: relative; z-index: 1;}.fc-time-grid .fc-bgevent-container{position: relative; z-index: 2;}.fc-time-grid .fc-highlight-container{position: relative; z-index: 3;}.fc-time-grid .fc-event-container{position: relative; z-index: 4;}.fc-time-grid .fc-now-indicator-line{z-index: 5;}.fc-time-grid .fc-mirror-container{position: relative; z-index: 6;}.fc-time-grid .fc-slats td{height: 1.5em; border-bottom: 0;}.fc-time-grid .fc-slats .fc-minor td{border-top-style: dotted;}.fc-time-grid .fc-highlight-container{position: relative;}.fc-time-grid .fc-highlight{position: absolute; left: 0; right: 0;}.fc-ltr .fc-time-grid .fc-event-container{margin: 0 2.5% 0 2px;}.fc-rtl .fc-time-grid .fc-event-container{margin: 0 2px 0 2.5%;}.fc-time-grid .fc-event, .fc-time-grid .fc-bgevent{position: absolute; z-index: 1;}.fc-time-grid .fc-bgevent{left: 0; right: 0;}.fc-time-grid-event{margin-bottom: 1px;}.fc-time-grid-event-inset{-webkit-box-shadow: 0px 0px 0px 1px #fff; box-shadow: 0px 0px 0px 1px #fff;}.fc-time-grid-event.fc-not-start{border-top-width: 0; padding-top: 1px; border-top-left-radius: 0; border-top-right-radius: 0;}.fc-time-grid-event.fc-not-end{border-bottom-width: 0; padding-bottom: 1px; border-bottom-left-radius: 0; border-bottom-right-radius: 0;}.fc-time-grid-event .fc-content{overflow: hidden; max-height: 100%;}.fc-time-grid-event .fc-time, .fc-time-grid-event .fc-title{padding: 0 1px;}.fc-time-grid-event .fc-time{font-size: 0.85em; white-space: nowrap;}.fc-time-grid-event.fc-short .fc-content{white-space: nowrap;}.fc-time-grid-event.fc-short .fc-time, .fc-time-grid-event.fc-short .fc-title{display: inline-block; vertical-align: top;}.fc-time-grid-event.fc-short .fc-time span{display: none;}.fc-time-grid-event.fc-short .fc-time:before{content: attr(data-start);}.fc-time-grid-event.fc-short .fc-time:after{content: " - ";}.fc-time-grid-event.fc-short .fc-title{font-size: 0.85em; padding: 0;}.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer{left: 0; right: 0; bottom: 0; height: 8px; overflow: hidden; line-height: 8px; font-size: 11px; font-family: monospace; text-align: center; cursor: s-resize;}.fc-time-grid-event.fc-allow-mouse-resize .fc-resizer:after{content: "=";}.fc-time-grid-event.fc-selected .fc-resizer{border-radius: 5px; border-width: 1px; width: 8px; height: 8px; border-style: solid; border-color: inherit; background: #fff; left: 50%; margin-left: -5px; bottom: -5px;}.fc-time-grid .fc-now-indicator-line{border-top-width: 1px; left: 0; right: 0;}.fc-time-grid .fc-now-indicator-arrow{margin-top: -5px;}.fc-ltr .fc-time-grid .fc-now-indicator-arrow{left: 0; border-width: 5px 0 5px 6px; border-top-color: transparent; border-bottom-color: transparent;}.fc-rtl .fc-time-grid .fc-now-indicator-arrow{right: 0; border-width: 5px 6px 5px 0; border-top-color: transparent; border-bottom-color: transparent;}

/* /web/static/lib/fullcalendar/list/main.css */
 .fc-event-dot{display: inline-block; width: 10px; height: 10px; border-radius: 5px;}.fc-rtl .fc-list-view{direction: rtl;}.fc-list-view{border-width: 1px; border-style: solid;}.fc .fc-list-table{table-layout: auto;}.fc-list-table td{border-width: 1px 0 0; padding: 8px 14px;}.fc-list-table tr:first-child td{border-top-width: 0;}.fc-list-heading{border-bottom-width: 1px;}.fc-list-heading td{font-weight: bold;}.fc-ltr .fc-list-heading-main{float: left;}.fc-ltr .fc-list-heading-alt{float: right;}.fc-rtl .fc-list-heading-main{float: right;}.fc-rtl .fc-list-heading-alt{float: left;}.fc-list-item.fc-has-url{cursor: pointer;}.fc-list-item-marker, .fc-list-item-time{white-space: nowrap; width: 1px;}.fc-ltr .fc-list-item-marker{padding-right: 0;}.fc-rtl .fc-list-item-marker{padding-left: 0;}.fc-list-item-title a{text-decoration: none; color: inherit;}.fc-list-item-title a[href]:hover{text-decoration: underline;}.fc-list-empty-wrap2{position: absolute; top: 0; left: 0; right: 0; bottom: 0;}.fc-list-empty-wrap1{width: 100%; height: 100%; display: table;}.fc-list-empty{display: table-cell; vertical-align: middle; text-align: center;}.fc-unthemed .fc-list-empty{background-color: #eee;}

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 

/* /web/static/src/legacy/scss/utils.scss */
 

/* /web_enterprise/static/src/scss/primary_variables.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */
 

/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */
 

/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /base/static/src/scss/onboarding.variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /documents/static/src/scss/documents.variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /web_enterprise/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web_enterprise/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/bs_mixins_overrides_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden_backend.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
