)]}'
{"version": 3, "sources": ["/web_enterprise/static/src/main.js", "/web/static/src/start.js", "/web/static/src/legacy/legacy_setup.js", "/mail/static/src/main.js"], "mappings": "AAAA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACZA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AC5EA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACzDA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["/** @odoo-module **/\n\nimport { startWebClient } from \"@web/start\";\nimport { WebClientEnterprise } from \"./webclient/webclient\";\n\n/**\n * This file starts the enterprise webclient. In the manifest, it replaces\n * the community main.js to load a different webclient class\n * (WebClientEnterprise instead of WebClient)\n */\n\nstartWebClient(WebClientEnterprise);\n", "/** @odoo-module **/\n\nimport { makeEnv, startServices } from \"./env\";\nimport { legacySetupProm } from \"./legacy/legacy_setup\";\nimport { mapLegacyEnvToWowlEnv } from \"./legacy/utils\";\nimport { localization } from \"@web/core/l10n/localization\";\nimport { session } from \"@web/session\";\nimport { renderToString } from \"./core/utils/render\";\nimport { setLoadXmlDefaultApp, templates } from \"@web/core/assets\";\nimport { hasTouch } from \"@web/core/browser/feature_detection\";\n\nimport { App, whenReady } from \"@odoo/owl\";\n\n/**\n * Function to start a webclient.\n * It is used both in community and enterprise in main.js.\n * It's meant to be webclient flexible so we can have a subclass of\n * webclient in enterprise with added features.\n *\n * @param {Component} Webclient\n */\nexport async function startWebClient(Webclient) {\n    odoo.info = {\n        db: session.db,\n        server_version: session.server_version,\n        server_version_info: session.server_version_info,\n        isEnterprise: session.server_version_info.slice(-1)[0] === \"e\",\n    };\n    odoo.isReady = false;\n\n    // setup environment\n    const env = makeEnv();\n    await startServices(env);\n\n    // start web client\n    await whenReady();\n    const legacyEnv = await legacySetupProm;\n    mapLegacyEnvToWowlEnv(legacyEnv, env);\n    const app = new App(Webclient, {\n        env,\n        templates,\n        dev: env.debug,\n        translatableAttributes: [\"data-tooltip\"],\n        translateFn: env._t,\n    });\n    renderToString.app = app;\n    setLoadXmlDefaultApp(app);\n    const root = await app.mount(document.body);\n    const classList = document.body.classList;\n    if (localization.direction === \"rtl\") {\n        classList.add(\"o_rtl\");\n    }\n    if (env.services.user.userId === 1) {\n        classList.add(\"o_is_superuser\");\n    }\n    if (env.debug) {\n        classList.add(\"o_debug\");\n    }\n    if (hasTouch()) {\n        classList.add(\"o_touch_device\");\n    }\n    // delete odoo.debug; // FIXME: some legacy code rely on this\n    odoo.__WOWL_DEBUG__ = { root };\n    odoo.isReady = true;\n\n    // Update Favicons\n    const favicon = `/web/image/res.company/${env.services.company.currentCompany.id}/favicon`;\n    const icons = document.querySelectorAll(\"link[rel*='icon']\");\n    const msIcon = document.querySelector(\"meta[name='msapplication-TileImage']\");\n    for (const icon of icons) {\n        icon.href = favicon;\n    }\n    if (msIcon) {\n        msIcon.content = favicon;\n    }\n}\n", "/** @odoo-module alias=web.legacySetup **/\n\nimport { registry } from \"../core/registry\";\nimport {\n    makeLegacyNotificationService,\n    makeLegacyRpcService,\n    makeLegacySessionService,\n    makeLegacyDialogMappingService,\n    makeLegacyCrashManagerService,\n    makeLegacyCommandService,\n    makeLegacyDropdownService,\n} from \"./utils\";\nimport { makeLegacyActionManagerService } from \"./backend_utils\";\nimport * as AbstractService from \"web.AbstractService\";\nimport legacyEnv from \"web.env\";\nimport * as session from \"web.session\";\nimport * as makeLegacyWebClientService from \"web.pseudo_web_client\";\nimport { templates } from \"@web/core/assets\";\n\nimport { Component, whenReady } from \"@odoo/owl\";\n\nlet legacySetupResolver;\nexport const legacySetupProm = new Promise((resolve) => {\n    legacySetupResolver = resolve;\n});\n\n// build the legacy env and set it on Component (this was done in main.js,\n// with the starting of the webclient)\n(async () => {\n    AbstractService.prototype.deployServices(legacyEnv);\n    Component.env = legacyEnv;\n    const legacyActionManagerService = makeLegacyActionManagerService(legacyEnv);\n    const serviceRegistry = registry.category(\"services\");\n    serviceRegistry.add(\"legacy_action_manager\", legacyActionManagerService);\n    // add a service to redirect rpc events triggered on the bus in the\n    // legacy env on the bus in the wowl env\n    const legacyRpcService = makeLegacyRpcService(legacyEnv);\n    serviceRegistry.add(\"legacy_rpc\", legacyRpcService);\n    const legacySessionService = makeLegacySessionService(legacyEnv, session);\n    serviceRegistry.add(\"legacy_session\", legacySessionService);\n    const legacyWebClientService = makeLegacyWebClientService(legacyEnv);\n    serviceRegistry.add(\"legacy_web_client\", legacyWebClientService);\n    serviceRegistry.add(\"legacy_notification\", makeLegacyNotificationService(legacyEnv));\n    serviceRegistry.add(\"legacy_crash_manager\", makeLegacyCrashManagerService(legacyEnv));\n    const legacyDialogMappingService = makeLegacyDialogMappingService(legacyEnv);\n    serviceRegistry.add(\"legacy_dialog_mapping\", legacyDialogMappingService);\n    const legacyCommandService = makeLegacyCommandService(legacyEnv);\n    serviceRegistry.add(\"legacy_command\", legacyCommandService);\n    serviceRegistry.add(\"legacy_dropdown\", makeLegacyDropdownService(legacyEnv));\n    const wowlToLegacyServiceMappers = registry.category(\"wowlToLegacyServiceMappers\").getEntries();\n    for (const [legacyServiceName, wowlToLegacyServiceMapper] of wowlToLegacyServiceMappers) {\n        serviceRegistry.add(legacyServiceName, wowlToLegacyServiceMapper(legacyEnv));\n    }\n    await Promise.all([whenReady(), session.is_bound]);\n    legacyEnv.templates = templates;\n    legacySetupResolver(legacyEnv);\n})();\n", "/** @odoo-module **/\n\nimport { ChatWindowManagerContainer } from '@mail/components/chat_window_manager_container/chat_window_manager_container';\nimport { DialogManagerContainer } from '@mail/components/dialog_manager_container/dialog_manager_container';\nimport { DiscussContainer } from '@mail/components/discuss_container/discuss_container';\nimport { PopoverManagerContainer } from '@mail/components/popover_manager_container/popover_manager_container';\nimport { messagingService } from '@mail/services/messaging_service';\nimport { systrayService } from '@mail/services/systray_service';\nimport { makeMessagingToLegacyEnv } from '@mail/utils/make_messaging_to_legacy_env';\n\nimport { registry } from '@web/core/registry';\n\nconst messagingValuesService = {\n    start() {\n        return {};\n    }\n};\n\nconst serviceRegistry = registry.category('services');\nserviceRegistry.add('messaging', messagingService);\nserviceRegistry.add('messagingValues', messagingValuesService);\nserviceRegistry.add('systray_service', systrayService);\nserviceRegistry.add('messaging_service_to_legacy_env', makeMessagingToLegacyEnv(owl.Component.env));\n\nregistry.category('actions').add('mail.action_discuss', DiscussContainer);\n\nregistry.category('main_components').add('DialogManagerContainer', { Component: DialogManagerContainer });\nregistry.category('main_components').add('ChatWindowManagerContainer', { Component: ChatWindowManagerContainer });\nregistry.category('main_components').add('PopoverManagerContainer', { Component: PopoverManagerContainer });\n"], "file": "/web/assets/1225-41a5e4a/web.assets_backend_prod_only.js", "sourceRoot": "../../../"}