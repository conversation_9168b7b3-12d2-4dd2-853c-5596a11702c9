
/*************************************************************
*  Filepath: /web/static/src/legacy/js/promise_extension.js  *
*  Lines: 23                                                 *
*************************************************************/
/**
 * This file adds a 'guardedCatch' function to the Promise API. This function
 * has to be used when we don't want to swallow real errors (crashes), like
 * 'catch' does (i.e. basically all the time in Odoo). We only execute the
 * 'onRejected' handler if the rejection's reason is not an Error, and we always
 * return a rejected Promise to let the rejection bubble up (and trigger the
 * 'unhandledrejection' event).
 */

(function () {
    var _catch = Promise.prototype.catch;
    Promise.prototype.guardedCatch = function (onRejected) {
        return _catch.call(this, function (reason) {
            const error = (reason instanceof Error && "cause" in reason) ? reason.cause : reason;
            if (!error || !(error instanceof Error)) {
                if (onRejected) {
                    onRejected.call(this, reason);
                }
            }
            return Promise.reject(reason);
        });
    };
})();
;

/**************************************
*  Filepath: /web/static/src/boot.js  *
*  Lines: 444                         *
**************************************/
/**
 *------------------------------------------------------------------------------
 * Odoo Web Boostrap Code
 *------------------------------------------------------------------------------
 *
 * Each module can return a promise. In that case, the module is marked as loaded
 * only when the promise is resolved, and its value is equal to the resolved value.
 * The module can be rejected (unloaded). This will be logged in the console as info.
 *
 * logs:
 *      Missing dependencies:
 *          These modules do not appear in the page. It is possible that the
 *          JavaScript file is not in the page or that the module name is wrong
 *      Failed modules:
 *          A javascript error is detected
 *      Rejected modules:
 *          The module returns a rejected promise. It (and its dependent modules)
 *          is not loaded.
 *      Rejected linked modules:
 *          Modules who depend on a rejected module
 *      Non loaded modules:
 *          Modules who depend on a missing or a failed module
 *      Debug:
 *          Non loaded or failed module informations for debugging
 */
(function () {
    "use strict";

    var jobUID = Date.now();

    var jobs = [];
    var factories = Object.create(null);
    var jobDeps = [];
    var jobPromises = [];

    var services = Object.create({});

    var commentRegExp = /(\/\*([\s\S]*?)\*\/|([^:]|^)\/\/(.*)$)/gm;
    var cjsRequireRegExp = /[^.]\s*require\s*\(\s*["']([^'"\s]+)["']\s*\)/g;
    if (!globalThis.odoo) {
        globalThis.odoo = {};
    }
    var odoo = globalThis.odoo;
    var debug = odoo.debug;

    var didLogInfoResolve;
    var didLogInfoPromise = new Promise(function (resolve) {
        didLogInfoResolve = resolve;
    });

    odoo.remainingJobs = jobs;
    odoo.__DEBUG__ = {
        didLogInfo: didLogInfoPromise,
        getDependencies: function (name, transitive) {
            var deps = name instanceof Array ? name : [name];
            var changed;
            do {
                changed = false;
                jobDeps.forEach(function (dep) {
                    if (deps.indexOf(dep.to) >= 0 && deps.indexOf(dep.from) < 0) {
                        deps.push(dep.from);
                        changed = true;
                    }
                });
            } while (changed && transitive);
            return deps;
        },
        getDependents: function (name) {
            return jobDeps
                .filter(function (dep) {
                    return dep.from === name;
                })
                .map(function (dep) {
                    return dep.to;
                });
        },
        getWaitedJobs: function () {
            return jobs
                .map(function (job) {
                    return job.name;
                })
                .filter(function (item, index, self) {
                    // uniq
                    return self.indexOf(item) === index;
                });
        },
        getMissingJobs: function () {
            var self = this;
            var waited = this.getWaitedJobs();
            var missing = [];
            waited.forEach(function (job) {
                self.getDependencies(job).forEach(function (job) {
                    if (!(job in self.services)) {
                        missing.push(job);
                    }
                });
            });
            return missing
                .filter(function (item, index, self) {
                    return self.indexOf(item) === index;
                })
                .filter(function (item) {
                    return waited.indexOf(item) < 0;
                })
                .filter(function (job) {
                    return !job.error;
                });
        },
        getFailedJobs: function () {
            return jobs.filter(function (job) {
                return !!job.error;
            });
        },
        processJobs: function () {
            var job;

            function processJob(job) {
                var require = makeRequire(job);

                var jobExec;
                function onError(e) {
                    job.error = e;
                    console.error(`Error while loading ${job.name}: ${e.message}`, e);
                    Promise.reject(e);
                }
                var def = new Promise(function (resolve) {
                    try {
                        jobExec = job.factory.call(null, require);
                        jobs.splice(jobs.indexOf(job), 1);
                    } catch (e) {
                        onError(e);
                    }
                    if (!job.error) {
                        Promise.resolve(jobExec)
                            .then(function (data) {
                                services[job.name] = data;
                                resolve();
                                odoo.__DEBUG__.processJobs();
                            })
                            .guardedCatch(function (e) {
                                job.rejected = e || true;
                                jobs.push(job);
                            })
                            .catch(function (e) {
                                if (e instanceof Error) {
                                    onError(e);
                                }
                                resolve();
                            });
                    } else {
                        resolve();
                    }
                });
                jobPromises.push(def);
                def.then(job.resolve);
            }

            function isReady(job) {
                return (
                    !job.error &&
                    !job.rejected &&
                    job.factory.deps.every(function (name) {
                        return name in services;
                    })
                );
            }

            function makeRequire(job) {
                var deps = {};
                Object.keys(services)
                    .filter(function (item) {
                        return job.deps.indexOf(item) >= 0;
                    })
                    .forEach(function (key) {
                        deps[key] = services[key];
                    });

                return function require(name) {
                    if (!(name in deps)) {
                        console.error("Undefined dependency: ", name);
                    }
                    return deps[name];
                };
            }

            while (jobs.length) {
                job = undefined;
                for (var i = 0; i < jobs.length; i++) {
                    if (isReady(jobs[i])) {
                        job = jobs[i];
                        break;
                    }
                }
                if (!job) {
                    break;
                }
                processJob(job);
            }

            return services;
        },
        factories: factories,
        services: services,
    };
    odoo.define = function () {
        var args = Array.prototype.slice.call(arguments);
        var name = typeof args[0] === "string" ? args.shift() : "__odoo_job" + jobUID++;
        var factory = args[args.length - 1];
        var deps;
        if (args[0] instanceof Array) {
            deps = args[0];
        } else {
            deps = [];
            factory
                .toString()
                .replace(commentRegExp, "")
                .replace(cjsRequireRegExp, function (match, dep) {
                    deps.push(dep);
                });
        }

        if (!(deps instanceof Array)) {
            throw new Error("Dependencies should be defined by an array", deps);
        }
        if (typeof factory !== "function") {
            throw new Error("Factory should be defined by a function", factory);
        }
        if (typeof name !== "string") {
            throw new Error("Invalid name definition (should be a string", name);
        }
        if (name in factories) {
            throw new Error("Service " + name + " already defined");
        }

        factory.deps = deps;
        factories[name] = factory;

        let promiseResolve;
        const promise = new Promise((resolve) => {
            promiseResolve = resolve;
        });
        jobs.push({
            name: name,
            factory: factory,
            deps: deps,
            resolve: promiseResolve,
            promise: promise,
        });

        deps.forEach(function (dep) {
            jobDeps.push({ from: dep, to: name });
        });

        odoo.__DEBUG__.processJobs();
    };
    odoo.log = function () {
        var missing = [];
        var failed = [];
        var cycle = null;

        if (jobs.length) {
            var debugJobs = {};
            var rejected = [];
            var rejectedLinked = [];
            var job;
            var jobdep;

            for (var k = 0; k < jobs.length; k++) {
                debugJobs[jobs[k].name] = job = {
                    dependencies: jobs[k].deps,
                    dependents: odoo.__DEBUG__.getDependents(jobs[k].name),
                    name: jobs[k].name,
                };
                if (jobs[k].error) {
                    job.error = jobs[k].error;
                }
                if (jobs[k].rejected) {
                    job.rejected = jobs[k].rejected;
                    rejected.push(job.name);
                }
                var deps = odoo.__DEBUG__.getDependencies(job.name);
                for (var i = 0; i < deps.length; i++) {
                    if (job.name !== deps[i] && !(deps[i] in services)) {
                        jobdep = debugJobs[deps[i]];
                        if (!jobdep && deps[i] in factories) {
                            for (var j = 0; j < jobs.length; j++) {
                                if (jobs[j].name === deps[i]) {
                                    jobdep = jobs[j];
                                    break;
                                }
                            }
                        }
                        if (jobdep && jobdep.rejected) {
                            if (!job.rejected) {
                                job.rejected = [];
                                rejectedLinked.push(job.name);
                            }
                            job.rejected.push(deps[i]);
                        } else {
                            if (!job.missing) {
                                job.missing = [];
                            }
                            job.missing.push(deps[i]);
                        }
                    }
                }
            }
            missing = odoo.__DEBUG__.getMissingJobs();
            failed = odoo.__DEBUG__.getFailedJobs();
            var unloaded = Object.keys(debugJobs) // Object.values is not supported
                .map(function (key) {
                    return debugJobs[key];
                })
                .filter(function (job) {
                    return job.missing;
                });

            if (debug || failed.length || unloaded.length) {
                var log = globalThis.console[
                    !failed.length || !unloaded.length ? "info" : "error"
                ].bind(globalThis.console);
                log(
                    (failed.length ? "error" : unloaded.length ? "warning" : "info") +
                        ": Some modules could not be started"
                );
                if (missing.length) {
                    log("Missing dependencies:    ", missing);
                }
                if (failed.length) {
                    log(
                        "Failed modules:          ",
                        failed.map(function (fail) {
                            return fail.name;
                        })
                    );
                }
                if (rejected.length) {
                    log("Rejected modules:        ", rejected);
                }
                if (rejectedLinked.length) {
                    log("Rejected linked modules: ", rejectedLinked);
                }
                if (unloaded.length) {
                    cycle = findCycle(unloaded);
                    if (cycle) {
                        console.error("Cyclic dependencies: " + cycle);
                    }
                    log(
                        "Non loaded modules:      ",
                        unloaded.map(function (unload) {
                            return unload.name;
                        })
                    );
                }
                if (debug && Object.keys(debugJobs).length) {
                    log("Debug:                   ", debugJobs);
                }
            }
        }
        odoo.__DEBUG__.jsModules = {
            missing: missing,
            failed: failed.map((mod) => mod.name),
            unloaded: unloaded ? unloaded.map((mod) => mod.name) : [],
            cycle,
        };
        didLogInfoResolve(true);
    };
    /**
     * Returns a resolved promise when the targeted services are loaded.
     * If no service is found the promise is used directly.
     *
     * @param {string|RegExp} serviceName name of the service to expect
     *      or regular expression matching the service.
     * @returns {Promise<number>} resolved when the services ares
     *      loaded. The value is equal to the number of services found.
     */
    odoo.ready = async function (serviceName) {
        function match(name) {
            return typeof serviceName === "string" ? name === serviceName : serviceName.test(name);
        }
        await Promise.all(jobs.filter((job) => match(job.name)).map((job) => job.promise));
        return Object.keys(factories).filter(match).length;
    };

    odoo.runtimeImport = function (moduleName) {
        if (!(moduleName in services)) {
            throw new Error(`Service "${moduleName} is not defined or isn't finished loading."`);
        }
        return services[moduleName];
    };

    // Automatically log errors detected when loading modules
    globalThis.addEventListener("load", function logWhenLoaded() {
        const len = jobPromises.length;
        Promise.all(jobPromises).then(function () {
            if (len === jobPromises.length) {
                odoo.log();
            } else {
                logWhenLoaded();
            }
        });
    });

    /**
     * Visit the list of jobs, and return the first found cycle, if any
     *
     * @param {any[]} jobs
     * @returns {null | string} either a string describing a cycle, or null
     */
    function findCycle(jobs) {
        // build dependency graph
        const dependencyGraph = new Map();
        for (const job of jobs) {
            dependencyGraph.set(job.name, job.dependencies);
        }

        // helpers
        function visitJobs(jobs, visited = new Set()) {
            for (const job of jobs) {
                const result = visitJob(job, visited);
                if (result) {
                    return result;
                }
            }
            return null;
        }

        function visitJob(job, visited) {
            if (visited.has(job)) {
                const jobs = Array.from(visited).concat([job]);
                const index = jobs.indexOf(job);
                return jobs
                    .slice(index)
                    .map((j) => `"${j}"`)
                    .join(" => ");
            }
            const deps = dependencyGraph.get(job);
            return deps ? visitJobs(deps, new Set(visited).add(job)) : null;
        }

        // visit each root to find cycles
        return visitJobs(jobs.map((j) => j.name));
    }
})();
;

/**********************************************************
*  Filepath: /bus/static/src/workers/websocket_worker.js  *
*  Lines: 445                                             *
**********************************************************/
odoo.define('@bus/workers/websocket_worker', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { debounce } = require('@bus/workers/websocket_worker_utils');

/**
 * Type of events that can be sent from the worker to its clients.
 *
 * @typedef { 'connect' | 'reconnect' | 'disconnect' | 'reconnecting' | 'notification' | 'initialized' } WorkerEvent
 */

/**
 * Type of action that can be sent from the client to the worker.
 *
 * @typedef {'add_channel' | 'delete_channel' | 'force_update_channels' | 'initialize_connection' | 'send' | 'leave' | 'stop' | 'start' } WorkerAction
 */

const WEBSOCKET_CLOSE_CODES = __exports.WEBSOCKET_CLOSE_CODES = Object.freeze({
    CLEAN: 1000,
    GOING_AWAY: 1001,
    PROTOCOL_ERROR: 1002,
    INCORRECT_DATA: 1003,
    ABNORMAL_CLOSURE: 1006,
    INCONSISTENT_DATA: 1007,
    MESSAGE_VIOLATING_POLICY: 1008,
    MESSAGE_TOO_BIG: 1009,
    EXTENSION_NEGOTIATION_FAILED: 1010,
    SERVER_ERROR: 1011,
    RESTART: 1012,
    TRY_LATER: 1013,
    BAD_GATEWAY: 1014,
    SESSION_EXPIRED: 4001,
    KEEP_ALIVE_TIMEOUT: 4002,
    RECONNECTING: 4003,
});
// Should be incremented on every worker update in order to force
// update of the worker in browser cache.
const WORKER_VERSION = __exports.WORKER_VERSION = '1.0.5';
const INITIAL_RECONNECT_DELAY = 1000;
const MAXIMUM_RECONNECT_DELAY = 60000;

/**
 * This class regroups the logic necessary in order for the
 * SharedWorker/Worker to work. Indeed, Safari and some minor browsers
 * do not support SharedWorker. In order to solve this issue, a Worker
 * is used in this case. The logic is almost the same than the one used
 * for SharedWorker and this class implements it.
 */
const WebsocketWorker = __exports.WebsocketWorker = class WebsocketWorker {
    constructor() {
        // Timestamp of start of most recent bus service sender
        this.newestStartTs = undefined;
        this.websocketURL = "";
        this.currentUID = null;
        this.isWaitingForNewUID = true;
        this.channelsByClient = new Map();
        this.connectRetryDelay = INITIAL_RECONNECT_DELAY;
        this.connectTimeout = null;
        this.debugModeByClient = new Map();
        this.isDebug = false;
        this.isReconnecting = false;
        this.lastChannelSubscription = null;
        this.lastNotificationId = 0;
        this.messageWaitQueue = [];
        this._forceUpdateChannels = debounce(this._forceUpdateChannels, 300, true);

        this._onWebsocketClose = this._onWebsocketClose.bind(this);
        this._onWebsocketError = this._onWebsocketError.bind(this);
        this._onWebsocketMessage = this._onWebsocketMessage.bind(this);
        this._onWebsocketOpen = this._onWebsocketOpen.bind(this);
    }

    //--------------------------------------------------------------------------
    // Public
    //--------------------------------------------------------------------------

    /**
     * Send the message to all the clients that are connected to the
     * worker.
     *
     * @param {WorkerEvent} type Event to broadcast to connected
     * clients.
     * @param {Object} data
     */
    broadcast(type, data) {
        for (const client of this.channelsByClient.keys()) {
            client.postMessage({ type, data });
        }
    }

    /**
     * Register a client handled by this worker.
     *
     * @param {MessagePort} messagePort
     */
    registerClient(messagePort) {
        messagePort.onmessage = ev => {
            this._onClientMessage(messagePort, ev.data);
        };
        this.channelsByClient.set(messagePort, []);
    }

    /**
     * Send message to the given client.
     *
     * @param {number} client
     * @param {WorkerEvent} type
     * @param {Object} data
     */
    sendToClient(client, type, data) {
        client.postMessage({ type, data });
    }

    //--------------------------------------------------------------------------
    // PRIVATE
    //--------------------------------------------------------------------------

    /**
     * Called when a message is posted to the worker by a client (i.e. a
     * MessagePort connected to this worker).
     *
     * @param {MessagePort} client
     * @param {Object} message
     * @param {WorkerAction} [message.action]
     * Action to execute.
     * @param {Object|undefined} [message.data] Data required by the
     * action.
     */
    _onClientMessage(client, { action, data }) {
        switch (action) {
            case 'send':
                return this._sendToServer(data);
            case 'start':
                return this._start();
            case 'stop':
                return this._stop();
            case 'leave':
                return this._unregisterClient(client);
            case 'add_channel':
                return this._addChannel(client, data);
            case 'delete_channel':
                return this._deleteChannel(client, data);
            case 'force_update_channels':
                return this._forceUpdateChannels();
            case 'initialize_connection':
                return this._initializeConnection(client, data);
        }
    }

    /**
     * Add a channel for the given client. If this channel is not yet
     * known, update the subscription on the server.
     *
     * @param {MessagePort} client
     * @param {string} channel
     */
    _addChannel(client, channel) {
        const clientChannels = this.channelsByClient.get(client);
        if (!clientChannels.includes(channel)) {
            clientChannels.push(channel);
            this.channelsByClient.set(client, clientChannels);
            this._updateChannels();
        }
    }

    /**
     * Remove a channel for the given client. If this channel is not
     * used anymore, update the subscription on the server.
     *
     * @param {MessagePort} client
     * @param {string} channel
     */
    _deleteChannel(client, channel) {
        const clientChannels = this.channelsByClient.get(client);
        if (!clientChannels) {
            return;
        }
        const channelIndex = clientChannels.indexOf(channel);
        if (channelIndex !== -1) {
            clientChannels.splice(channelIndex, 1);
            this._updateChannels();
        }
    }

    /**
     * Update the channels on the server side even if the channels on
     * the client side are the same than the last time we subscribed.
     */
    _forceUpdateChannels() {
        this._updateChannels({ force: true });
    }

    /**
     * Remove the given client from this worker client list as well as
     * its channels. If some of its channels are not used anymore,
     * update the subscription on the server.
     *
     * @param {MessagePort} client
     */
    _unregisterClient(client) {
        this.channelsByClient.delete(client);
        this.debugModeByClient.delete(client);
        this.isDebug = Object.values(this.debugModeByClient).some(debugValue => debugValue !== '');
        this._updateChannels();
    }

    /**
     * Initialize a client connection to this worker.
     *
     * @param {Object} param0
     * @param {String} [param0.debug] Current debugging mode for the
     * given client.
     * @param {Number} [param0.lastNotificationId] Last notification id
     * known by the client.
     * @param {String} [param0.websocketURL] URL of the websocket endpoint.
     * @param {Number|false|undefined} [param0.uid] Current user id
     *     - Number: user is logged whether on the frontend/backend.
     *     - false: user is not logged.
     *     - undefined: not available (e.g. livechat support page)
     * @param {Number} param0.startTs Timestamp of start of bus service sender.
     */
    _initializeConnection(client, { debug, lastNotificationId, uid, websocketURL, startTs }) {
        if (this.newestStartTs && this.newestStartTs > startTs) {
            this.debugModeByClient[client] = debug;
            this.isDebug = Object.values(this.debugModeByClient).some(debugValue => debugValue !== '');
            this.sendToClient(client, "initialized");
            return;
        }
        this.newestStartTs = startTs;
        this.websocketURL = websocketURL;
        this.lastNotificationId = lastNotificationId;
        this.debugModeByClient[client] = debug;
        this.isDebug = Object.values(this.debugModeByClient).some(debugValue => debugValue !== '');
        const isCurrentUserKnown = uid !== undefined;
        if (this.isWaitingForNewUID && isCurrentUserKnown) {
            this.isWaitingForNewUID = false;
            this.currentUID = uid;
        }
        if (this.currentUID !== uid && isCurrentUserKnown) {
            this.currentUID = uid;
            if (this.websocket) {
                this.websocket.close(WEBSOCKET_CLOSE_CODES.CLEAN);
            }
            this.channelsByClient.forEach((_, key) => this.channelsByClient.set(key, []));
        }
        this.sendToClient(client, 'initialized');
    }

    /**
     * Determine whether or not the websocket associated to this worker
     * is connected.
     *
     * @returns {boolean}
     */
    _isWebsocketConnected() {
        return this.websocket && this.websocket.readyState === 1;
    }

    /**
     * Determine whether or not the websocket associated to this worker
     * is connecting.
     *
     * @returns {boolean}
     */
    _isWebsocketConnecting() {
        return this.websocket && this.websocket.readyState === 0;
    }

    /**
     * Determine whether or not the websocket associated to this worker
     * is in the closing state.
     *
     * @returns {boolean}
     */
    _isWebsocketClosing() {
        return this.websocket && this.websocket.readyState === 2;
    }

    /**
     * Triggered when a connection is closed. If closure was not clean ,
     * try to reconnect after indicating to the clients that the
     * connection was closed.
     *
     * @param {CloseEvent} ev
     * @param {number} code  close code indicating why the connection
     * was closed.
     * @param {string} reason reason indicating why the connection was
     * closed.
     */
    _onWebsocketClose({ code, reason }) {
        if (this.isDebug) {
            console.debug(`%c${new Date().toLocaleString()} - [onClose]`, 'color: #c6e; font-weight: bold;', code, reason);
        }
        this.lastChannelSubscription = null;
        if (this.isReconnecting) {
            // Connection was not established but the close event was
            // triggered anyway. Let the onWebsocketError method handle
            // this case.
            return;
        }
        this.broadcast('disconnect', { code, reason });
        if (code === WEBSOCKET_CLOSE_CODES.CLEAN) {
            // WebSocket was closed on purpose, do not try to reconnect.
            return;
        }
        // WebSocket was not closed cleanly, let's try to reconnect.
        this.broadcast('reconnecting', { closeCode: code });
        this.isReconnecting = true;
        if (code === WEBSOCKET_CLOSE_CODES.KEEP_ALIVE_TIMEOUT) {
            // Don't wait to reconnect on keep alive timeout.
            this.connectRetryDelay = 0;
        }
        if (code === WEBSOCKET_CLOSE_CODES.SESSION_EXPIRED) {
            this.isWaitingForNewUID = true;
        }
        this._retryConnectionWithDelay();
    }

    /**
     * Triggered when a connection failed or failed to established.
     */
    _onWebsocketError() {
        if (this.isDebug) {
            console.debug(`%c${new Date().toLocaleString()} - [onError]`, 'color: #c6e; font-weight: bold;');
        }
        this._retryConnectionWithDelay();
    }

    /**
    * Handle data received from the bus.
    *
    * @param {MessageEvent} messageEv
    */
    _onWebsocketMessage(messageEv) {
        const notifications = JSON.parse(messageEv.data);
        if (this.isDebug) {
            console.debug(`%c${new Date().toLocaleString()} - [onMessage]`, 'color: #c6e; font-weight: bold;', notifications);
        }
        this.lastNotificationId = notifications[notifications.length - 1].id;
        this.broadcast('notification', notifications);
    }

    /**
     * Triggered on websocket open. Send message that were waiting for
     * the connection to open.
     */
    _onWebsocketOpen() {
        if (this.isDebug) {
            console.debug(`%c${new Date().toLocaleString()} - [onOpen]`, 'color: #c6e; font-weight: bold;');
        }
        this._updateChannels();
        this.messageWaitQueue.forEach(msg => this.websocket.send(msg));
        this.messageWaitQueue = [];
        this.broadcast(this.isReconnecting ? 'reconnect' : 'connect');
        this.connectRetryDelay = INITIAL_RECONNECT_DELAY;
        this.connectTimeout = null;
        this.isReconnecting = false;
    }

    /**
     * Try to reconnect to the server, an exponential back off is
     * applied to the reconnect attempts.
     */
    _retryConnectionWithDelay() {
        this.connectRetryDelay = Math.min(this.connectRetryDelay * 1.5, MAXIMUM_RECONNECT_DELAY) + 1000 * Math.random();
        this.connectTimeout = setTimeout(this._start.bind(this), this.connectRetryDelay);
    }

    /**
     * Send a message to the server through the websocket connection.
     * If the websocket is not open, enqueue the message and send it
     * upon the next reconnection.
     *
     * @param {any} message Message to send to the server.
     */
    _sendToServer(message) {
        const payload = JSON.stringify(message);
        if (!this._isWebsocketConnected()) {
            this.messageWaitQueue.push(payload);
        } else {
            this.websocket.send(payload);
        }
    }

    /**
     * Start the worker by opening a websocket connection.
     */
    _start() {
        if (this._isWebsocketConnected() || this._isWebsocketConnecting()) {
            return;
        }
        if (this.websocket) {
            this.websocket.removeEventListener('open', this._onWebsocketOpen);
            this.websocket.removeEventListener('message', this._onWebsocketMessage);
            this.websocket.removeEventListener('error', this._onWebsocketError);
            this.websocket.removeEventListener('close', this._onWebsocketClose);
        }
        if (this._isWebsocketClosing()) {
            // close event was not triggered and will never be, broadcast the
            // disconnect event for consistency sake.
            this.lastChannelSubscription = null;
            this.broadcast("disconnect", { code: WEBSOCKET_CLOSE_CODES.ABNORMAL_CLOSURE });
        }
        this.websocket = new WebSocket(this.websocketURL);
        this.websocket.addEventListener('open', this._onWebsocketOpen);
        this.websocket.addEventListener('error', this._onWebsocketError);
        this.websocket.addEventListener('message', this._onWebsocketMessage);
        this.websocket.addEventListener('close', this._onWebsocketClose);
    }

    /**
     * Stop the worker.
     */
    _stop() {
        clearTimeout(this.connectTimeout);
        this.connectRetryDelay = INITIAL_RECONNECT_DELAY;
        this.isReconnecting = false;
        this.lastChannelSubscription = null;
        if (this.websocket) {
            this.websocket.close();
        }
    }

    /**
     * Update the channel subscription on the server. Ignore if the channels
     * did not change since the last subscription.
     *
     * @param {boolean} force Whether or not we should update the subscription
     * event if the channels haven't change since last subscription.
     */
    _updateChannels({ force = false } = {}) {
        const allTabsChannels = [...new Set([].concat.apply([], [...this.channelsByClient.values()]))].sort();
        const allTabsChannelsString = JSON.stringify(allTabsChannels);
        const shouldUpdateChannelSubscription = allTabsChannelsString !== this.lastChannelSubscription;
        if (force || shouldUpdateChannelSubscription) {
            this.lastChannelSubscription = allTabsChannelsString;
            this._sendToServer({ event_name: 'subscribe', data: { channels: allTabsChannels, last: this.lastNotificationId } });
        }
    }
}

return __exports;
});
;

/*****************************************************************
*  Filepath: /bus/static/src/workers/websocket_worker_script.js  *
*  Lines: 29                                                     *
*****************************************************************/
odoo.define('@bus/workers/websocket_worker_script', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/
/* eslint-env worker */
/* eslint-disable no-restricted-globals */

const { WebsocketWorker } = require("@bus/workers/websocket_worker");

(function () {
    const websocketWorker = new WebsocketWorker();

    if (self.name.includes('shared')) {
        // The script is running in a shared worker: let's register every
        // tab connection to the worker in order to relay notifications
        // coming from the websocket.
        onconnect = function (ev) {
            const currentClient = ev.ports[0];
            websocketWorker.registerClient(currentClient);
        };
    } else {
        // The script is running in a simple web worker.
        websocketWorker.registerClient(self);
    }
})();


return __exports;
});
;

/****************************************************************
*  Filepath: /bus/static/src/workers/websocket_worker_utils.js  *
*  Lines: 35                                                    *
****************************************************************/
odoo.define('@bus/workers/websocket_worker_utils', async function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

/**
 * Returns a function, that, as long as it continues to be invoked, will not
 * be triggered. The function will be called after it stops being called for
 * N milliseconds. If `immediate` is passed, trigger the function on the
 * leading edge, instead of the trailing.
 *
 * Inspired by https://davidwalsh.name/javascript-debounce-function
 */
 __exports.debounce = debounce; function debounce(func, wait, immediate) {
    let timeout;
    return function () {
        const context = this;
        const args = arguments;
        function later() {
            timeout = null;
            if (!immediate) {
                func.apply(context, args);
            }
        }
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) {
            func.apply(context, args);
        }
    };
}

return __exports;
});


//# sourceMappingURL=/web/assets/1245-6923fe5/bus.websocket_worker_assets.js.map