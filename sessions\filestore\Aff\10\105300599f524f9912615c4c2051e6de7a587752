)]}'
{"version": 3, "sources": ["/web/static/lib/bootstrap/scss/_functions.scss", "/web/static/lib/bootstrap/scss/_mixins.scss", "/web/static/src/scss/mixins_forwardport.scss", "/web/static/src/scss/bs_mixins_overrides.scss", "/web/static/src/legacy/scss/utils.scss", "/web_enterprise/static/src/scss/primary_variables.scss", "/web/static/src/scss/primary_variables.scss", "/web_enterprise/static/src/core/notifications/notifications.variables.scss", "/web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss", "/web_enterprise/static/src/webclient/navbar/navbar.variables.scss", "/web/static/src/core/notifications/notification.variables.scss", "/web/static/src/search/control_panel/control_panel.variables.scss", "/web/static/src/search/search_panel/search_panel.variables.scss", "/web/static/src/views/form/form.variables.scss", "/web/static/src/views/kanban/kanban.variables.scss", "/web/static/src/webclient/burger_menu/burger_menu.variables.scss", "/web/static/src/webclient/navbar/navbar.variables.scss", "/base/static/src/scss/onboarding.variables.scss", "/mail/static/src/scss/variables/primary_variables.scss", "/web_editor/static/src/scss/web_editor.variables.scss", "/web_editor/static/src/scss/wysiwyg.variables.scss", "/portal/static/src/scss/primary_variables.scss", "/account/static/src/scss/variables.scss", "/website/static/src/scss/primary_variables.scss", "/website/static/src/scss/options/user_values.scss", "/website/static/src/scss/options/colors/user_color_palette.scss", "/website/static/src/scss/options/colors/user_gray_color_palette.scss", "/website/static/src/scss/options/colors/user_theme_color_palette.scss", "/documents/static/src/scss/documents.variables.scss", "/hr_org_chart/static/src/scss/variables.scss", "/website/static/src/snippets/s_badge/000_variables.scss", "/website/static/src/snippets/s_product_list/000_variables.scss", "/website/static/src/scss/secondary_variables.scss", "/web_enterprise/static/src/scss/secondary_variables.scss", "/web/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/secondary_variables.scss", "/web/static/src/scss/pre_variables.scss", "/web/static/lib/bootstrap/scss/_variables.scss", "/web/static/src/legacy/scss/tempusdominus_overridden.scss", "/web/static/lib/tempusdominus/tempusdominus.scss", "/web/static/lib/jquery.ui/jquery-ui.css", "/web/static/src/libs/fontawesome/css/font-awesome.css", "/web/static/lib/odoo_ui_icons/style.css", "/web/static/lib/select2/select2.css", "/web/static/lib/select2-bootstrap-css/select2-bootstrap.css", "/web/static/lib/daterangepicker/daterangepicker.css", "/web/static/src/webclient/navbar/navbar.scss", "/web/static/src/legacy/scss/ui.scss", "/web/static/src/legacy/scss/mimetypes.scss", "/web/static/src/legacy/scss/modal.scss", "/web/static/src/legacy/scss/animation.scss", "/web/static/src/legacy/scss/datepicker.scss", "/web/static/src/legacy/scss/daterangepicker.scss", "/web/static/src/legacy/scss/banner.scss", "/web/static/src/legacy/scss/colorpicker.scss", "/web/static/src/legacy/scss/popover.scss", "/web/static/src/legacy/scss/translation_dialog.scss", "/web/static/src/legacy/scss/keyboard.scss", "/web/static/src/legacy/scss/name_and_signature.scss", "/web/static/src/legacy/scss/web.zoomodoo.scss", "/web/static/src/legacy/scss/fontawesome_overridden.scss", "/web_tour/static/src/scss/tip.scss", "/web_editor/static/src/js/editor/odoo-editor/src/base_style.scss", "/web_enterprise/static/src/webclient/home_menu/home_menu_background.scss", "/web_enterprise/static/src/webclient/navbar/navbar.scss"], "mappings": ";AAAA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChBA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACpTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACv1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACxvEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC3CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtr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vGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACzGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACzKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC9IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACpOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC/lBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["\n/* /web/static/lib/bootstrap/scss/_functions.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_mixins.scss */\n\n", "\n/* /web/static/src/scss/mixins_forwardport.scss */\n\n", "\n/* /web/static/src/scss/bs_mixins_overrides.scss */\n\n", "\n/* /web/static/src/legacy/scss/utils.scss */\n\n.o_colorpicker_widget .o_opacity_slider, .o_colorpicker_widget .o_color_preview {\n  position: relative;\n  z-index: 0;\n}\n\n.o_colorpicker_widget .o_opacity_slider::before, .o_colorpicker_widget .o_color_preview::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background-image: url(\"/web/static/img/transparent.png\");\n  background-size: 10px auto;\n  border-radius: inherit;\n}\n\n.o_colorpicker_widget .o_opacity_slider::after, .o_colorpicker_widget .o_color_preview::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background: inherit;\n  border-radius: inherit;\n}\n\n", "\n/* /web_enterprise/static/src/scss/primary_variables.scss */\n\n", "\n/* /web/static/src/scss/primary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /web/static/src/core/notifications/notification.variables.scss */\n\n", "\n/* /web/static/src/search/control_panel/control_panel.variables.scss */\n\n", "\n/* /web/static/src/search/search_panel/search_panel.variables.scss */\n\n", "\n/* /web/static/src/views/form/form.variables.scss */\n\n", "\n/* /web/static/src/views/kanban/kanban.variables.scss */\n\n", "\n/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */\n\n", "\n/* /web/static/src/webclient/navbar/navbar.variables.scss */\n\n.o_main_navbar .o_menu_toggle, .o_main_navbar .o_menu_systray > * > a, .o_main_navbar .o_menu_systray > * > .dropdown > a, .o_main_navbar .o_menu_systray > * > button, .o_main_navbar .o_menu_systray > * > label, .o_main_navbar .o_menu_brand, .o_main_navbar .o_navbar_apps_menu .dropdown-toggle, .o_main_navbar .dropdown-toggle, .o_main_navbar .o_nav_entry {\n  position: relative;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  width: auto;\n  height: var(--o-navbar-height, 40px);\n  user-select: none;\n  background: transparent;\n  font-size: 1.08333333rem;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.o_main_navbar .o_menu_toggle:hover, .o_main_navbar .o_menu_systray > * > a:hover, .o_main_navbar .o_menu_systray > * > .dropdown > a:hover, .o_main_navbar .o_menu_systray > * > button:hover, .o_main_navbar .o_menu_systray > * > label:hover, .o_main_navbar .o_menu_brand:hover, .o_main_navbar .o_navbar_apps_menu .dropdown-toggle:hover, .o_main_navbar .dropdown-toggle:hover, .o_main_navbar .o_nav_entry:hover, .o_main_navbar .o_menu_toggle:focus, .o_main_navbar .o_menu_systray > * > a:focus, .o_main_navbar .o_menu_systray > * > .dropdown > a:focus, .o_main_navbar .o_menu_systray > * > button:focus, .o_main_navbar .o_menu_systray > * > label:focus, .o_main_navbar .o_menu_brand:focus, .o_main_navbar .o_navbar_apps_menu .dropdown-toggle:focus, .o_main_navbar .dropdown-toggle:focus, .o_main_navbar .o_nav_entry:focus, .o_main_navbar .focus.o_menu_toggle, .o_main_navbar .o_menu_systray > * > a.focus, .o_main_navbar .o_menu_systray > * > .dropdown > a.focus, .o_main_navbar .o_menu_systray > * > button.focus, .o_main_navbar .o_menu_systray > * > label.focus, .o_main_navbar .focus.o_menu_brand, .o_main_navbar .o_navbar_apps_menu .focus.dropdown-toggle, .o_main_navbar .focus.dropdown-toggle, .o_main_navbar .focus.o_nav_entry {\n  color: #FFF;\n}\n\n.o_main_navbar .o_menu_toggle, .o_main_navbar .o_menu_systray > * > a, .o_main_navbar .o_menu_systray > * > .dropdown > a, .o_main_navbar .o_menu_systray > * > button, .o_main_navbar .o_menu_systray > * > label, .o_main_navbar .o_menu_brand, .o_main_navbar .dropdown-toggle, .o_main_navbar .o_nav_entry {\n  padding: 0 12px;\n  line-height: var(--o-navbar-height);\n}\n\n.o_main_navbar .o_menu_systray > * > a:hover, .o_main_navbar .o_menu_systray > * > .dropdown > a:hover, .o_main_navbar .o_menu_systray > * > button:hover, .o_main_navbar .o_menu_systray > * > label:hover, .o_main_navbar .dropdown-toggle:hover, .o_main_navbar .o_nav_entry:hover {\n  background-color: rgba(0, 0, 0, 0.08);\n}\n\n.o_main_navbar .dropdown.show > .dropdown-toggle {\n  background: rgba(0, 0, 0, 0.08);\n  color: #FFF;\n}\n\n", "\n/* /base/static/src/scss/onboarding.variables.scss */\n\n", "\n/* /mail/static/src/scss/variables/primary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/web_editor.variables.scss */\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.variables.scss */\n\n", "\n/* /portal/static/src/scss/primary_variables.scss */\n\n", "\n/* /account/static/src/scss/variables.scss */\n\n@keyframes animate-red {\n  0% {\n    color: red;\n  }\n  100% {\n    color: inherit;\n  }\n}\n\n.animate {\n  animation: animate-red 1s ease;\n}\n\n", "\n/* /website/static/src/scss/primary_variables.scss */\n\n", "\n/* /website/static/src/scss/options/user_values.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */\n\n", "\n/* /documents/static/src/scss/documents.variables.scss */\n\n", "\n/* /hr_org_chart/static/src/scss/variables.scss */\n\n", "\n/* /website/static/src/snippets/s_badge/000_variables.scss */\n\n", "\n/* /website/static/src/snippets/s_product_list/000_variables.scss */\n\n", "\n/* /website/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web/static/src/scss/pre_variables.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_variables.scss */\n\n", "\n/* /web/static/src/legacy/scss/tempusdominus_overridden.scss */\n\n.sr-only, .bootstrap-datetimepicker-widget table th.next::after, .bootstrap-datetimepicker-widget table th.prev::after, .bootstrap-datetimepicker-widget .picker-switch::after, .bootstrap-datetimepicker-widget .btn[data-action=\"today\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"clear\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"togglePeriod\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"showMinutes\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"showHours\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"decrementMinutes\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"decrementHours\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"incrementMinutes\"]::after, .bootstrap-datetimepicker-widget .btn[data-action=\"incrementHours\"]::after {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n", "\n/* /web/static/lib/tempusdominus/tempusdominus.scss */\n\n.bootstrap-datetimepicker-widget {\n  list-style: none;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu {\n  display: block;\n  margin: 2px 0;\n  padding: 4px;\n  width: 14rem;\n}\n\n@media (min-width: 576px) {\n  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {\n    width: 38em;\n  }\n}\n\n@media (min-width: 768px) {\n  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {\n    width: 38em;\n  }\n}\n\n@media (min-width: 992px) {\n  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {\n    width: 38em;\n  }\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu:before, .bootstrap-datetimepicker-widget.dropdown-menu:after {\n  content: '';\n  display: inline-block;\n  position: absolute;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before {\n  border-left: 7px solid transparent;\n  border-right: 7px solid transparent;\n  border-bottom: 7px solid rgba(0, 0, 0, 0.15);\n  border-bottom-color: var(--bs-datetimepicker-secondary-border-color-rgba, rgba(0, 0, 0, 0.2));\n  top: -7px;\n  left: 7px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.bottom:after {\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  border-bottom: 6px solid var(--bs-datetimepicker-primary-border-color, white);\n  top: -6px;\n  left: 8px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.top:before {\n  border-left: 7px solid transparent;\n  border-right: 7px solid transparent;\n  border-top: 7px solid rgba(0, 0, 0, 0.15);\n  border-top-color: var(--bs-datetimepicker-secondary-border-color-rgba, rgba(0, 0, 0, 0.2));\n  bottom: -7px;\n  left: 6px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.top:after {\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  border-top: 6px solid var(--bs-datetimepicker-primary-border-color, white);\n  bottom: -6px;\n  left: 7px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.float-right:before {\n  left: auto;\n  right: 6px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.float-right:after {\n  left: auto;\n  right: 7px;\n}\n\n.bootstrap-datetimepicker-widget.dropdown-menu.wider {\n  width: 16rem;\n}\n\n.bootstrap-datetimepicker-widget .list-unstyled {\n  margin: 0;\n}\n\n.bootstrap-datetimepicker-widget a[data-action] {\n  padding: 6px 0;\n}\n\n.bootstrap-datetimepicker-widget a[data-action]:active {\n  box-shadow: none;\n}\n\n.bootstrap-datetimepicker-widget .timepicker-hour, .bootstrap-datetimepicker-widget .timepicker-minute, .bootstrap-datetimepicker-widget .timepicker-second {\n  width: 54px;\n  font-weight: bold;\n  font-size: var(--bs-datetimepicker-timepicker-font-size, 1.2em);\n  margin: 0;\n}\n\n.bootstrap-datetimepicker-widget button[data-action] {\n  padding: 6px;\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"incrementHours\"]::after {\n  content: \"Increment Hours\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"incrementMinutes\"]::after {\n  content: \"Increment Minutes\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"decrementHours\"]::after {\n  content: \"Decrement Hours\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"decrementMinutes\"]::after {\n  content: \"Decrement Minutes\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"showHours\"]::after {\n  content: \"Show Hours\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"showMinutes\"]::after {\n  content: \"Show Minutes\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"togglePeriod\"]::after {\n  content: \"Toggle AM/PM\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"clear\"]::after {\n  content: \"Clear the picker\";\n}\n\n.bootstrap-datetimepicker-widget .btn[data-action=\"today\"]::after {\n  content: \"Set the date to today\";\n}\n\n.bootstrap-datetimepicker-widget .picker-switch {\n  text-align: center;\n}\n\n.bootstrap-datetimepicker-widget .picker-switch::after {\n  content: \"Toggle Date and Time Screens\";\n}\n\n.bootstrap-datetimepicker-widget .picker-switch td {\n  padding: 0;\n  margin: 0;\n  height: auto;\n  width: auto;\n  line-height: inherit;\n}\n\n.bootstrap-datetimepicker-widget .picker-switch td span {\n  line-height: 2.5;\n  height: 2.5em;\n  width: 100%;\n}\n\n.bootstrap-datetimepicker-widget table {\n  width: 100%;\n  margin: 0;\n}\n\n.bootstrap-datetimepicker-widget table td, .bootstrap-datetimepicker-widget table th {\n  text-align: center;\n  border-radius: var(--bs-datetimepicker-border-radius, 0.25rem);\n}\n\n.bootstrap-datetimepicker-widget table th {\n  height: 20px;\n  line-height: 20px;\n  width: 20px;\n}\n\n.bootstrap-datetimepicker-widget table th.picker-switch {\n  width: 145px;\n}\n\n.bootstrap-datetimepicker-widget table th.disabled, .bootstrap-datetimepicker-widget table th.disabled:hover {\n  background: none;\n  color: var(--bs-datetimepicker-disabled-color, #6c757d);\n  cursor: not-allowed;\n}\n\n.bootstrap-datetimepicker-widget table th.prev::after {\n  content: \"Previous Month\";\n}\n\n.bootstrap-datetimepicker-widget table th.next::after {\n  content: \"Next Month\";\n}\n\n.bootstrap-datetimepicker-widget table thead tr:first-child th {\n  cursor: pointer;\n}\n\n.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {\n  background: var(--bs-datetimepicker-btn-hover-bg, #e9ecef);\n}\n\n.bootstrap-datetimepicker-widget table td {\n  height: 54px;\n  line-height: 54px;\n  width: 54px;\n}\n\n.bootstrap-datetimepicker-widget table td.cw {\n  font-size: .8em;\n  height: 20px;\n  line-height: 20px;\n  color: var(--bs-datetimepicker-alternate-color, #6c757d);\n}\n\n.bootstrap-datetimepicker-widget table td.day {\n  height: 20px;\n  line-height: 20px;\n  width: 20px;\n}\n\n.bootstrap-datetimepicker-widget table td.day:hover, .bootstrap-datetimepicker-widget table td.hour:hover, .bootstrap-datetimepicker-widget table td.minute:hover, .bootstrap-datetimepicker-widget table td.second:hover {\n  background: var(--bs-datetimepicker-btn-hover-bg, #e9ecef);\n  cursor: pointer;\n}\n\n.bootstrap-datetimepicker-widget table td.old, .bootstrap-datetimepicker-widget table td.new {\n  color: var(--bs-datetimepicker-alternate-color, #6c757d);\n}\n\n.bootstrap-datetimepicker-widget table td.today {\n  position: relative;\n}\n\n.bootstrap-datetimepicker-widget table td.today:before {\n  content: '';\n  display: inline-block;\n  border: solid transparent;\n  border-width: 0 0 7px 7px;\n  border-bottom-color: #714B67;\n  border-top-color: var(--bs-datetimepicker-secondary-border-color-rgba, rgba(0, 0, 0, 0.2));\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n}\n\n.bootstrap-datetimepicker-widget table td.active, .bootstrap-datetimepicker-widget table td.active:hover {\n  background-color: #714B67;\n  color: #fff;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);\n}\n\n.bootstrap-datetimepicker-widget table td.active.today:before {\n  border-bottom-color: #fff;\n}\n\n.bootstrap-datetimepicker-widget table td.disabled, .bootstrap-datetimepicker-widget table td.disabled:hover {\n  background: none;\n  color: var(--bs-datetimepicker-disabled-color, #6c757d);\n  cursor: not-allowed;\n}\n\n.bootstrap-datetimepicker-widget table td span {\n  display: inline-block;\n  width: 54px;\n  height: 54px;\n  line-height: 54px;\n  margin: 2px 1.5px;\n  cursor: pointer;\n  border-radius: var(--bs-datetimepicker-border-radius, 0.25rem);\n}\n\n.bootstrap-datetimepicker-widget table td span:hover {\n  background: var(--bs-datetimepicker-btn-hover-bg, #e9ecef);\n}\n\n.bootstrap-datetimepicker-widget table td span.active {\n  background-color: #714B67;\n  color: #fff;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);\n}\n\n.bootstrap-datetimepicker-widget table td span.old {\n  color: var(--bs-datetimepicker-alternate-color, #6c757d);\n}\n\n.bootstrap-datetimepicker-widget table td span.disabled, .bootstrap-datetimepicker-widget table td span.disabled:hover {\n  background: none;\n  color: var(--bs-datetimepicker-disabled-color, #6c757d);\n  cursor: not-allowed;\n}\n\n.bootstrap-datetimepicker-widget.usetwentyfour td.hour {\n  height: 27px;\n  line-height: 27px;\n}\n\n.input-group [data-toggle=\"datetimepicker\"] {\n  cursor: pointer;\n}\n\n", "\n/* /web/static/lib/jquery.ui/jquery-ui.css */\n/*! jQuery UI - v1.12.1 - 2019-01-18\n* http://jqueryui.com\n* Includes: draggable.css, core.css, resizable.css, selectable.css, sortable.css, autocomplete.css, menu.css, datepicker.css, tooltip.css, theme.css\n* To view and modify this theme, visit http://jqueryui.com/themeroller/?scope=&folderName=base&cornerRadiusShadow=8px&offsetLeftShadow=0px&offsetTopShadow=0px&thicknessShadow=5px&opacityShadow=30&bgImgOpacityShadow=0&bgTextureShadow=flat&bgColorShadow=666666&opacityOverlay=30&bgImgOpacityOverlay=0&bgTextureOverlay=flat&bgColorOverlay=aaaaaa&iconColorError=cc0000&fcError=5f3f3f&borderColorError=f1a899&bgTextureError=flat&bgColorError=fddfdf&iconColorHighlight=777620&fcHighlight=777620&borderColorHighlight=dad55e&bgTextureHighlight=flat&bgColorHighlight=fffa90&iconColorActive=ffffff&fcActive=ffffff&borderColorActive=003eff&bgTextureActive=flat&bgColorActive=007fff&iconColorHover=555555&fcHover=2b2b2b&borderColorHover=cccccc&bgTextureHover=flat&bgColorHover=ededed&iconColorDefault=777777&fcDefault=454545&borderColorDefault=c5c5c5&bgTextureDefault=flat&bgColorDefault=f6f6f6&iconColorContent=444444&fcContent=333333&borderColorContent=dddddd&bgTextureContent=flat&bgColorContent=ffffff&iconColorHeader=444444&fcHeader=333333&borderColorHeader=dddddd&bgTextureHeader=flat&bgColorHeader=e9e9e9&cornerRadius=3px&fwDefault=normal&fsDefault=1em&ffDefault=Arial%2CHelvetica%2Csans-serif\n* Copyright jQuery Foundation and other contributors; Licensed MIT */\n\n.ui-draggable-handle {\n\t-ms-touch-action: none;\n\ttouch-action: none;\n}\n/* Layout helpers\n----------------------------------*/\n.ui-helper-hidden {\n\tdisplay: none;\n}\n.ui-helper-hidden-accessible {\n\tborder: 0;\n\tclip: rect(0 0 0 0);\n\theight: 1px;\n\tmargin: -1px;\n\toverflow: hidden;\n\tpadding: 0;\n\tposition: absolute;\n\twidth: 1px;\n}\n.ui-helper-reset {\n\tmargin: 0;\n\tpadding: 0;\n\tborder: 0;\n\toutline: 0;\n\tline-height: 1.3;\n\ttext-decoration: none;\n\tfont-size: 100%;\n\tlist-style: none;\n}\n.ui-helper-clearfix:before,\n.ui-helper-clearfix:after {\n\tcontent: \"\";\n\tdisplay: table;\n\tborder-collapse: collapse;\n}\n.ui-helper-clearfix:after {\n\tclear: both;\n}\n.ui-helper-zfix {\n\twidth: 100%;\n\theight: 100%;\n\ttop: 0;\n\tleft: 0;\n\tposition: absolute;\n\topacity: 0;\n\tfilter:Alpha(Opacity=0); /* support: IE8 */\n}\n\n.ui-front {\n\tz-index: 100;\n}\n\n\n/* Interaction Cues\n----------------------------------*/\n.ui-state-disabled {\n\tcursor: default !important;\n\tpointer-events: none;\n}\n\n\n/* Icons\n----------------------------------*/\n.ui-icon {\n\tdisplay: inline-block;\n\tvertical-align: middle;\n\tmargin-top: -.25em;\n\tposition: relative;\n\ttext-indent: -99999px;\n\toverflow: hidden;\n\tbackground-repeat: no-repeat;\n}\n\n.ui-widget-icon-block {\n\tleft: 50%;\n\tmargin-left: -8px;\n\tdisplay: block;\n}\n\n/* Misc visuals\n----------------------------------*/\n\n/* Overlays */\n.ui-widget-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n}\n.ui-resizable {\n\tposition: relative;\n}\n.ui-resizable-handle {\n\tposition: absolute;\n\tfont-size: 0.1px;\n\tdisplay: block;\n\t-ms-touch-action: none;\n\ttouch-action: none;\n}\n.ui-resizable-disabled .ui-resizable-handle,\n.ui-resizable-autohide .ui-resizable-handle {\n\tdisplay: none;\n}\n.ui-resizable-n {\n\tcursor: n-resize;\n\theight: 7px;\n\twidth: 100%;\n\ttop: -5px;\n\tleft: 0;\n}\n.ui-resizable-s {\n\tcursor: s-resize;\n\theight: 7px;\n\twidth: 100%;\n\tbottom: -5px;\n\tleft: 0;\n}\n.ui-resizable-e {\n\tcursor: e-resize;\n\twidth: 7px;\n\tright: -5px;\n\ttop: 0;\n\theight: 100%;\n}\n.ui-resizable-w {\n\tcursor: w-resize;\n\twidth: 7px;\n\tleft: -5px;\n\ttop: 0;\n\theight: 100%;\n}\n.ui-resizable-se {\n\tcursor: se-resize;\n\twidth: 12px;\n\theight: 12px;\n\tright: 1px;\n\tbottom: 1px;\n}\n.ui-resizable-sw {\n\tcursor: sw-resize;\n\twidth: 9px;\n\theight: 9px;\n\tleft: -5px;\n\tbottom: -5px;\n}\n.ui-resizable-nw {\n\tcursor: nw-resize;\n\twidth: 9px;\n\theight: 9px;\n\tleft: -5px;\n\ttop: -5px;\n}\n.ui-resizable-ne {\n\tcursor: ne-resize;\n\twidth: 9px;\n\theight: 9px;\n\tright: -5px;\n\ttop: -5px;\n}\n.ui-selectable {\n\t-ms-touch-action: none;\n\ttouch-action: none;\n}\n.ui-selectable-helper {\n\tposition: absolute;\n\tz-index: 100;\n\tborder: 1px dotted black;\n}\n.ui-sortable-handle {\n\t-ms-touch-action: none;\n\ttouch-action: none;\n}\n.ui-autocomplete {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tcursor: default;\n}\n.ui-menu {\n\tlist-style: none;\n\tpadding: 0;\n\tmargin: 0;\n\tdisplay: block;\n\toutline: 0;\n}\n.ui-menu .ui-menu {\n\tposition: absolute;\n}\n.ui-menu .ui-menu-item {\n\tmargin: 0;\n\tcursor: pointer;\n\t/* support: IE10, see #8844 */\n\tlist-style-image: url(\"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\");\n}\n.ui-menu .ui-menu-item-wrapper {\n\tposition: relative;\n\tpadding: 3px 1em 3px .4em;\n}\n.ui-menu .ui-menu-divider {\n\tmargin: 5px 0;\n\theight: 0;\n\tfont-size: 0;\n\tline-height: 0;\n\tborder-width: 1px 0 0 0;\n}\n.ui-menu .ui-state-focus,\n.ui-menu .ui-state-active {\n\tmargin: -1px;\n}\n\n/* icon support */\n.ui-menu-icons {\n\tposition: relative;\n}\n.ui-menu-icons .ui-menu-item-wrapper {\n\tpadding-left: 2em;\n}\n\n/* left-aligned */\n.ui-menu .ui-icon {\n\tposition: absolute;\n\ttop: 0;\n\tbottom: 0;\n\tleft: .2em;\n\tmargin: auto 0;\n}\n\n/* right-aligned */\n.ui-menu .ui-menu-icon {\n\tleft: auto;\n\tright: 0;\n}\n.ui-datepicker {\n\twidth: 17em;\n\tpadding: .2em .2em 0;\n\tdisplay: none;\n}\n.ui-datepicker .ui-datepicker-header {\n\tposition: relative;\n\tpadding: .2em 0;\n}\n.ui-datepicker .ui-datepicker-prev,\n.ui-datepicker .ui-datepicker-next {\n\tposition: absolute;\n\ttop: 2px;\n\twidth: 1.8em;\n\theight: 1.8em;\n}\n.ui-datepicker .ui-datepicker-prev-hover,\n.ui-datepicker .ui-datepicker-next-hover {\n\ttop: 1px;\n}\n.ui-datepicker .ui-datepicker-prev {\n\tleft: 2px;\n}\n.ui-datepicker .ui-datepicker-next {\n\tright: 2px;\n}\n.ui-datepicker .ui-datepicker-prev-hover {\n\tleft: 1px;\n}\n.ui-datepicker .ui-datepicker-next-hover {\n\tright: 1px;\n}\n.ui-datepicker .ui-datepicker-prev span,\n.ui-datepicker .ui-datepicker-next span {\n\tdisplay: block;\n\tposition: absolute;\n\tleft: 50%;\n\tmargin-left: -8px;\n\ttop: 50%;\n\tmargin-top: -8px;\n}\n.ui-datepicker .ui-datepicker-title {\n\tmargin: 0 2.3em;\n\tline-height: 1.8em;\n\ttext-align: center;\n}\n.ui-datepicker .ui-datepicker-title select {\n\tfont-size: 1em;\n\tmargin: 1px 0;\n}\n.ui-datepicker select.ui-datepicker-month,\n.ui-datepicker select.ui-datepicker-year {\n\twidth: 45%;\n}\n.ui-datepicker table {\n\twidth: 100%;\n\tfont-size: .9em;\n\tborder-collapse: collapse;\n\tmargin: 0 0 .4em;\n}\n.ui-datepicker th {\n\tpadding: .7em .3em;\n\ttext-align: center;\n\tfont-weight: bold;\n\tborder: 0;\n}\n.ui-datepicker td {\n\tborder: 0;\n\tpadding: 1px;\n}\n.ui-datepicker td span,\n.ui-datepicker td a {\n\tdisplay: block;\n\tpadding: .2em;\n\ttext-align: right;\n\ttext-decoration: none;\n}\n.ui-datepicker .ui-datepicker-buttonpane {\n\tbackground-image: none;\n\tmargin: .7em 0 0 0;\n\tpadding: 0 .2em;\n\tborder-left: 0;\n\tborder-right: 0;\n\tborder-bottom: 0;\n}\n.ui-datepicker .ui-datepicker-buttonpane button {\n\tfloat: right;\n\tmargin: .5em .2em .4em;\n\tcursor: pointer;\n\tpadding: .2em .6em .3em .6em;\n\twidth: auto;\n\toverflow: visible;\n}\n.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {\n\tfloat: left;\n}\n\n/* with multiple calendars */\n.ui-datepicker.ui-datepicker-multi {\n\twidth: auto;\n}\n.ui-datepicker-multi .ui-datepicker-group {\n\tfloat: left;\n}\n.ui-datepicker-multi .ui-datepicker-group table {\n\twidth: 95%;\n\tmargin: 0 auto .4em;\n}\n.ui-datepicker-multi-2 .ui-datepicker-group {\n\twidth: 50%;\n}\n.ui-datepicker-multi-3 .ui-datepicker-group {\n\twidth: 33.3%;\n}\n.ui-datepicker-multi-4 .ui-datepicker-group {\n\twidth: 25%;\n}\n.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,\n.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {\n\tborder-left-width: 0;\n}\n.ui-datepicker-multi .ui-datepicker-buttonpane {\n\tclear: left;\n}\n.ui-datepicker-row-break {\n\tclear: both;\n\twidth: 100%;\n\tfont-size: 0;\n}\n\n/* RTL support */\n.ui-datepicker-rtl {\n\tdirection: rtl;\n}\n.ui-datepicker-rtl .ui-datepicker-prev {\n\tright: 2px;\n\tleft: auto;\n}\n.ui-datepicker-rtl .ui-datepicker-next {\n\tleft: 2px;\n\tright: auto;\n}\n.ui-datepicker-rtl .ui-datepicker-prev:hover {\n\tright: 1px;\n\tleft: auto;\n}\n.ui-datepicker-rtl .ui-datepicker-next:hover {\n\tleft: 1px;\n\tright: auto;\n}\n.ui-datepicker-rtl .ui-datepicker-buttonpane {\n\tclear: right;\n}\n.ui-datepicker-rtl .ui-datepicker-buttonpane button {\n\tfloat: left;\n}\n.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,\n.ui-datepicker-rtl .ui-datepicker-group {\n\tfloat: right;\n}\n.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,\n.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {\n\tborder-right-width: 0;\n\tborder-left-width: 1px;\n}\n\n/* Icons */\n.ui-datepicker .ui-icon {\n\tdisplay: block;\n\ttext-indent: -99999px;\n\toverflow: hidden;\n\tbackground-repeat: no-repeat;\n\tleft: .5em;\n\ttop: .3em;\n}\n.ui-tooltip {\n\tpadding: 8px;\n\tposition: absolute;\n\tz-index: 9999;\n\tmax-width: 300px;\n}\nbody .ui-tooltip {\n\tborder-width: 2px;\n}\n\n/* Component containers\n----------------------------------*/\n.ui-widget {\n\tfont-family: Arial,Helvetica,sans-serif;\n\tfont-size: 1em;\n}\n.ui-widget .ui-widget {\n\tfont-size: 1em;\n}\n.ui-widget input,\n.ui-widget select,\n.ui-widget textarea,\n.ui-widget button {\n\tfont-family: Arial,Helvetica,sans-serif;\n\tfont-size: 1em;\n}\n.ui-widget.ui-widget-content {\n\tborder: 1px solid #c5c5c5;\n}\n.ui-widget-content {\n\tborder: 1px solid #dddddd;\n\tbackground: #ffffff;\n\tcolor: #333333;\n}\n.ui-widget-content a {\n\tcolor: #333333;\n}\n.ui-widget-header {\n\tborder: 1px solid #dddddd;\n\tbackground: #e9e9e9;\n\tcolor: #333333;\n\tfont-weight: bold;\n}\n.ui-widget-header a {\n\tcolor: #333333;\n}\n\n/* Interaction states\n----------------------------------*/\n.ui-state-default,\n.ui-widget-content .ui-state-default,\n.ui-widget-header .ui-state-default,\n.ui-button,\n\n/* We use html here because we need a greater specificity to make sure disabled\nworks properly when clicked or hovered */\nhtml .ui-button.ui-state-disabled:hover,\nhtml .ui-button.ui-state-disabled:active {\n\tborder: 1px solid #c5c5c5;\n\tbackground: #f6f6f6;\n\tfont-weight: normal;\n\tcolor: #454545;\n}\n.ui-state-default a,\n.ui-state-default a:link,\n.ui-state-default a:visited,\na.ui-button,\na:link.ui-button,\na:visited.ui-button,\n.ui-button {\n\tcolor: #454545;\n\ttext-decoration: none;\n}\n.ui-state-hover,\n.ui-widget-content .ui-state-hover,\n.ui-widget-header .ui-state-hover,\n.ui-state-focus,\n.ui-widget-content .ui-state-focus,\n.ui-widget-header .ui-state-focus,\n.ui-button:hover,\n.ui-button:focus {\n\tborder: 1px solid #cccccc;\n\tbackground: #ededed;\n\tfont-weight: normal;\n\tcolor: #2b2b2b;\n}\n.ui-state-hover a,\n.ui-state-hover a:hover,\n.ui-state-hover a:link,\n.ui-state-hover a:visited,\n.ui-state-focus a,\n.ui-state-focus a:hover,\n.ui-state-focus a:link,\n.ui-state-focus a:visited,\na.ui-button:hover,\na.ui-button:focus {\n\tcolor: #2b2b2b;\n\ttext-decoration: none;\n}\n\n.ui-visual-focus {\n\tbox-shadow: 0 0 3px 1px rgb(94, 158, 214);\n}\n.ui-state-active,\n.ui-widget-content .ui-state-active,\n.ui-widget-header .ui-state-active,\na.ui-button:active,\n.ui-button:active,\n.ui-button.ui-state-active:hover {\n\tborder: 1px solid #003eff;\n\tbackground: #007fff;\n\tfont-weight: normal;\n\tcolor: #ffffff;\n}\n.ui-icon-background,\n.ui-state-active .ui-icon-background {\n\tborder: #003eff;\n\tbackground-color: #ffffff;\n}\n.ui-state-active a,\n.ui-state-active a:link,\n.ui-state-active a:visited {\n\tcolor: #ffffff;\n\ttext-decoration: none;\n}\n\n/* Interaction Cues\n----------------------------------*/\n.ui-state-highlight,\n.ui-widget-content .ui-state-highlight,\n.ui-widget-header .ui-state-highlight {\n\tborder: 1px solid #dad55e;\n\tbackground: #fffa90;\n\tcolor: #777620;\n}\n.ui-state-checked {\n\tborder: 1px solid #dad55e;\n\tbackground: #fffa90;\n}\n.ui-state-highlight a,\n.ui-widget-content .ui-state-highlight a,\n.ui-widget-header .ui-state-highlight a {\n\tcolor: #777620;\n}\n.ui-state-error,\n.ui-widget-content .ui-state-error,\n.ui-widget-header .ui-state-error {\n\tborder: 1px solid #f1a899;\n\tbackground: #fddfdf;\n\tcolor: #5f3f3f;\n}\n.ui-state-error a,\n.ui-widget-content .ui-state-error a,\n.ui-widget-header .ui-state-error a {\n\tcolor: #5f3f3f;\n}\n.ui-state-error-text,\n.ui-widget-content .ui-state-error-text,\n.ui-widget-header .ui-state-error-text {\n\tcolor: #5f3f3f;\n}\n.ui-priority-primary,\n.ui-widget-content .ui-priority-primary,\n.ui-widget-header .ui-priority-primary {\n\tfont-weight: bold;\n}\n.ui-priority-secondary,\n.ui-widget-content .ui-priority-secondary,\n.ui-widget-header .ui-priority-secondary {\n\topacity: .7;\n\tfilter:Alpha(Opacity=70); /* support: IE8 */\n\tfont-weight: normal;\n}\n.ui-state-disabled,\n.ui-widget-content .ui-state-disabled,\n.ui-widget-header .ui-state-disabled {\n\topacity: .35;\n\tfilter:Alpha(Opacity=35); /* support: IE8 */\n\tbackground-image: none;\n}\n.ui-state-disabled .ui-icon {\n\tfilter:Alpha(Opacity=35); /* support: IE8 - See #6059 */\n}\n\n/* Icons\n----------------------------------*/\n\n/* states and images */\n.ui-icon {\n\twidth: 16px;\n\theight: 16px;\n}\n.ui-icon,\n.ui-widget-content .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_444444_256x240.png\");\n}\n.ui-widget-header .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_444444_256x240.png\");\n}\n.ui-state-hover .ui-icon,\n.ui-state-focus .ui-icon,\n.ui-button:hover .ui-icon,\n.ui-button:focus .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_555555_256x240.png\");\n}\n.ui-state-active .ui-icon,\n.ui-button:active .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_ffffff_256x240.png\");\n}\n.ui-state-highlight .ui-icon,\n.ui-button .ui-state-highlight.ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_777620_256x240.png\");\n}\n.ui-state-error .ui-icon,\n.ui-state-error-text .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_cc0000_256x240.png\");\n}\n.ui-button .ui-icon {\n\tbackground-image: url(\"/web/static/lib/jquery.ui/images/ui-icons_777777_256x240.png\");\n}\n\n/* positioning */\n.ui-icon-blank { background-position: 16px 16px; }\n.ui-icon-caret-1-n { background-position: 0 0; }\n.ui-icon-caret-1-ne { background-position: -16px 0; }\n.ui-icon-caret-1-e { background-position: -32px 0; }\n.ui-icon-caret-1-se { background-position: -48px 0; }\n.ui-icon-caret-1-s { background-position: -65px 0; }\n.ui-icon-caret-1-sw { background-position: -80px 0; }\n.ui-icon-caret-1-w { background-position: -96px 0; }\n.ui-icon-caret-1-nw { background-position: -112px 0; }\n.ui-icon-caret-2-n-s { background-position: -128px 0; }\n.ui-icon-caret-2-e-w { background-position: -144px 0; }\n.ui-icon-triangle-1-n { background-position: 0 -16px; }\n.ui-icon-triangle-1-ne { background-position: -16px -16px; }\n.ui-icon-triangle-1-e { background-position: -32px -16px; }\n.ui-icon-triangle-1-se { background-position: -48px -16px; }\n.ui-icon-triangle-1-s { background-position: -65px -16px; }\n.ui-icon-triangle-1-sw { background-position: -80px -16px; }\n.ui-icon-triangle-1-w { background-position: -96px -16px; }\n.ui-icon-triangle-1-nw { background-position: -112px -16px; }\n.ui-icon-triangle-2-n-s { background-position: -128px -16px; }\n.ui-icon-triangle-2-e-w { background-position: -144px -16px; }\n.ui-icon-arrow-1-n { background-position: 0 -32px; }\n.ui-icon-arrow-1-ne { background-position: -16px -32px; }\n.ui-icon-arrow-1-e { background-position: -32px -32px; }\n.ui-icon-arrow-1-se { background-position: -48px -32px; }\n.ui-icon-arrow-1-s { background-position: -65px -32px; }\n.ui-icon-arrow-1-sw { background-position: -80px -32px; }\n.ui-icon-arrow-1-w { background-position: -96px -32px; }\n.ui-icon-arrow-1-nw { background-position: -112px -32px; }\n.ui-icon-arrow-2-n-s { background-position: -128px -32px; }\n.ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }\n.ui-icon-arrow-2-e-w { background-position: -160px -32px; }\n.ui-icon-arrow-2-se-nw { background-position: -176px -32px; }\n.ui-icon-arrowstop-1-n { background-position: -192px -32px; }\n.ui-icon-arrowstop-1-e { background-position: -208px -32px; }\n.ui-icon-arrowstop-1-s { background-position: -224px -32px; }\n.ui-icon-arrowstop-1-w { background-position: -240px -32px; }\n.ui-icon-arrowthick-1-n { background-position: 1px -48px; }\n.ui-icon-arrowthick-1-ne { background-position: -16px -48px; }\n.ui-icon-arrowthick-1-e { background-position: -32px -48px; }\n.ui-icon-arrowthick-1-se { background-position: -48px -48px; }\n.ui-icon-arrowthick-1-s { background-position: -64px -48px; }\n.ui-icon-arrowthick-1-sw { background-position: -80px -48px; }\n.ui-icon-arrowthick-1-w { background-position: -96px -48px; }\n.ui-icon-arrowthick-1-nw { background-position: -112px -48px; }\n.ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }\n.ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }\n.ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }\n.ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }\n.ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }\n.ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }\n.ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }\n.ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }\n.ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }\n.ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }\n.ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }\n.ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }\n.ui-icon-arrowreturn-1-w { background-position: -64px -64px; }\n.ui-icon-arrowreturn-1-n { background-position: -80px -64px; }\n.ui-icon-arrowreturn-1-e { background-position: -96px -64px; }\n.ui-icon-arrowreturn-1-s { background-position: -112px -64px; }\n.ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }\n.ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }\n.ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }\n.ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }\n.ui-icon-arrow-4 { background-position: 0 -80px; }\n.ui-icon-arrow-4-diag { background-position: -16px -80px; }\n.ui-icon-extlink { background-position: -32px -80px; }\n.ui-icon-newwin { background-position: -48px -80px; }\n.ui-icon-refresh { background-position: -64px -80px; }\n.ui-icon-shuffle { background-position: -80px -80px; }\n.ui-icon-transfer-e-w { background-position: -96px -80px; }\n.ui-icon-transferthick-e-w { background-position: -112px -80px; }\n.ui-icon-folder-collapsed { background-position: 0 -96px; }\n.ui-icon-folder-open { background-position: -16px -96px; }\n.ui-icon-document { background-position: -32px -96px; }\n.ui-icon-document-b { background-position: -48px -96px; }\n.ui-icon-note { background-position: -64px -96px; }\n.ui-icon-mail-closed { background-position: -80px -96px; }\n.ui-icon-mail-open { background-position: -96px -96px; }\n.ui-icon-suitcase { background-position: -112px -96px; }\n.ui-icon-comment { background-position: -128px -96px; }\n.ui-icon-person { background-position: -144px -96px; }\n.ui-icon-print { background-position: -160px -96px; }\n.ui-icon-trash { background-position: -176px -96px; }\n.ui-icon-locked { background-position: -192px -96px; }\n.ui-icon-unlocked { background-position: -208px -96px; }\n.ui-icon-bookmark { background-position: -224px -96px; }\n.ui-icon-tag { background-position: -240px -96px; }\n.ui-icon-home { background-position: 0 -112px; }\n.ui-icon-flag { background-position: -16px -112px; }\n.ui-icon-calendar { background-position: -32px -112px; }\n.ui-icon-cart { background-position: -48px -112px; }\n.ui-icon-pencil { background-position: -64px -112px; }\n.ui-icon-clock { background-position: -80px -112px; }\n.ui-icon-disk { background-position: -96px -112px; }\n.ui-icon-calculator { background-position: -112px -112px; }\n.ui-icon-zoomin { background-position: -128px -112px; }\n.ui-icon-zoomout { background-position: -144px -112px; }\n.ui-icon-search { background-position: -160px -112px; }\n.ui-icon-wrench { background-position: -176px -112px; }\n.ui-icon-gear { background-position: -192px -112px; }\n.ui-icon-heart { background-position: -208px -112px; }\n.ui-icon-star { background-position: -224px -112px; }\n.ui-icon-link { background-position: -240px -112px; }\n.ui-icon-cancel { background-position: 0 -128px; }\n.ui-icon-plus { background-position: -16px -128px; }\n.ui-icon-plusthick { background-position: -32px -128px; }\n.ui-icon-minus { background-position: -48px -128px; }\n.ui-icon-minusthick { background-position: -64px -128px; }\n.ui-icon-close { background-position: -80px -128px; }\n.ui-icon-closethick { background-position: -96px -128px; }\n.ui-icon-key { background-position: -112px -128px; }\n.ui-icon-lightbulb { background-position: -128px -128px; }\n.ui-icon-scissors { background-position: -144px -128px; }\n.ui-icon-clipboard { background-position: -160px -128px; }\n.ui-icon-copy { background-position: -176px -128px; }\n.ui-icon-contact { background-position: -192px -128px; }\n.ui-icon-image { background-position: -208px -128px; }\n.ui-icon-video { background-position: -224px -128px; }\n.ui-icon-script { background-position: -240px -128px; }\n.ui-icon-alert { background-position: 0 -144px; }\n.ui-icon-info { background-position: -16px -144px; }\n.ui-icon-notice { background-position: -32px -144px; }\n.ui-icon-help { background-position: -48px -144px; }\n.ui-icon-check { background-position: -64px -144px; }\n.ui-icon-bullet { background-position: -80px -144px; }\n.ui-icon-radio-on { background-position: -96px -144px; }\n.ui-icon-radio-off { background-position: -112px -144px; }\n.ui-icon-pin-w { background-position: -128px -144px; }\n.ui-icon-pin-s { background-position: -144px -144px; }\n.ui-icon-play { background-position: 0 -160px; }\n.ui-icon-pause { background-position: -16px -160px; }\n.ui-icon-seek-next { background-position: -32px -160px; }\n.ui-icon-seek-prev { background-position: -48px -160px; }\n.ui-icon-seek-end { background-position: -64px -160px; }\n.ui-icon-seek-start { background-position: -80px -160px; }\n/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */\n.ui-icon-seek-first { background-position: -80px -160px; }\n.ui-icon-stop { background-position: -96px -160px; }\n.ui-icon-eject { background-position: -112px -160px; }\n.ui-icon-volume-off { background-position: -128px -160px; }\n.ui-icon-volume-on { background-position: -144px -160px; }\n.ui-icon-power { background-position: 0 -176px; }\n.ui-icon-signal-diag { background-position: -16px -176px; }\n.ui-icon-signal { background-position: -32px -176px; }\n.ui-icon-battery-0 { background-position: -48px -176px; }\n.ui-icon-battery-1 { background-position: -64px -176px; }\n.ui-icon-battery-2 { background-position: -80px -176px; }\n.ui-icon-battery-3 { background-position: -96px -176px; }\n.ui-icon-circle-plus { background-position: 0 -192px; }\n.ui-icon-circle-minus { background-position: -16px -192px; }\n.ui-icon-circle-close { background-position: -32px -192px; }\n.ui-icon-circle-triangle-e { background-position: -48px -192px; }\n.ui-icon-circle-triangle-s { background-position: -64px -192px; }\n.ui-icon-circle-triangle-w { background-position: -80px -192px; }\n.ui-icon-circle-triangle-n { background-position: -96px -192px; }\n.ui-icon-circle-arrow-e { background-position: -112px -192px; }\n.ui-icon-circle-arrow-s { background-position: -128px -192px; }\n.ui-icon-circle-arrow-w { background-position: -144px -192px; }\n.ui-icon-circle-arrow-n { background-position: -160px -192px; }\n.ui-icon-circle-zoomin { background-position: -176px -192px; }\n.ui-icon-circle-zoomout { background-position: -192px -192px; }\n.ui-icon-circle-check { background-position: -208px -192px; }\n.ui-icon-circlesmall-plus { background-position: 0 -208px; }\n.ui-icon-circlesmall-minus { background-position: -16px -208px; }\n.ui-icon-circlesmall-close { background-position: -32px -208px; }\n.ui-icon-squaresmall-plus { background-position: -48px -208px; }\n.ui-icon-squaresmall-minus { background-position: -64px -208px; }\n.ui-icon-squaresmall-close { background-position: -80px -208px; }\n.ui-icon-grip-dotted-vertical { background-position: 0 -224px; }\n.ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }\n.ui-icon-grip-solid-vertical { background-position: -32px -224px; }\n.ui-icon-grip-solid-horizontal { background-position: -48px -224px; }\n.ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }\n.ui-icon-grip-diagonal-se { background-position: -80px -224px; }\n\n\n/* Misc visuals\n----------------------------------*/\n\n/* Corner radius */\n.ui-corner-all,\n.ui-corner-top,\n.ui-corner-left,\n.ui-corner-tl {\n\tborder-top-left-radius: 3px;\n}\n.ui-corner-all,\n.ui-corner-top,\n.ui-corner-right,\n.ui-corner-tr {\n\tborder-top-right-radius: 3px;\n}\n.ui-corner-all,\n.ui-corner-bottom,\n.ui-corner-left,\n.ui-corner-bl {\n\tborder-bottom-left-radius: 3px;\n}\n.ui-corner-all,\n.ui-corner-bottom,\n.ui-corner-right,\n.ui-corner-br {\n\tborder-bottom-right-radius: 3px;\n}\n\n/* Overlays */\n.ui-widget-overlay {\n\tbackground: #aaaaaa;\n\topacity: .3;\n\tfilter: Alpha(Opacity=30); /* support: IE8 */\n}\n.ui-widget-shadow {\n\t-webkit-box-shadow: 0px 0px 5px #666666;\n\tbox-shadow: 0px 0px 5px #666666;\n}\n", "\n/* /web/static/src/libs/fontawesome/css/font-awesome.css */\n/*!\n *  Based on Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome\n *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)\n */\n/* FONT PATH\n * -------------------------- */\n@font-face {\n  font-family: 'FontAwesome';\n  src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff');\n  font-weight: normal;\n  font-style: normal;\n  font-display: block;\n}\n.fa {\n  display: inline-block;\n  font: normal normal normal 14px/1 FontAwesome;\n  font-size: inherit;\n  text-rendering: auto;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n/* matches 'oi-large' utility-class */\n.fa-lg {\n  font-size: 1.315em;\n  vertical-align: -6%;\n}\n.fa-2x {\n  font-size: 2em;\n}\n.fa-3x {\n  font-size: 3em;\n}\n.fa-4x {\n  font-size: 4em;\n}\n.fa-5x {\n  font-size: 5em;\n}\n.fa-fw {\n  width: 1.28571429em;\n  text-align: center;\n}\n.fa-ul {\n  padding-left: 0;\n  margin-left: 2.14285714em;\n  list-style-type: none;\n}\n.fa-ul > li {\n  position: relative;\n}\n.fa-li {\n  position: absolute;\n  left: -2.14285714em;\n  width: 2.14285714em;\n  top: 0.14285714em;\n  text-align: center;\n}\n.fa-li.fa-lg {\n  left: -1.85714286em;\n}\n.fa-border {\n  padding: .2em .25em .15em;\n  border: solid 0.08em #eeeeee;\n  border-radius: .1em;\n}\n.fa-pull-left {\n  float: left;\n}\n.fa-pull-right {\n  float: right;\n}\n.fa.fa-pull-left {\n  margin-right: .3em;\n}\n.fa.fa-pull-right {\n  margin-left: .3em;\n}\n.fa-spin {\n  animation: fa-spin 2s infinite linear;\n}\n.fa-pulse {\n  animation: fa-spin 1s infinite steps(8);\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(359deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical {\n  filter: none;\n}\n.fa-stack {\n  position: relative;\n  display: inline-block;\n  width: 2em;\n  height: 2em;\n  line-height: 2em;\n  vertical-align: middle;\n}\n.fa-stack-1x,\n.fa-stack-2x {\n  position: absolute;\n  left: 0;\n  width: 100%;\n  text-align: center;\n}\n.fa-stack-1x {\n  line-height: inherit;\n}\n.fa-stack-2x {\n  font-size: 2em;\n}\n.fa-inverse {\n  color: #ffffff;\n}\n/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen\n   readers do not read off random characters that represent icons */\n.fa-glass:before {\n  content: \"\\f000\";\n}\n.fa-music:before {\n  content: \"\\f001\";\n}\n.fa-search:before {\n  content: \"\\f002\";\n}\n.fa-envelope-o:before {\n  content: \"\\f003\";\n}\n.fa-heart:before {\n  content: \"\\f004\";\n}\n.fa-star:before {\n  content: \"\\f005\";\n}\n.fa-star-o:before {\n  content: \"\\f006\";\n}\n.fa-user:before {\n  content: \"\\f007\";\n}\n.fa-film:before {\n  content: \"\\f008\";\n}\n.fa-th-large:before {\n  content: \"\\f009\";\n}\n.fa-th:before {\n  content: \"\\f00a\";\n}\n.fa-th-list:before {\n  content: \"\\f00b\";\n}\n.fa-check:before {\n  content: \"\\f00c\";\n}\n.fa-remove:before,\n.fa-close:before,\n.fa-times:before {\n  content: \"\\f00d\";\n}\n.fa-search-plus:before {\n  content: \"\\f00e\";\n}\n.fa-search-minus:before {\n  content: \"\\f010\";\n}\n.fa-power-off:before {\n  content: \"\\f011\";\n}\n.fa-signal:before {\n  content: \"\\f012\";\n}\n.fa-gear:before,\n.fa-cog:before {\n  content: \"\\f013\";\n}\n.fa-trash-o:before {\n  content: \"\\f014\";\n}\n.fa-home:before {\n  content: \"\\f015\";\n}\n.fa-file-o:before {\n  content: \"\\f016\";\n}\n.fa-clock-o:before {\n  content: \"\\f017\";\n}\n.fa-road:before {\n  content: \"\\f018\";\n}\n.fa-download:before {\n  content: \"\\f019\";\n}\n.fa-arrow-circle-o-down:before {\n  content: \"\\f01a\";\n}\n.fa-arrow-circle-o-up:before {\n  content: \"\\f01b\";\n}\n.fa-inbox:before {\n  content: \"\\f01c\";\n}\n.fa-play-circle-o:before {\n  content: \"\\f01d\";\n}\n.fa-rotate-right:before,\n.fa-repeat:before {\n  content: \"\\f01e\";\n}\n.fa-refresh:before {\n  content: \"\\f021\";\n}\n.fa-list-alt:before {\n  content: \"\\f022\";\n}\n.fa-lock:before {\n  content: \"\\f023\";\n}\n.fa-flag:before {\n  content: \"\\f024\";\n}\n.fa-headphones:before {\n  content: \"\\f025\";\n}\n.fa-volume-off:before {\n  content: \"\\f026\";\n}\n.fa-volume-down:before {\n  content: \"\\f027\";\n}\n.fa-volume-up:before {\n  content: \"\\f028\";\n}\n.fa-qrcode:before {\n  content: \"\\f029\";\n}\n.fa-barcode:before {\n  content: \"\\f02a\";\n}\n.fa-tag:before {\n  content: \"\\f02b\";\n}\n.fa-tags:before {\n  content: \"\\f02c\";\n}\n.fa-book:before {\n  content: \"\\f02d\";\n}\n.fa-bookmark:before {\n  content: \"\\f02e\";\n}\n.fa-print:before {\n  content: \"\\f02f\";\n}\n.fa-camera:before {\n  content: \"\\f030\";\n}\n.fa-font:before {\n  content: \"\\f031\";\n}\n.fa-bold:before {\n  content: \"\\f032\";\n}\n.fa-italic:before {\n  content: \"\\f033\";\n}\n.fa-text-height:before {\n  content: \"\\f034\";\n}\n.fa-text-width:before {\n  content: \"\\f035\";\n}\n.fa-align-left:before {\n  content: \"\\f036\";\n}\n.fa-align-center:before {\n  content: \"\\f037\";\n}\n.fa-align-right:before {\n  content: \"\\f038\";\n}\n.fa-align-justify:before {\n  content: \"\\f039\";\n}\n.fa-list:before {\n  content: \"\\f03a\";\n}\n.fa-dedent:before,\n.fa-outdent:before {\n  content: \"\\f03b\";\n}\n.fa-indent:before {\n  content: \"\\f03c\";\n}\n.fa-video-camera:before {\n  content: \"\\f03d\";\n}\n.fa-photo:before,\n.fa-image:before,\n.fa-picture-o:before {\n  content: \"\\f03e\";\n}\n.fa-pencil:before {\n  content: \"\\f040\";\n}\n.fa-map-marker:before {\n  content: \"\\f041\";\n}\n.fa-adjust:before {\n  content: \"\\f042\";\n}\n.fa-tint:before {\n  content: \"\\f043\";\n}\n.fa-edit:before,\n.fa-pencil-square-o:before {\n  content: \"\\f044\";\n}\n.fa-share-square-o:before {\n  content: \"\\f045\";\n}\n.fa-check-square-o:before {\n  content: \"\\f046\";\n}\n.fa-arrows:before {\n  content: \"\\f047\";\n}\n.fa-step-backward:before {\n  content: \"\\f048\";\n}\n.fa-fast-backward:before {\n  content: \"\\f049\";\n}\n.fa-backward:before {\n  content: \"\\f04a\";\n}\n.fa-play:before {\n  content: \"\\f04b\";\n}\n.fa-pause:before {\n  content: \"\\f04c\";\n}\n.fa-stop:before {\n  content: \"\\f04d\";\n}\n.fa-forward:before {\n  content: \"\\f04e\";\n}\n.fa-fast-forward:before {\n  content: \"\\f050\";\n}\n.fa-step-forward:before {\n  content: \"\\f051\";\n}\n.fa-eject:before {\n  content: \"\\f052\";\n}\n.fa-chevron-left:before {\n  content: \"\\f053\";\n}\n.fa-chevron-right:before {\n  content: \"\\f054\";\n}\n.fa-plus-circle:before {\n  content: \"\\f055\";\n}\n.fa-minus-circle:before {\n  content: \"\\f056\";\n}\n.fa-times-circle:before {\n  content: \"\\f057\";\n}\n.fa-check-circle:before {\n  content: \"\\f058\";\n}\n.fa-question-circle:before {\n  content: \"\\f059\";\n}\n.fa-info-circle:before {\n  content: \"\\f05a\";\n}\n.fa-crosshairs:before {\n  content: \"\\f05b\";\n}\n.fa-times-circle-o:before {\n  content: \"\\f05c\";\n}\n.fa-check-circle-o:before {\n  content: \"\\f05d\";\n}\n.fa-ban:before {\n  content: \"\\f05e\";\n}\n.fa-arrow-left:before {\n  content: \"\\f060\";\n}\n.fa-arrow-right:before {\n  content: \"\\f061\";\n}\n.fa-arrow-up:before {\n  content: \"\\f062\";\n}\n.fa-arrow-down:before {\n  content: \"\\f063\";\n}\n.fa-mail-forward:before,\n.fa-share:before {\n  content: \"\\f064\";\n}\n.fa-expand:before {\n  content: \"\\f065\";\n}\n.fa-compress:before {\n  content: \"\\f066\";\n}\n.fa-plus:before {\n  content: \"\\f067\";\n}\n.fa-minus:before {\n  content: \"\\f068\";\n}\n.fa-asterisk:before {\n  content: \"\\f069\";\n}\n.fa-exclamation-circle:before {\n  content: \"\\f06a\";\n}\n.fa-gift:before {\n  content: \"\\f06b\";\n}\n.fa-leaf:before {\n  content: \"\\f06c\";\n}\n.fa-fire:before {\n  content: \"\\f06d\";\n}\n.fa-eye:before {\n  content: \"\\f06e\";\n}\n.fa-eye-slash:before {\n  content: \"\\f070\";\n}\n.fa-warning:before,\n.fa-exclamation-triangle:before {\n  content: \"\\f071\";\n}\n.fa-plane:before {\n  content: \"\\f072\";\n}\n.fa-calendar:before {\n  content: \"\\f073\";\n}\n.fa-random:before {\n  content: \"\\f074\";\n}\n.fa-comment:before {\n  content: \"\\f075\";\n}\n.fa-magnet:before {\n  content: \"\\f076\";\n}\n.fa-chevron-up:before {\n  content: \"\\f077\";\n}\n.fa-chevron-down:before {\n  content: \"\\f078\";\n}\n.fa-retweet:before {\n  content: \"\\f079\";\n}\n.fa-shopping-cart:before {\n  content: \"\\f07a\";\n}\n.fa-folder:before {\n  content: \"\\f07b\";\n}\n.fa-folder-open:before {\n  content: \"\\f07c\";\n}\n.fa-arrows-v:before {\n  content: \"\\f07d\";\n}\n.fa-arrows-h:before {\n  content: \"\\f07e\";\n}\n.fa-bar-chart-o:before,\n.fa-bar-chart:before {\n  content: \"\\f080\";\n}\n.fa-twitter-square:before {\n  content: \"\\f081\";\n}\n.fa-facebook-square:before {\n  content: \"\\f082\";\n}\n.fa-camera-retro:before {\n  content: \"\\f083\";\n}\n.fa-key:before {\n  content: \"\\f084\";\n}\n.fa-gears:before,\n.fa-cogs:before {\n  content: \"\\f085\";\n}\n.fa-comments:before {\n  content: \"\\f086\";\n}\n.fa-thumbs-o-up:before {\n  content: \"\\f087\";\n}\n.fa-thumbs-o-down:before {\n  content: \"\\f088\";\n}\n.fa-star-half:before {\n  content: \"\\f089\";\n}\n.fa-heart-o:before {\n  content: \"\\f08a\";\n}\n.fa-sign-out:before {\n  content: \"\\f08b\";\n}\n.fa-linkedin-square:before {\n  content: \"\\f08c\";\n}\n.fa-thumb-tack:before {\n  content: \"\\f08d\";\n}\n.fa-external-link:before {\n  content: \"\\f08e\";\n}\n.fa-sign-in:before {\n  content: \"\\f090\";\n}\n.fa-trophy:before {\n  content: \"\\f091\";\n}\n.fa-github-square:before {\n  content: \"\\f092\";\n}\n.fa-upload:before {\n  content: \"\\f093\";\n}\n.fa-lemon-o:before {\n  content: \"\\f094\";\n}\n.fa-phone:before {\n  content: \"\\f095\";\n}\n.fa-square-o:before {\n  content: \"\\f096\";\n}\n.fa-bookmark-o:before {\n  content: \"\\f097\";\n}\n.fa-phone-square:before {\n  content: \"\\f098\";\n}\n.fa-twitter:before {\n  content: \"\\f099\";\n}\n.fa-facebook-f:before,\n.fa-facebook:before {\n  content: \"\\f09a\";\n}\n.fa-github:before {\n  content: \"\\f09b\";\n}\n.fa-unlock:before {\n  content: \"\\f09c\";\n}\n.fa-credit-card:before {\n  content: \"\\f09d\";\n}\n.fa-feed:before,\n.fa-rss:before {\n  content: \"\\f09e\";\n}\n.fa-hdd-o:before {\n  content: \"\\f0a0\";\n}\n.fa-bullhorn:before {\n  content: \"\\f0a1\";\n}\n.fa-bell:before {\n  content: \"\\f0f3\";\n}\n.fa-certificate:before {\n  content: \"\\f0a3\";\n}\n.fa-hand-o-right:before {\n  content: \"\\f0a4\";\n}\n.fa-hand-o-left:before {\n  content: \"\\f0a5\";\n}\n.fa-hand-o-up:before {\n  content: \"\\f0a6\";\n}\n.fa-hand-o-down:before {\n  content: \"\\f0a7\";\n}\n.fa-arrow-circle-left:before {\n  content: \"\\f0a8\";\n}\n.fa-arrow-circle-right:before {\n  content: \"\\f0a9\";\n}\n.fa-arrow-circle-up:before {\n  content: \"\\f0aa\";\n}\n.fa-arrow-circle-down:before {\n  content: \"\\f0ab\";\n}\n.fa-globe:before {\n  content: \"\\f0ac\";\n}\n.fa-wrench:before {\n  content: \"\\f0ad\";\n}\n.fa-tasks:before {\n  content: \"\\f0ae\";\n}\n.fa-filter:before {\n  content: \"\\f0b0\";\n}\n.fa-briefcase:before {\n  content: \"\\f0b1\";\n}\n.fa-arrows-alt:before {\n  content: \"\\f0b2\";\n}\n.fa-group:before,\n.fa-users:before {\n  content: \"\\f0c0\";\n}\n.fa-chain:before,\n.fa-link:before {\n  content: \"\\f0c1\";\n}\n.fa-cloud:before {\n  content: \"\\f0c2\";\n}\n.fa-flask:before {\n  content: \"\\f0c3\";\n}\n.fa-cut:before,\n.fa-scissors:before {\n  content: \"\\f0c4\";\n}\n.fa-copy:before,\n.fa-files-o:before {\n  content: \"\\f0c5\";\n}\n.fa-paperclip:before {\n  content: \"\\f0c6\";\n}\n.fa-save:before,\n.fa-floppy-o:before {\n  content: \"\\f0c7\";\n}\n.fa-square:before {\n  content: \"\\f0c8\";\n}\n.fa-navicon:before,\n.fa-reorder:before,\n.fa-bars:before {\n  content: \"\\f0c9\";\n}\n.fa-list-ul:before {\n  content: \"\\f0ca\";\n}\n.fa-list-ol:before {\n  content: \"\\f0cb\";\n}\n.fa-strikethrough:before {\n  content: \"\\f0cc\";\n}\n.fa-underline:before {\n  content: \"\\f0cd\";\n}\n.fa-table:before {\n  content: \"\\f0ce\";\n}\n.fa-magic:before {\n  content: \"\\f0d0\";\n}\n.fa-truck:before {\n  content: \"\\f0d1\";\n}\n.fa-pinterest:before {\n  content: \"\\f0d2\";\n}\n.fa-pinterest-square:before {\n  content: \"\\f0d3\";\n}\n.fa-google-plus-square:before {\n  content: \"\\f0d4\";\n}\n.fa-google-plus:before {\n  content: \"\\f0d5\";\n}\n.fa-money:before {\n  content: \"\\f0d6\";\n}\n.fa-caret-down:before {\n  content: \"\\f0d7\";\n}\n.fa-caret-up:before {\n  content: \"\\f0d8\";\n}\n.fa-caret-left:before {\n  content: \"\\f0d9\";\n}\n.fa-caret-right:before {\n  content: \"\\f0da\";\n}\n.fa-columns:before {\n  content: \"\\f0db\";\n}\n.fa-unsorted:before,\n.fa-sort:before {\n  content: \"\\f0dc\";\n}\n.fa-sort-down:before,\n.fa-sort-desc:before {\n  content: \"\\f0dd\";\n}\n.fa-sort-up:before,\n.fa-sort-asc:before {\n  content: \"\\f0de\";\n}\n.fa-envelope:before {\n  content: \"\\f0e0\";\n}\n.fa-linkedin:before {\n  content: \"\\f0e1\";\n}\n.fa-rotate-left:before,\n.fa-undo:before {\n  content: \"\\f0e2\";\n}\n.fa-legal:before,\n.fa-gavel:before {\n  content: \"\\f0e3\";\n}\n.fa-dashboard:before,\n.fa-tachometer:before {\n  content: \"\\f0e4\";\n}\n.fa-comment-o:before {\n  content: \"\\f0e5\";\n}\n.fa-comments-o:before {\n  content: \"\\f0e6\";\n}\n.fa-flash:before,\n.fa-bolt:before {\n  content: \"\\f0e7\";\n}\n.fa-sitemap:before {\n  content: \"\\f0e8\";\n}\n.fa-umbrella:before {\n  content: \"\\f0e9\";\n}\n.fa-paste:before,\n.fa-clipboard:before {\n  content: \"\\f0ea\";\n}\n.fa-lightbulb-o:before {\n  content: \"\\f0eb\";\n}\n.fa-exchange:before {\n  content: \"\\f0ec\";\n}\n.fa-cloud-download:before {\n  content: \"\\f0ed\";\n}\n.fa-cloud-upload:before {\n  content: \"\\f0ee\";\n}\n.fa-user-md:before {\n  content: \"\\f0f0\";\n}\n.fa-stethoscope:before {\n  content: \"\\f0f1\";\n}\n.fa-suitcase:before {\n  content: \"\\f0f2\";\n}\n.fa-bell-o:before {\n  content: \"\\f0a2\";\n}\n.fa-coffee:before {\n  content: \"\\f0f4\";\n}\n.fa-cutlery:before {\n  content: \"\\f0f5\";\n}\n.fa-file-text-o:before {\n  content: \"\\f0f6\";\n}\n.fa-building-o:before {\n  content: \"\\f0f7\";\n}\n.fa-hospital-o:before {\n  content: \"\\f0f8\";\n}\n.fa-ambulance:before {\n  content: \"\\f0f9\";\n}\n.fa-medkit:before {\n  content: \"\\f0fa\";\n}\n.fa-fighter-jet:before {\n  content: \"\\f0fb\";\n}\n.fa-beer:before {\n  content: \"\\f0fc\";\n}\n.fa-h-square:before {\n  content: \"\\f0fd\";\n}\n.fa-plus-square:before {\n  content: \"\\f0fe\";\n}\n.fa-angle-double-left:before {\n  content: \"\\f100\";\n}\n.fa-angle-double-right:before {\n  content: \"\\f101\";\n}\n.fa-angle-double-up:before {\n  content: \"\\f102\";\n}\n.fa-angle-double-down:before {\n  content: \"\\f103\";\n}\n.fa-angle-left:before {\n  content: \"\\f104\";\n}\n.fa-angle-right:before {\n  content: \"\\f105\";\n}\n.fa-angle-up:before {\n  content: \"\\f106\";\n}\n.fa-angle-down:before {\n  content: \"\\f107\";\n}\n.fa-desktop:before {\n  content: \"\\f108\";\n}\n.fa-laptop:before {\n  content: \"\\f109\";\n}\n.fa-tablet:before {\n  content: \"\\f10a\";\n}\n.fa-mobile-phone:before,\n.fa-mobile:before {\n  content: \"\\f10b\";\n}\n.fa-circle-o:before {\n  content: \"\\f10c\";\n}\n.fa-quote-left:before {\n  content: \"\\f10d\";\n}\n.fa-quote-right:before {\n  content: \"\\f10e\";\n}\n.fa-spinner:before {\n  content: \"\\f110\";\n}\n.fa-circle:before {\n  content: \"\\f111\";\n}\n.fa-mail-reply:before,\n.fa-reply:before {\n  content: \"\\f112\";\n}\n.fa-github-alt:before {\n  content: \"\\f113\";\n}\n.fa-folder-o:before {\n  content: \"\\f114\";\n}\n.fa-folder-open-o:before {\n  content: \"\\f115\";\n}\n.fa-smile-o:before {\n  content: \"\\f118\";\n}\n.fa-frown-o:before {\n  content: \"\\f119\";\n}\n.fa-meh-o:before {\n  content: \"\\f11a\";\n}\n.fa-gamepad:before {\n  content: \"\\f11b\";\n}\n.fa-keyboard-o:before {\n  content: \"\\f11c\";\n}\n.fa-flag-o:before {\n  content: \"\\f11d\";\n}\n.fa-flag-checkered:before {\n  content: \"\\f11e\";\n}\n.fa-terminal:before {\n  content: \"\\f120\";\n}\n.fa-code:before {\n  content: \"\\f121\";\n}\n.fa-mail-reply-all:before,\n.fa-reply-all:before {\n  content: \"\\f122\";\n}\n.fa-star-half-empty:before,\n.fa-star-half-full:before,\n.fa-star-half-o:before {\n  content: \"\\f123\";\n}\n.fa-location-arrow:before {\n  content: \"\\f124\";\n}\n.fa-crop:before {\n  content: \"\\f125\";\n}\n.fa-code-fork:before {\n  content: \"\\f126\";\n}\n.fa-unlink:before,\n.fa-chain-broken:before {\n  content: \"\\f127\";\n}\n.fa-question:before {\n  content: \"\\f128\";\n}\n.fa-info:before {\n  content: \"\\f129\";\n}\n.fa-exclamation:before {\n  content: \"\\f12a\";\n}\n.fa-superscript:before {\n  content: \"\\f12b\";\n}\n.fa-subscript:before {\n  content: \"\\f12c\";\n}\n.fa-eraser:before {\n  content: \"\\f12d\";\n}\n.fa-puzzle-piece:before {\n  content: \"\\f12e\";\n}\n.fa-microphone:before {\n  content: \"\\f130\";\n}\n.fa-microphone-slash:before {\n  content: \"\\f131\";\n}\n.fa-shield:before {\n  content: \"\\f132\";\n}\n.fa-calendar-o:before {\n  content: \"\\f133\";\n}\n.fa-fire-extinguisher:before {\n  content: \"\\f134\";\n}\n.fa-rocket:before {\n  content: \"\\f135\";\n}\n.fa-maxcdn:before {\n  content: \"\\f136\";\n}\n.fa-chevron-circle-left:before {\n  content: \"\\f137\";\n}\n.fa-chevron-circle-right:before {\n  content: \"\\f138\";\n}\n.fa-chevron-circle-up:before {\n  content: \"\\f139\";\n}\n.fa-chevron-circle-down:before {\n  content: \"\\f13a\";\n}\n.fa-html5:before {\n  content: \"\\f13b\";\n}\n.fa-css3:before {\n  content: \"\\f13c\";\n}\n.fa-anchor:before {\n  content: \"\\f13d\";\n}\n.fa-unlock-alt:before {\n  content: \"\\f13e\";\n}\n.fa-bullseye:before {\n  content: \"\\f140\";\n}\n.fa-ellipsis-h:before {\n  content: \"\\f141\";\n}\n.fa-ellipsis-v:before {\n  content: \"\\f142\";\n}\n.fa-rss-square:before {\n  content: \"\\f143\";\n}\n.fa-play-circle:before {\n  content: \"\\f144\";\n}\n.fa-ticket:before {\n  content: \"\\f145\";\n}\n.fa-minus-square:before {\n  content: \"\\f146\";\n}\n.fa-minus-square-o:before {\n  content: \"\\f147\";\n}\n.fa-level-up:before {\n  content: \"\\f148\";\n}\n.fa-level-down:before {\n  content: \"\\f149\";\n}\n.fa-check-square:before {\n  content: \"\\f14a\";\n}\n.fa-pencil-square:before {\n  content: \"\\f14b\";\n}\n.fa-external-link-square:before {\n  content: \"\\f14c\";\n}\n.fa-share-square:before {\n  content: \"\\f14d\";\n}\n.fa-compass:before {\n  content: \"\\f14e\";\n}\n.fa-toggle-down:before,\n.fa-caret-square-o-down:before {\n  content: \"\\f150\";\n}\n.fa-toggle-up:before,\n.fa-caret-square-o-up:before {\n  content: \"\\f151\";\n}\n.fa-toggle-right:before,\n.fa-caret-square-o-right:before {\n  content: \"\\f152\";\n}\n.fa-euro:before,\n.fa-eur:before {\n  content: \"\\f153\";\n}\n.fa-gbp:before {\n  content: \"\\f154\";\n}\n.fa-dollar:before,\n.fa-usd:before {\n  content: \"\\f155\";\n}\n.fa-rupee:before,\n.fa-inr:before {\n  content: \"\\f156\";\n}\n.fa-cny:before,\n.fa-rmb:before,\n.fa-yen:before,\n.fa-jpy:before {\n  content: \"\\f157\";\n}\n.fa-ruble:before,\n.fa-rouble:before,\n.fa-rub:before {\n  content: \"\\f158\";\n}\n.fa-won:before,\n.fa-krw:before {\n  content: \"\\f159\";\n}\n.fa-bitcoin:before,\n.fa-btc:before {\n  content: \"\\f15a\";\n}\n.fa-file:before {\n  content: \"\\f15b\";\n}\n.fa-file-text:before {\n  content: \"\\f15c\";\n}\n.fa-sort-alpha-asc:before {\n  content: \"\\f15d\";\n}\n.fa-sort-alpha-desc:before {\n  content: \"\\f15e\";\n}\n.fa-sort-amount-asc:before {\n  content: \"\\f160\";\n}\n.fa-sort-amount-desc:before {\n  content: \"\\f161\";\n}\n.fa-sort-numeric-asc:before {\n  content: \"\\f162\";\n}\n.fa-sort-numeric-desc:before {\n  content: \"\\f163\";\n}\n.fa-thumbs-up:before {\n  content: \"\\f164\";\n}\n.fa-thumbs-down:before {\n  content: \"\\f165\";\n}\n.fa-youtube-square:before {\n  content: \"\\f166\";\n}\n.fa-youtube:before {\n  content: \"\\f167\";\n}\n.fa-xing:before {\n  content: \"\\f168\";\n}\n.fa-xing-square:before {\n  content: \"\\f169\";\n}\n.fa-youtube-play:before {\n  content: \"\\f16a\";\n}\n.fa-dropbox:before {\n  content: \"\\f16b\";\n}\n.fa-stack-overflow:before {\n  content: \"\\f16c\";\n}\n.fa-instagram:before {\n  content: \"\\f16d\";\n}\n.fa-flickr:before {\n  content: \"\\f16e\";\n}\n.fa-adn:before {\n  content: \"\\f170\";\n}\n.fa-bitbucket:before {\n  content: \"\\f171\";\n}\n.fa-bitbucket-square:before {\n  content: \"\\f172\";\n}\n.fa-tumblr:before {\n  content: \"\\f173\";\n}\n.fa-tumblr-square:before {\n  content: \"\\f174\";\n}\n.fa-long-arrow-down:before {\n  content: \"\\f175\";\n}\n.fa-long-arrow-up:before {\n  content: \"\\f176\";\n}\n.fa-long-arrow-left:before {\n  content: \"\\f177\";\n}\n.fa-long-arrow-right:before {\n  content: \"\\f178\";\n}\n.fa-apple:before {\n  content: \"\\f179\";\n}\n.fa-windows:before {\n  content: \"\\f17a\";\n}\n.fa-android:before {\n  content: \"\\f17b\";\n}\n.fa-linux:before {\n  content: \"\\f17c\";\n}\n.fa-dribbble:before {\n  content: \"\\f17d\";\n}\n.fa-skype:before {\n  content: \"\\f17e\";\n}\n.fa-foursquare:before {\n  content: \"\\f180\";\n}\n.fa-trello:before {\n  content: \"\\f181\";\n}\n.fa-female:before {\n  content: \"\\f182\";\n}\n.fa-male:before {\n  content: \"\\f183\";\n}\n.fa-gittip:before,\n.fa-gratipay:before {\n  content: \"\\f184\";\n}\n.fa-sun-o:before {\n  content: \"\\f185\";\n}\n.fa-moon-o:before {\n  content: \"\\f186\";\n}\n.fa-archive:before {\n  content: \"\\f187\";\n}\n.fa-bug:before {\n  content: \"\\f188\";\n}\n.fa-vk:before {\n  content: \"\\f189\";\n}\n.fa-weibo:before {\n  content: \"\\f18a\";\n}\n.fa-renren:before {\n  content: \"\\f18b\";\n}\n.fa-pagelines:before {\n  content: \"\\f18c\";\n}\n.fa-stack-exchange:before {\n  content: \"\\f18d\";\n}\n.fa-arrow-circle-o-right:before {\n  content: \"\\f18e\";\n}\n.fa-arrow-circle-o-left:before {\n  content: \"\\f190\";\n}\n.fa-toggle-left:before,\n.fa-caret-square-o-left:before {\n  content: \"\\f191\";\n}\n.fa-dot-circle-o:before {\n  content: \"\\f192\";\n}\n.fa-wheelchair:before {\n  content: \"\\f193\";\n}\n.fa-vimeo-square:before {\n  content: \"\\f194\";\n}\n.fa-turkish-lira:before,\n.fa-try:before {\n  content: \"\\f195\";\n}\n.fa-plus-square-o:before {\n  content: \"\\f196\";\n}\n.fa-space-shuttle:before {\n  content: \"\\f197\";\n}\n.fa-slack:before {\n  content: \"\\f198\";\n}\n.fa-envelope-square:before {\n  content: \"\\f199\";\n}\n.fa-wordpress:before {\n  content: \"\\f19a\";\n}\n.fa-openid:before {\n  content: \"\\f19b\";\n}\n.fa-institution:before,\n.fa-bank:before,\n.fa-university:before {\n  content: \"\\f19c\";\n}\n.fa-mortar-board:before,\n.fa-graduation-cap:before {\n  content: \"\\f19d\";\n}\n.fa-yahoo:before {\n  content: \"\\f19e\";\n}\n.fa-google:before {\n  content: \"\\f1a0\";\n}\n.fa-reddit:before {\n  content: \"\\f1a1\";\n}\n.fa-reddit-square:before {\n  content: \"\\f1a2\";\n}\n.fa-stumbleupon-circle:before {\n  content: \"\\f1a3\";\n}\n.fa-stumbleupon:before {\n  content: \"\\f1a4\";\n}\n.fa-delicious:before {\n  content: \"\\f1a5\";\n}\n.fa-digg:before {\n  content: \"\\f1a6\";\n}\n.fa-pied-piper-pp:before {\n  content: \"\\f1a7\";\n}\n.fa-pied-piper-alt:before {\n  content: \"\\f1a8\";\n}\n.fa-drupal:before {\n  content: \"\\f1a9\";\n}\n.fa-joomla:before {\n  content: \"\\f1aa\";\n}\n.fa-language:before {\n  content: \"\\f1ab\";\n}\n.fa-fax:before {\n  content: \"\\f1ac\";\n}\n.fa-building:before {\n  content: \"\\f1ad\";\n}\n.fa-child:before {\n  content: \"\\f1ae\";\n}\n.fa-paw:before {\n  content: \"\\f1b0\";\n}\n.fa-spoon:before {\n  content: \"\\f1b1\";\n}\n.fa-cube:before {\n  content: \"\\f1b2\";\n}\n.fa-cubes:before {\n  content: \"\\f1b3\";\n}\n.fa-behance:before {\n  content: \"\\f1b4\";\n}\n.fa-behance-square:before {\n  content: \"\\f1b5\";\n}\n.fa-steam:before {\n  content: \"\\f1b6\";\n}\n.fa-steam-square:before {\n  content: \"\\f1b7\";\n}\n.fa-recycle:before {\n  content: \"\\f1b8\";\n}\n.fa-automobile:before,\n.fa-car:before {\n  content: \"\\f1b9\";\n}\n.fa-cab:before,\n.fa-taxi:before {\n  content: \"\\f1ba\";\n}\n.fa-tree:before {\n  content: \"\\f1bb\";\n}\n.fa-spotify:before {\n  content: \"\\f1bc\";\n}\n.fa-deviantart:before {\n  content: \"\\f1bd\";\n}\n.fa-soundcloud:before {\n  content: \"\\f1be\";\n}\n.fa-database:before {\n  content: \"\\f1c0\";\n}\n.fa-file-pdf-o:before {\n  content: \"\\f1c1\";\n}\n.fa-file-word-o:before {\n  content: \"\\f1c2\";\n}\n.fa-file-excel-o:before {\n  content: \"\\f1c3\";\n}\n.fa-file-powerpoint-o:before {\n  content: \"\\f1c4\";\n}\n.fa-file-photo-o:before,\n.fa-file-picture-o:before,\n.fa-file-image-o:before {\n  content: \"\\f1c5\";\n}\n.fa-file-zip-o:before,\n.fa-file-archive-o:before {\n  content: \"\\f1c6\";\n}\n.fa-file-sound-o:before,\n.fa-file-audio-o:before {\n  content: \"\\f1c7\";\n}\n.fa-file-movie-o:before,\n.fa-file-video-o:before {\n  content: \"\\f1c8\";\n}\n.fa-file-code-o:before {\n  content: \"\\f1c9\";\n}\n.fa-vine:before {\n  content: \"\\f1ca\";\n}\n.fa-codepen:before {\n  content: \"\\f1cb\";\n}\n.fa-jsfiddle:before {\n  content: \"\\f1cc\";\n}\n.fa-life-bouy:before,\n.fa-life-buoy:before,\n.fa-life-saver:before,\n.fa-support:before,\n.fa-life-ring:before {\n  content: \"\\f1cd\";\n}\n.fa-circle-o-notch:before {\n  content: \"\\f1ce\";\n}\n.fa-ra:before,\n.fa-resistance:before,\n.fa-rebel:before {\n  content: \"\\f1d0\";\n}\n.fa-ge:before,\n.fa-empire:before {\n  content: \"\\f1d1\";\n}\n.fa-git-square:before {\n  content: \"\\f1d2\";\n}\n.fa-git:before {\n  content: \"\\f1d3\";\n}\n.fa-y-combinator-square:before,\n.fa-yc-square:before,\n.fa-hacker-news:before {\n  content: \"\\f1d4\";\n}\n.fa-tencent-weibo:before {\n  content: \"\\f1d5\";\n}\n.fa-qq:before {\n  content: \"\\f1d6\";\n}\n.fa-wechat:before,\n.fa-weixin:before {\n  content: \"\\f1d7\";\n}\n.fa-send:before,\n.fa-paper-plane:before {\n  content: \"\\f1d8\";\n}\n.fa-send-o:before,\n.fa-paper-plane-o:before {\n  content: \"\\f1d9\";\n}\n.fa-history:before {\n  content: \"\\f1da\";\n}\n.fa-circle-thin:before {\n  content: \"\\f1db\";\n}\n.fa-header:before {\n  content: \"\\f1dc\";\n}\n.fa-paragraph:before {\n  content: \"\\f1dd\";\n}\n.fa-sliders:before {\n  content: \"\\f1de\";\n}\n.fa-share-alt:before {\n  content: \"\\f1e0\";\n}\n.fa-share-alt-square:before {\n  content: \"\\f1e1\";\n}\n.fa-bomb:before {\n  content: \"\\f1e2\";\n}\n.fa-soccer-ball-o:before,\n.fa-futbol-o:before {\n  content: \"\\f1e3\";\n}\n.fa-tty:before {\n  content: \"\\f1e4\";\n}\n.fa-binoculars:before {\n  content: \"\\f1e5\";\n}\n.fa-plug:before {\n  content: \"\\f1e6\";\n}\n.fa-slideshare:before {\n  content: \"\\f1e7\";\n}\n.fa-twitch:before {\n  content: \"\\f1e8\";\n}\n.fa-yelp:before {\n  content: \"\\f1e9\";\n}\n.fa-newspaper-o:before {\n  content: \"\\f1ea\";\n}\n.fa-wifi:before {\n  content: \"\\f1eb\";\n}\n.fa-calculator:before {\n  content: \"\\f1ec\";\n}\n.fa-paypal:before {\n  content: \"\\f1ed\";\n}\n.fa-google-wallet:before {\n  content: \"\\f1ee\";\n}\n.fa-cc-visa:before {\n  content: \"\\f1f0\";\n}\n.fa-cc-mastercard:before {\n  content: \"\\f1f1\";\n}\n.fa-cc-discover:before {\n  content: \"\\f1f2\";\n}\n.fa-cc-amex:before {\n  content: \"\\f1f3\";\n}\n.fa-cc-paypal:before {\n  content: \"\\f1f4\";\n}\n.fa-cc-stripe:before {\n  content: \"\\f1f5\";\n}\n.fa-bell-slash:before {\n  content: \"\\f1f6\";\n}\n.fa-bell-slash-o:before {\n  content: \"\\f1f7\";\n}\n.fa-trash:before {\n  content: \"\\f1f8\";\n}\n.fa-copyright:before {\n  content: \"\\f1f9\";\n}\n.fa-at:before {\n  content: \"\\f1fa\";\n}\n.fa-eyedropper:before {\n  content: \"\\f1fb\";\n}\n.fa-paint-brush:before {\n  content: \"\\f1fc\";\n}\n.fa-birthday-cake:before {\n  content: \"\\f1fd\";\n}\n.fa-area-chart:before {\n  content: \"\\f1fe\";\n}\n.fa-pie-chart:before {\n  content: \"\\f200\";\n}\n.fa-line-chart:before {\n  content: \"\\f201\";\n}\n.fa-lastfm:before {\n  content: \"\\f202\";\n}\n.fa-lastfm-square:before {\n  content: \"\\f203\";\n}\n.fa-toggle-off:before {\n  content: \"\\f204\";\n}\n.fa-toggle-on:before {\n  content: \"\\f205\";\n}\n.fa-bicycle:before {\n  content: \"\\f206\";\n}\n.fa-bus:before {\n  content: \"\\f207\";\n}\n.fa-ioxhost:before {\n  content: \"\\f208\";\n}\n.fa-angellist:before {\n  content: \"\\f209\";\n}\n.fa-cc:before {\n  content: \"\\f20a\";\n}\n.fa-shekel:before,\n.fa-sheqel:before,\n.fa-ils:before {\n  content: \"\\f20b\";\n}\n.fa-meanpath:before {\n  content: \"\\f20c\";\n}\n.fa-buysellads:before {\n  content: \"\\f20d\";\n}\n.fa-connectdevelop:before {\n  content: \"\\f20e\";\n}\n.fa-dashcube:before {\n  content: \"\\f210\";\n}\n.fa-forumbee:before {\n  content: \"\\f211\";\n}\n.fa-leanpub:before {\n  content: \"\\f212\";\n}\n.fa-sellsy:before {\n  content: \"\\f213\";\n}\n.fa-shirtsinbulk:before {\n  content: \"\\f214\";\n}\n.fa-simplybuilt:before {\n  content: \"\\f215\";\n}\n.fa-skyatlas:before {\n  content: \"\\f216\";\n}\n.fa-cart-plus:before {\n  content: \"\\f217\";\n}\n.fa-cart-arrow-down:before {\n  content: \"\\f218\";\n}\n.fa-diamond:before {\n  content: \"\\f219\";\n}\n.fa-ship:before {\n  content: \"\\f21a\";\n}\n.fa-user-secret:before {\n  content: \"\\f21b\";\n}\n.fa-motorcycle:before {\n  content: \"\\f21c\";\n}\n.fa-street-view:before {\n  content: \"\\f21d\";\n}\n.fa-heartbeat:before {\n  content: \"\\f21e\";\n}\n.fa-venus:before {\n  content: \"\\f221\";\n}\n.fa-mars:before {\n  content: \"\\f222\";\n}\n.fa-mercury:before {\n  content: \"\\f223\";\n}\n.fa-intersex:before,\n.fa-transgender:before {\n  content: \"\\f224\";\n}\n.fa-transgender-alt:before {\n  content: \"\\f225\";\n}\n.fa-venus-double:before {\n  content: \"\\f226\";\n}\n.fa-mars-double:before {\n  content: \"\\f227\";\n}\n.fa-venus-mars:before {\n  content: \"\\f228\";\n}\n.fa-mars-stroke:before {\n  content: \"\\f229\";\n}\n.fa-mars-stroke-v:before {\n  content: \"\\f22a\";\n}\n.fa-mars-stroke-h:before {\n  content: \"\\f22b\";\n}\n.fa-neuter:before {\n  content: \"\\f22c\";\n}\n.fa-genderless:before {\n  content: \"\\f22d\";\n}\n.fa-facebook-official:before {\n  content: \"\\f230\";\n}\n.fa-pinterest-p:before {\n  content: \"\\f231\";\n}\n.fa-whatsapp:before {\n  content: \"\\f232\";\n}\n.fa-server:before {\n  content: \"\\f233\";\n}\n.fa-user-plus:before {\n  content: \"\\f234\";\n}\n.fa-user-times:before {\n  content: \"\\f235\";\n}\n.fa-hotel:before,\n.fa-bed:before {\n  content: \"\\f236\";\n}\n.fa-viacoin:before {\n  content: \"\\f237\";\n}\n.fa-train:before {\n  content: \"\\f238\";\n}\n.fa-subway:before {\n  content: \"\\f239\";\n}\n.fa-medium:before {\n  content: \"\\f23a\";\n}\n.fa-yc:before,\n.fa-y-combinator:before {\n  content: \"\\f23b\";\n}\n.fa-optin-monster:before {\n  content: \"\\f23c\";\n}\n.fa-opencart:before {\n  content: \"\\f23d\";\n}\n.fa-expeditedssl:before {\n  content: \"\\f23e\";\n}\n.fa-battery-4:before,\n.fa-battery:before,\n.fa-battery-full:before {\n  content: \"\\f240\";\n}\n.fa-battery-3:before,\n.fa-battery-three-quarters:before {\n  content: \"\\f241\";\n}\n.fa-battery-2:before,\n.fa-battery-half:before {\n  content: \"\\f242\";\n}\n.fa-battery-1:before,\n.fa-battery-quarter:before {\n  content: \"\\f243\";\n}\n.fa-battery-0:before,\n.fa-battery-empty:before {\n  content: \"\\f244\";\n}\n.fa-mouse-pointer:before {\n  content: \"\\f245\";\n}\n.fa-i-cursor:before {\n  content: \"\\f246\";\n}\n.fa-object-group:before {\n  content: \"\\f247\";\n}\n.fa-object-ungroup:before {\n  content: \"\\f248\";\n}\n.fa-sticky-note:before {\n  content: \"\\f249\";\n}\n.fa-sticky-note-o:before {\n  content: \"\\f24a\";\n}\n.fa-cc-jcb:before {\n  content: \"\\f24b\";\n}\n.fa-cc-diners-club:before {\n  content: \"\\f24c\";\n}\n.fa-clone:before {\n  content: \"\\f24d\";\n}\n.fa-balance-scale:before {\n  content: \"\\f24e\";\n}\n.fa-hourglass-o:before {\n  content: \"\\f250\";\n}\n.fa-hourglass-1:before,\n.fa-hourglass-start:before {\n  content: \"\\f251\";\n}\n.fa-hourglass-2:before,\n.fa-hourglass-half:before {\n  content: \"\\f252\";\n}\n.fa-hourglass-3:before,\n.fa-hourglass-end:before {\n  content: \"\\f253\";\n}\n.fa-hourglass:before {\n  content: \"\\f254\";\n}\n.fa-hand-grab-o:before,\n.fa-hand-rock-o:before {\n  content: \"\\f255\";\n}\n.fa-hand-stop-o:before,\n.fa-hand-paper-o:before {\n  content: \"\\f256\";\n}\n.fa-hand-scissors-o:before {\n  content: \"\\f257\";\n}\n.fa-hand-lizard-o:before {\n  content: \"\\f258\";\n}\n.fa-hand-spock-o:before {\n  content: \"\\f259\";\n}\n.fa-hand-pointer-o:before {\n  content: \"\\f25a\";\n}\n.fa-hand-peace-o:before {\n  content: \"\\f25b\";\n}\n.fa-trademark:before {\n  content: \"\\f25c\";\n}\n.fa-registered:before {\n  content: \"\\f25d\";\n}\n.fa-creative-commons:before {\n  content: \"\\f25e\";\n}\n.fa-gg:before {\n  content: \"\\f260\";\n}\n.fa-gg-circle:before {\n  content: \"\\f261\";\n}\n.fa-tripadvisor:before {\n  content: \"\\f262\";\n}\n.fa-odnoklassniki:before {\n  content: \"\\f263\";\n}\n.fa-odnoklassniki-square:before {\n  content: \"\\f264\";\n}\n.fa-get-pocket:before {\n  content: \"\\f265\";\n}\n.fa-wikipedia-w:before {\n  content: \"\\f266\";\n}\n.fa-safari:before {\n  content: \"\\f267\";\n}\n.fa-chrome:before {\n  content: \"\\f268\";\n}\n.fa-firefox:before {\n  content: \"\\f269\";\n}\n.fa-opera:before {\n  content: \"\\f26a\";\n}\n.fa-internet-explorer:before {\n  content: \"\\f26b\";\n}\n.fa-tv:before,\n.fa-television:before {\n  content: \"\\f26c\";\n}\n.fa-contao:before {\n  content: \"\\f26d\";\n}\n.fa-500px:before {\n  content: \"\\f26e\";\n}\n.fa-amazon:before {\n  content: \"\\f270\";\n}\n.fa-calendar-plus-o:before {\n  content: \"\\f271\";\n}\n.fa-calendar-minus-o:before {\n  content: \"\\f272\";\n}\n.fa-calendar-times-o:before {\n  content: \"\\f273\";\n}\n.fa-calendar-check-o:before {\n  content: \"\\f274\";\n}\n.fa-industry:before {\n  content: \"\\f275\";\n}\n.fa-map-pin:before {\n  content: \"\\f276\";\n}\n.fa-map-signs:before {\n  content: \"\\f277\";\n}\n.fa-map-o:before {\n  content: \"\\f278\";\n}\n.fa-map:before {\n  content: \"\\f279\";\n}\n.fa-commenting:before {\n  content: \"\\f27a\";\n}\n.fa-commenting-o:before {\n  content: \"\\f27b\";\n}\n.fa-houzz:before {\n  content: \"\\f27c\";\n}\n.fa-vimeo:before {\n  content: \"\\f27d\";\n}\n.fa-black-tie:before {\n  content: \"\\f27e\";\n}\n.fa-fonticons:before {\n  content: \"\\f280\";\n}\n.fa-reddit-alien:before {\n  content: \"\\f281\";\n}\n.fa-edge:before {\n  content: \"\\f282\";\n}\n.fa-credit-card-alt:before {\n  content: \"\\f283\";\n}\n.fa-codiepie:before {\n  content: \"\\f284\";\n}\n.fa-modx:before {\n  content: \"\\f285\";\n}\n.fa-fort-awesome:before {\n  content: \"\\f286\";\n}\n.fa-usb:before {\n  content: \"\\f287\";\n}\n.fa-product-hunt:before {\n  content: \"\\f288\";\n}\n.fa-mixcloud:before {\n  content: \"\\f289\";\n}\n.fa-scribd:before {\n  content: \"\\f28a\";\n}\n.fa-pause-circle:before {\n  content: \"\\f28b\";\n}\n.fa-pause-circle-o:before {\n  content: \"\\f28c\";\n}\n.fa-stop-circle:before {\n  content: \"\\f28d\";\n}\n.fa-stop-circle-o:before {\n  content: \"\\f28e\";\n}\n.fa-shopping-bag:before {\n  content: \"\\f290\";\n}\n.fa-shopping-basket:before {\n  content: \"\\f291\";\n}\n.fa-hashtag:before {\n  content: \"\\f292\";\n}\n.fa-bluetooth:before {\n  content: \"\\f293\";\n}\n.fa-bluetooth-b:before {\n  content: \"\\f294\";\n}\n.fa-percent:before {\n  content: \"\\f295\";\n}\n.fa-gitlab:before {\n  content: \"\\f296\";\n}\n.fa-wpbeginner:before {\n  content: \"\\f297\";\n}\n.fa-wpforms:before {\n  content: \"\\f298\";\n}\n.fa-envira:before {\n  content: \"\\f299\";\n}\n.fa-universal-access:before {\n  content: \"\\f29a\";\n}\n.fa-wheelchair-alt:before {\n  content: \"\\f29b\";\n}\n.fa-question-circle-o:before {\n  content: \"\\f29c\";\n}\n.fa-blind:before {\n  content: \"\\f29d\";\n}\n.fa-audio-description:before {\n  content: \"\\f29e\";\n}\n.fa-volume-control-phone:before {\n  content: \"\\f2a0\";\n}\n.fa-braille:before {\n  content: \"\\f2a1\";\n}\n.fa-assistive-listening-systems:before {\n  content: \"\\f2a2\";\n}\n.fa-asl-interpreting:before,\n.fa-american-sign-language-interpreting:before {\n  content: \"\\f2a3\";\n}\n.fa-deafness:before,\n.fa-hard-of-hearing:before,\n.fa-deaf:before {\n  content: \"\\f2a4\";\n}\n.fa-glide:before {\n  content: \"\\f2a5\";\n}\n.fa-glide-g:before {\n  content: \"\\f2a6\";\n}\n.fa-signing:before,\n.fa-sign-language:before {\n  content: \"\\f2a7\";\n}\n.fa-low-vision:before {\n  content: \"\\f2a8\";\n}\n.fa-viadeo:before {\n  content: \"\\f2a9\";\n}\n.fa-viadeo-square:before {\n  content: \"\\f2aa\";\n}\n.fa-snapchat:before {\n  content: \"\\f2ab\";\n}\n.fa-snapchat-ghost:before {\n  content: \"\\f2ac\";\n}\n.fa-snapchat-square:before {\n  content: \"\\f2ad\";\n}\n.fa-pied-piper:before {\n  content: \"\\f2ae\";\n}\n.fa-first-order:before {\n  content: \"\\f2b0\";\n}\n.fa-yoast:before {\n  content: \"\\f2b1\";\n}\n.fa-themeisle:before {\n  content: \"\\f2b2\";\n}\n.fa-google-plus-circle:before,\n.fa-google-plus-official:before {\n  content: \"\\f2b3\";\n}\n.fa-fa:before,\n.fa-font-awesome:before {\n  content: \"\\f2b4\";\n}\n.fa-handshake-o:before {\n  content: \"\\f2b5\";\n}\n.fa-envelope-open:before {\n  content: \"\\f2b6\";\n}\n.fa-envelope-open-o:before {\n  content: \"\\f2b7\";\n}\n.fa-linode:before {\n  content: \"\\f2b8\";\n}\n.fa-address-book:before {\n  content: \"\\f2b9\";\n}\n.fa-address-book-o:before {\n  content: \"\\f2ba\";\n}\n.fa-vcard:before,\n.fa-address-card:before {\n  content: \"\\f2bb\";\n}\n.fa-vcard-o:before,\n.fa-address-card-o:before {\n  content: \"\\f2bc\";\n}\n.fa-user-circle:before {\n  content: \"\\f2bd\";\n}\n.fa-user-circle-o:before {\n  content: \"\\f2be\";\n}\n.fa-user-o:before {\n  content: \"\\f2c0\";\n}\n.fa-id-badge:before {\n  content: \"\\f2c1\";\n}\n.fa-drivers-license:before,\n.fa-id-card:before {\n  content: \"\\f2c2\";\n}\n.fa-drivers-license-o:before,\n.fa-id-card-o:before {\n  content: \"\\f2c3\";\n}\n.fa-quora:before {\n  content: \"\\f2c4\";\n}\n.fa-free-code-camp:before {\n  content: \"\\f2c5\";\n}\n.fa-telegram:before {\n  content: \"\\f2c6\";\n}\n.fa-thermometer-4:before,\n.fa-thermometer:before,\n.fa-thermometer-full:before {\n  content: \"\\f2c7\";\n}\n.fa-thermometer-3:before,\n.fa-thermometer-three-quarters:before {\n  content: \"\\f2c8\";\n}\n.fa-thermometer-2:before,\n.fa-thermometer-half:before {\n  content: \"\\f2c9\";\n}\n.fa-thermometer-1:before,\n.fa-thermometer-quarter:before {\n  content: \"\\f2ca\";\n}\n.fa-thermometer-0:before,\n.fa-thermometer-empty:before {\n  content: \"\\f2cb\";\n}\n.fa-shower:before {\n  content: \"\\f2cc\";\n}\n.fa-bathtub:before,\n.fa-s15:before,\n.fa-bath:before {\n  content: \"\\f2cd\";\n}\n.fa-podcast:before {\n  content: \"\\f2ce\";\n}\n.fa-window-maximize:before {\n  content: \"\\f2d0\";\n}\n.fa-window-minimize:before {\n  content: \"\\f2d1\";\n}\n.fa-window-restore:before {\n  content: \"\\f2d2\";\n}\n.fa-times-rectangle:before,\n.fa-window-close:before {\n  content: \"\\f2d3\";\n}\n.fa-times-rectangle-o:before,\n.fa-window-close-o:before {\n  content: \"\\f2d4\";\n}\n.fa-bandcamp:before {\n  content: \"\\f2d5\";\n}\n.fa-grav:before {\n  content: \"\\f2d6\";\n}\n.fa-etsy:before {\n  content: \"\\f2d7\";\n}\n.fa-imdb:before {\n  content: \"\\f2d8\";\n}\n.fa-ravelry:before {\n  content: \"\\f2d9\";\n}\n.fa-eercast:before {\n  content: \"\\f2da\";\n}\n.fa-microchip:before {\n  content: \"\\f2db\";\n}\n.fa-snowflake-o:before {\n  content: \"\\f2dc\";\n}\n.fa-superpowers:before {\n  content: \"\\f2dd\";\n}\n.fa-wpexplorer:before {\n  content: \"\\f2de\";\n}\n.fa-meetup:before {\n  content: \"\\f2e0\";\n}\n.visually-hidden {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n.visually-hidden-focusable:active,\n.visually-hidden-focusable:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  clip: auto;\n}\n", "\n/* /web/static/lib/odoo_ui_icons/style.css */\n@font-face {\n  font-family: 'odoo_ui_icons';\n  src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff');\n  font-weight: normal;\n  font-style: normal;\n  font-display: block;\n}\n\n.oi {\n  font-family: 'odoo_ui_icons';\n  speak: never;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.oi-close:before {\n  content: '\\00d7';\n  font-family: sans-serif;\n  font-weight: bold;\n}\n\n.oi-view-pivot:before { content: '\\e800'; }\n.oi-voip:before { content: '\\e803'; }\n.oi-odoo:before { content: '\\e806'; }\n.oi-search:before { content: '\\e808'; }\n.oi-group:before { content: '\\e80a'; }\n.oi-settings-adjust:before { content: '\\e80c'; }\n.oi-apps:before { content: '\\e80d'; }\n.oi-panel-right:before { content: '\\e810'; }\n.oi-launch:before { content: '\\e812'; }\n.oi-studio:before { content: '\\e813'; }\n.oi-view-kanban:before { content: '\\e814'; }\n.oi-view-cohort:before { content: '\\e816'; }\n.oi-view-list:before { content: '\\e817'; }\n", "\n/* /web/static/lib/select2/select2.css */\n/*\nVersion: 3.5.4 Timestamp: Sun Aug 30 13:30:32 EDT 2015\n*/\n.select2-container {\n    margin: 0;\n    position: relative;\n    display: inline-block;\n    vertical-align: middle;\n}\n\n.select2-container,\n.select2-drop,\n.select2-search,\n.select2-search input {\n  /*\n    Force border-box so that % widths fit the parent\n    container without overlap because of margin/padding.\n    More Info : http://www.quirksmode.org/css/box.html\n  */\n  -webkit-box-sizing: border-box; /* webkit */\n     -moz-box-sizing: border-box; /* firefox */\n          box-sizing: border-box; /* css3 */\n}\n\n.select2-container .select2-choice {\n    display: block;\n    height: 26px;\n    padding: 0 0 0 8px;\n    overflow: hidden;\n    position: relative;\n\n    border: 1px solid #aaa;\n    white-space: nowrap;\n    line-height: 26px;\n    color: #444;\n    text-decoration: none;\n\n    border-radius: 4px;\n\n    background-clip: padding-box;\n\n    -webkit-touch-callout: none;\n      -webkit-user-select: none;\n         -moz-user-select: none;\n          -ms-user-select: none;\n              user-select: none;\n\n    background-color: #fff;\n    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff));\n    background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 50%);\n    background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 50%);\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#ffffff', endColorstr = '#eeeeee', GradientType = 0);\n    background-image: linear-gradient(to top, #eee 0%, #fff 50%);\n}\n\nhtml[dir=\"rtl\"] .select2-container .select2-choice {\n    padding: 0 8px 0 0;\n}\n\n.select2-container.select2-drop-above .select2-choice {\n    border-bottom-color: #aaa;\n\n    border-radius: 0 0 4px 4px;\n\n    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, #fff));\n    background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 90%);\n    background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 90%);\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0);\n    background-image: linear-gradient(to bottom, #eee 0%, #fff 90%);\n}\n\n.select2-container.select2-allowclear .select2-choice .select2-chosen {\n    margin-right: 42px;\n}\n\n.select2-container .select2-choice > .select2-chosen {\n    margin-right: 26px;\n    display: block;\n    overflow: hidden;\n\n    white-space: nowrap;\n\n    text-overflow: ellipsis;\n    float: none;\n    width: auto;\n}\n\nhtml[dir=\"rtl\"] .select2-container .select2-choice > .select2-chosen {\n    margin-left: 26px;\n    margin-right: 0;\n}\n\n.select2-container .select2-choice abbr {\n    display: none;\n    width: 12px;\n    height: 12px;\n    position: absolute;\n    right: 24px;\n    top: 8px;\n\n    font-size: 1px;\n    text-decoration: none;\n\n    border: 0;\n    background: url('/web/static/lib/select2/select2.png') right top no-repeat;\n    cursor: pointer;\n    outline: 0;\n}\n\n.select2-container.select2-allowclear .select2-choice abbr {\n    display: inline-block;\n}\n\n.select2-container .select2-choice abbr:hover {\n    background-position: right -11px;\n    cursor: pointer;\n}\n\n.select2-drop-mask {\n    border: 0;\n    margin: 0;\n    padding: 0;\n    position: fixed;\n    left: 0;\n    top: 0;\n    min-height: 100%;\n    min-width: 100%;\n    height: auto;\n    width: auto;\n    opacity: 0;\n    z-index: 9998;\n    /* styles required for IE to work */\n    background-color: #fff;\n    filter: alpha(opacity=0);\n}\n\n.select2-drop {\n    width: 100%;\n    margin-top: -1px;\n    position: absolute;\n    z-index: 9999;\n    top: 100%;\n\n    background: #fff;\n    color: #000;\n    border: 1px solid #aaa;\n    border-top: 0;\n\n    border-radius: 0 0 4px 4px;\n\n    -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);\n            box-shadow: 0 4px 5px rgba(0, 0, 0, .15);\n}\n\n.select2-drop.select2-drop-above {\n    margin-top: 1px;\n    border-top: 1px solid #aaa;\n    border-bottom: 0;\n\n    border-radius: 4px 4px 0 0;\n\n    -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);\n            box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);\n}\n\n.select2-drop-active {\n    border: 1px solid #5897fb;\n    border-top: none;\n}\n\n.select2-drop.select2-drop-above.select2-drop-active {\n    border-top: 1px solid #5897fb;\n}\n\n.select2-drop-auto-width {\n    border-top: 1px solid #aaa;\n    width: auto;\n}\n\n.select2-container .select2-choice .select2-arrow {\n    display: inline-block;\n    width: 18px;\n    height: 100%;\n    position: absolute;\n    right: 0;\n    top: 0;\n\n    border-left: 1px solid #aaa;\n    border-radius: 0 4px 4px 0;\n\n    background-clip: padding-box;\n\n    background: #ccc;\n    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee));\n    background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%);\n    background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%);\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#eeeeee', endColorstr = '#cccccc', GradientType = 0);\n    background-image: linear-gradient(to top, #ccc 0%, #eee 60%);\n}\n\nhtml[dir=\"rtl\"] .select2-container .select2-choice .select2-arrow {\n    left: 0;\n    right: auto;\n\n    border-left: none;\n    border-right: 1px solid #aaa;\n    border-radius: 4px 0 0 4px;\n}\n\n.select2-container .select2-choice .select2-arrow b {\n    display: block;\n    width: 100%;\n    height: 100%;\n    background: url('/web/static/lib/select2/select2.png') no-repeat 0 1px;\n}\n\nhtml[dir=\"rtl\"] .select2-container .select2-choice .select2-arrow b {\n    background-position: 2px 1px;\n}\n\n.select2-search {\n    display: inline-block;\n    width: 100%;\n    min-height: 26px;\n    margin: 0;\n    padding: 4px 4px 0 4px;\n\n    position: relative;\n    z-index: 10000;\n\n    white-space: nowrap;\n}\n\n.select2-search input {\n    width: 100%;\n    height: auto !important;\n    min-height: 26px;\n    padding: 4px 20px 4px 5px;\n    margin: 0;\n\n    outline: 0;\n    font-family: sans-serif;\n    font-size: 1em;\n\n    border: 1px solid #aaa;\n    border-radius: 0;\n\n    -webkit-box-shadow: none;\n            box-shadow: none;\n\n    background: #fff url('/web/static/lib/select2/select2.png') no-repeat 100% -22px;\n    background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee));\n    background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;\n}\n\nhtml[dir=\"rtl\"] .select2-search input {\n    padding: 4px 5px 4px 20px;\n\n    background: #fff url('/web/static/lib/select2/select2.png') no-repeat -37px -22px;\n    background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee));\n    background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;\n}\n\n.select2-search input.select2-active {\n    background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%;\n    background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee));\n    background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%);\n    background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;\n}\n\n.select2-container-active .select2-choice,\n.select2-container-active .select2-choices {\n    border: 1px solid #5897fb;\n    outline: none;\n\n    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);\n            box-shadow: 0 0 5px rgba(0, 0, 0, .3);\n}\n\n.select2-dropdown-open .select2-choice {\n    border-bottom-color: transparent;\n    -webkit-box-shadow: 0 1px 0 #fff inset;\n            box-shadow: 0 1px 0 #fff inset;\n\n    border-bottom-left-radius: 0;\n    border-bottom-right-radius: 0;\n\n    background-color: #eee;\n    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #fff), color-stop(0.5, #eee));\n    background-image: -webkit-linear-gradient(center bottom, #fff 0%, #eee 50%);\n    background-image: -moz-linear-gradient(center bottom, #fff 0%, #eee 50%);\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);\n    background-image: linear-gradient(to top, #fff 0%, #eee 50%);\n}\n\n.select2-dropdown-open.select2-drop-above .select2-choice,\n.select2-dropdown-open.select2-drop-above .select2-choices {\n    border: 1px solid #5897fb;\n    border-top-color: transparent;\n\n    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(0.5, #eee));\n    background-image: -webkit-linear-gradient(center top, #fff 0%, #eee 50%);\n    background-image: -moz-linear-gradient(center top, #fff 0%, #eee 50%);\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);\n    background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);\n}\n\n.select2-dropdown-open .select2-choice .select2-arrow {\n    background: transparent;\n    border-left: none;\n    filter: none;\n}\nhtml[dir=\"rtl\"] .select2-dropdown-open .select2-choice .select2-arrow {\n    border-right: none;\n}\n\n.select2-dropdown-open .select2-choice .select2-arrow b {\n    background-position: -18px 1px;\n}\n\nhtml[dir=\"rtl\"] .select2-dropdown-open .select2-choice .select2-arrow b {\n    background-position: -16px 1px;\n}\n\n.select2-hidden-accessible {\n    border: 0;\n    clip: rect(0 0 0 0);\n    height: 1px;\n    margin: -1px;\n    overflow: hidden;\n    padding: 0;\n    position: absolute;\n    width: 1px;\n}\n\n/* results */\n.select2-results {\n    max-height: 200px;\n    padding: 0 0 0 4px;\n    margin: 4px 4px 4px 0;\n    position: relative;\n    overflow-x: hidden;\n    overflow-y: auto;\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n\nhtml[dir=\"rtl\"] .select2-results {\n    padding: 0 4px 0 0;\n    margin: 4px 0 4px 4px;\n}\n\n.select2-results ul.select2-result-sub {\n    margin: 0;\n    padding-left: 0;\n}\n\n.select2-results li {\n    list-style: none;\n    display: list-item;\n    background-image: none;\n}\n\n.select2-results li.select2-result-with-children > .select2-result-label {\n    font-weight: bold;\n}\n\n.select2-results .select2-result-label {\n    padding: 3px 7px 4px;\n    margin: 0;\n    cursor: pointer;\n\n    min-height: 1em;\n\n    -webkit-touch-callout: none;\n      -webkit-user-select: none;\n         -moz-user-select: none;\n          -ms-user-select: none;\n              user-select: none;\n}\n\n.select2-results-dept-1 .select2-result-label { padding-left: 20px }\n.select2-results-dept-2 .select2-result-label { padding-left: 40px }\n.select2-results-dept-3 .select2-result-label { padding-left: 60px }\n.select2-results-dept-4 .select2-result-label { padding-left: 80px }\n.select2-results-dept-5 .select2-result-label { padding-left: 100px }\n.select2-results-dept-6 .select2-result-label { padding-left: 110px }\n.select2-results-dept-7 .select2-result-label { padding-left: 120px }\n\n.select2-results .select2-highlighted {\n    background: #3875d7;\n    color: #fff;\n}\n\n.select2-results li em {\n    background: #feffde;\n    font-style: normal;\n}\n\n.select2-results .select2-highlighted em {\n    background: transparent;\n}\n\n.select2-results .select2-highlighted ul {\n    background: #fff;\n    color: #000;\n}\n\n.select2-results .select2-no-results,\n.select2-results .select2-searching,\n.select2-results .select2-ajax-error,\n.select2-results .select2-selection-limit {\n    background: #f4f4f4;\n    display: list-item;\n    padding-left: 5px;\n}\n\n/*\ndisabled look for disabled choices in the results dropdown\n*/\n.select2-results .select2-disabled.select2-highlighted {\n    color: #666;\n    background: #f4f4f4;\n    display: list-item;\n    cursor: default;\n}\n.select2-results .select2-disabled {\n  background: #f4f4f4;\n  display: list-item;\n  cursor: default;\n}\n\n.select2-results .select2-selected {\n    display: none;\n}\n\n.select2-more-results.select2-active {\n    background: #f4f4f4 url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%;\n}\n\n.select2-results .select2-ajax-error {\n    background: rgba(255, 50, 50, .2);\n}\n\n.select2-more-results {\n    background: #f4f4f4;\n    display: list-item;\n}\n\n/* disabled styles */\n\n.select2-container.select2-container-disabled .select2-choice {\n    background-color: #f4f4f4;\n    background-image: none;\n    border: 1px solid #ddd;\n    cursor: default;\n}\n\n.select2-container.select2-container-disabled .select2-choice .select2-arrow {\n    background-color: #f4f4f4;\n    background-image: none;\n    border-left: 0;\n}\n\n.select2-container.select2-container-disabled .select2-choice abbr {\n    display: none;\n}\n\n\n/* multiselect */\n\n.select2-container-multi .select2-choices {\n    height: auto !important;\n    height: 1%;\n    margin: 0;\n    padding: 0 5px 0 0;\n    position: relative;\n\n    border: 1px solid #aaa;\n    cursor: text;\n    overflow: hidden;\n\n    background-color: #fff;\n    background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eee), color-stop(15%, #fff));\n    background-image: -webkit-linear-gradient(top, #eee 1%, #fff 15%);\n    background-image: -moz-linear-gradient(top, #eee 1%, #fff 15%);\n    background-image: linear-gradient(to bottom, #eee 1%, #fff 15%);\n}\n\nhtml[dir=\"rtl\"] .select2-container-multi .select2-choices {\n    padding: 0 0 0 5px;\n}\n\n.select2-locked {\n  padding: 3px 5px 3px 5px !important;\n}\n\n.select2-container-multi .select2-choices {\n    min-height: 26px;\n}\n\n.select2-container-multi.select2-container-active .select2-choices {\n    border: 1px solid #5897fb;\n    outline: none;\n\n    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);\n            box-shadow: 0 0 5px rgba(0, 0, 0, .3);\n}\n.select2-container-multi .select2-choices li {\n    float: left;\n    list-style: none;\n}\nhtml[dir=\"rtl\"] .select2-container-multi .select2-choices li\n{\n    float: right;\n}\n.select2-container-multi .select2-choices .select2-search-field {\n    margin: 0;\n    padding: 0;\n    white-space: nowrap;\n}\n\n.select2-container-multi .select2-choices .select2-search-field input {\n    padding: 5px;\n    margin: 1px 0;\n\n    font-family: sans-serif;\n    font-size: 100%;\n    color: #666;\n    outline: 0;\n    border: 0;\n    -webkit-box-shadow: none;\n            box-shadow: none;\n    background: transparent !important;\n}\n\n.select2-container-multi .select2-choices .select2-search-field input.select2-active {\n    background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100% !important;\n}\n\n.select2-default {\n    color: #999 !important;\n}\n\n.select2-container-multi .select2-choices .select2-search-choice {\n    padding: 3px 5px 3px 18px;\n    margin: 3px 0 3px 5px;\n    position: relative;\n\n    line-height: 13px;\n    color: #333;\n    cursor: default;\n    border: 1px solid #aaaaaa;\n\n    border-radius: 3px;\n\n    -webkit-box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05);\n            box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05);\n\n    background-clip: padding-box;\n\n    -webkit-touch-callout: none;\n      -webkit-user-select: none;\n         -moz-user-select: none;\n          -ms-user-select: none;\n              user-select: none;\n\n    background-color: #e4e4e4;\n    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0);\n    background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee));\n    background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);\n    background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);\n    background-image: linear-gradient(to bottom, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);\n}\nhtml[dir=\"rtl\"] .select2-container-multi .select2-choices .select2-search-choice\n{\n    margin: 3px 5px 3px 0;\n    padding: 3px 18px 3px 5px;\n}\n.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {\n    cursor: default;\n}\n.select2-container-multi .select2-choices .select2-search-choice-focus {\n    background: #d4d4d4;\n}\n\n.select2-search-choice-close {\n    display: block;\n    width: 12px;\n    height: 13px;\n    position: absolute;\n    right: 3px;\n    top: 4px;\n\n    font-size: 1px;\n    outline: none;\n    background: url('/web/static/lib/select2/select2.png') right top no-repeat;\n}\nhtml[dir=\"rtl\"] .select2-search-choice-close {\n    right: auto;\n    left: 3px;\n}\n\n.select2-container-multi .select2-search-choice-close {\n    left: 3px;\n}\n\nhtml[dir=\"rtl\"] .select2-container-multi .select2-search-choice-close {\n    left: auto;\n    right: 2px;\n}\n\n.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {\n  background-position: right -11px;\n}\n.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {\n    background-position: right -11px;\n}\n\n/* disabled styles */\n.select2-container-multi.select2-container-disabled .select2-choices {\n    background-color: #f4f4f4;\n    background-image: none;\n    border: 1px solid #ddd;\n    cursor: default;\n}\n\n.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {\n    padding: 3px 5px 3px 5px;\n    border: 1px solid #ddd;\n    background-image: none;\n    background-color: #f4f4f4;\n}\n\n.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {    display: none;\n    background: none;\n}\n/* end multiselect */\n\n\n.select2-result-selectable .select2-match,\n.select2-result-unselectable .select2-match {\n    text-decoration: underline;\n}\n\n.select2-offscreen, .select2-offscreen:focus {\n    clip: rect(0 0 0 0) !important;\n    width: 1px !important;\n    height: 1px !important;\n    border: 0 !important;\n    margin: 0 !important;\n    padding: 0 !important;\n    overflow: hidden !important;\n    position: absolute !important;\n    outline: 0 !important;\n    left: 0px !important;\n    top: 0px !important;\n}\n\n.select2-display-none {\n    display: none;\n}\n\n.select2-measure-scrollbar {\n    position: absolute;\n    top: -10000px;\n    left: -10000px;\n    width: 100px;\n    height: 100px;\n    overflow: scroll;\n}\n\n/* Retina-ize icons */\n\n@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx)  {\n    .select2-search input,\n    .select2-search-choice-close,\n    .select2-container .select2-choice abbr,\n    .select2-container .select2-choice .select2-arrow b {\n        background-image: url('/web/static/lib/select2/select2x2.png') !important;\n        background-repeat: no-repeat !important;\n        background-size: 60px 40px !important;\n    }\n\n    .select2-search input {\n        background-position: 100% -21px !important;\n    }\n}\n", "\n/* /web/static/lib/select2-bootstrap-css/select2-bootstrap.css */\n/*\nVersion: 3.5.1 Timestamp: Tue Jul 22 18:58:56 EDT 2014\n*/\n.form-control .select2-choice {\n    border: 0;\n    border-radius: 2px;\n}\n\n.form-control .select2-choice .select2-arrow {\n    border-radius: 0 2px 2px 0;   \n}\n\n.form-control.select2-container {\n    height: auto !important;\n    padding: 0;\n}\n\n.form-control.select2-container.select2-dropdown-open {\n    border-color: #5897FB;\n    border-radius: 3px 3px 0 0;\n}\n\n.form-control .select2-container.select2-dropdown-open .select2-choices {\n    border-radius: 3px 3px 0 0;\n}\n\n.form-control.select2-container .select2-choices {\n    border: 0 !important;\n    border-radius: 3px;\n}\n\n.control-group.warning .select2-container .select2-choice,\n.control-group.warning .select2-container .select2-choices,\n.control-group.warning .select2-container-active .select2-choice,\n.control-group.warning .select2-container-active .select2-choices,\n.control-group.warning .select2-dropdown-open.select2-drop-above .select2-choice,\n.control-group.warning .select2-dropdown-open.select2-drop-above .select2-choices,\n.control-group.warning .select2-container-multi.select2-container-active .select2-choices {\n    border: 1px solid #C09853 !important;\n}\n\n.control-group.warning .select2-container .select2-choice div {\n    border-left: 1px solid #C09853 !important;\n    background: #FCF8E3 !important;\n}\n\n.control-group.error .select2-container .select2-choice,\n.control-group.error .select2-container .select2-choices,\n.control-group.error .select2-container-active .select2-choice,\n.control-group.error .select2-container-active .select2-choices,\n.control-group.error .select2-dropdown-open.select2-drop-above .select2-choice,\n.control-group.error .select2-dropdown-open.select2-drop-above .select2-choices,\n.control-group.error .select2-container-multi.select2-container-active .select2-choices {\n    border: 1px solid #B94A48 !important;\n}\n\n.control-group.error .select2-container .select2-choice div {\n    border-left: 1px solid #B94A48 !important;\n    background: #F2DEDE !important;\n}\n\n.control-group.info .select2-container .select2-choice,\n.control-group.info .select2-container .select2-choices,\n.control-group.info .select2-container-active .select2-choice,\n.control-group.info .select2-container-active .select2-choices,\n.control-group.info .select2-dropdown-open.select2-drop-above .select2-choice,\n.control-group.info .select2-dropdown-open.select2-drop-above .select2-choices,\n.control-group.info .select2-container-multi.select2-container-active .select2-choices {\n    border: 1px solid #3A87AD !important;\n}\n\n.control-group.info .select2-container .select2-choice div {\n    border-left: 1px solid #3A87AD !important;\n    background: #D9EDF7 !important;\n}\n\n.control-group.success .select2-container .select2-choice,\n.control-group.success .select2-container .select2-choices,\n.control-group.success .select2-container-active .select2-choice,\n.control-group.success .select2-container-active .select2-choices,\n.control-group.success .select2-dropdown-open.select2-drop-above .select2-choice,\n.control-group.success .select2-dropdown-open.select2-drop-above .select2-choices,\n.control-group.success .select2-container-multi.select2-container-active .select2-choices {\n    border: 1px solid #468847 !important;\n}\n\n.control-group.success .select2-container .select2-choice div {\n    border-left: 1px solid #468847 !important;\n    background: #DFF0D8 !important;\n}", "\n/* /web/static/lib/daterangepicker/daterangepicker.css */\n.daterangepicker {\n  position: absolute;\n  color: inherit;\n  background-color: #fff;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  width: 278px;\n  max-width: none;\n  padding: 0;\n  margin-top: 7px;\n  top: 100px;\n  left: 20px;\n  z-index: 3001;\n  display: none;\n  font-family: arial;\n  font-size: 15px;\n  line-height: 1em;\n}\n\n.daterangepicker:before, .daterangepicker:after {\n  position: absolute;\n  display: inline-block;\n  border-bottom-color: rgba(0, 0, 0, 0.2);\n  content: '';\n}\n\n.daterangepicker:before {\n  top: -7px;\n  border-right: 7px solid transparent;\n  border-left: 7px solid transparent;\n  border-bottom: 7px solid #ccc;\n}\n\n.daterangepicker:after {\n  top: -6px;\n  border-right: 6px solid transparent;\n  border-bottom: 6px solid #fff;\n  border-left: 6px solid transparent;\n}\n\n.daterangepicker.opensleft:before {\n  right: 9px;\n}\n\n.daterangepicker.opensleft:after {\n  right: 10px;\n}\n\n.daterangepicker.openscenter:before {\n  left: 0;\n  right: 0;\n  width: 0;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.daterangepicker.openscenter:after {\n  left: 0;\n  right: 0;\n  width: 0;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.daterangepicker.opensright:before {\n  left: 9px;\n}\n\n.daterangepicker.opensright:after {\n  left: 10px;\n}\n\n.daterangepicker.drop-up {\n  margin-top: -7px;\n}\n\n.daterangepicker.drop-up:before {\n  top: initial;\n  bottom: -7px;\n  border-bottom: initial;\n  border-top: 7px solid #ccc;\n}\n\n.daterangepicker.drop-up:after {\n  top: initial;\n  bottom: -6px;\n  border-bottom: initial;\n  border-top: 6px solid #fff;\n}\n\n.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {\n  float: none;\n}\n\n.daterangepicker.single .drp-selected {\n  display: none;\n}\n\n.daterangepicker.show-calendar .drp-calendar {\n  display: block;\n}\n\n.daterangepicker.show-calendar .drp-buttons {\n  display: block;\n}\n\n.daterangepicker.auto-apply .drp-buttons {\n  display: none;\n}\n\n.daterangepicker .drp-calendar {\n  display: none;\n  max-width: 270px;\n}\n\n.daterangepicker .drp-calendar.left {\n  padding: 8px 0 8px 8px;\n}\n\n.daterangepicker .drp-calendar.right {\n  padding: 8px;\n}\n\n.daterangepicker .drp-calendar.single .calendar-table {\n  border: none;\n}\n\n.daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {\n  color: #fff;\n  border: solid black;\n  border-width: 0 2px 2px 0;\n  border-radius: 0;\n  display: inline-block;\n  padding: 3px;\n}\n\n.daterangepicker .calendar-table .next span {\n  transform: rotate(-45deg);\n  -webkit-transform: rotate(-45deg);\n}\n\n.daterangepicker .calendar-table .prev span {\n  transform: rotate(135deg);\n  -webkit-transform: rotate(135deg);\n}\n\n.daterangepicker .calendar-table th, .daterangepicker .calendar-table td {\n  white-space: nowrap;\n  text-align: center;\n  vertical-align: middle;\n  min-width: 32px;\n  width: 32px;\n  height: 24px;\n  line-height: 24px;\n  font-size: 12px;\n  border-radius: 4px;\n  border: 1px solid transparent;\n  white-space: nowrap;\n  cursor: pointer;\n}\n\n.daterangepicker .calendar-table {\n  border: 1px solid #fff;\n  border-radius: 4px;\n  background-color: #fff;\n}\n\n.daterangepicker .calendar-table table {\n  width: 100%;\n  margin: 0;\n  border-spacing: 0;\n  border-collapse: collapse;\n}\n\n.daterangepicker td.available:hover, .daterangepicker th.available:hover {\n  background-color: #eee;\n  border-color: transparent;\n  color: inherit;\n}\n\n.daterangepicker td.week, .daterangepicker th.week {\n  font-size: 80%;\n  color: #ccc;\n}\n\n.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {\n  background-color: #fff;\n  border-color: transparent;\n  color: #999;\n}\n\n.daterangepicker td.in-range {\n  background-color: #ebf4f8;\n  border-color: transparent;\n  color: #000;\n  border-radius: 0;\n}\n\n.daterangepicker td.start-date {\n  border-radius: 4px 0 0 4px;\n}\n\n.daterangepicker td.end-date {\n  border-radius: 0 4px 4px 0;\n}\n\n.daterangepicker td.start-date.end-date {\n  border-radius: 4px;\n}\n\n.daterangepicker td.active, .daterangepicker td.active:hover {\n  background-color: #357ebd;\n  border-color: transparent;\n  color: #fff;\n}\n\n.daterangepicker th.month {\n  width: auto;\n}\n\n.daterangepicker td.disabled, .daterangepicker option.disabled {\n  color: #999;\n  cursor: not-allowed;\n  text-decoration: line-through;\n}\n\n.daterangepicker select.monthselect, .daterangepicker select.yearselect {\n  font-size: 12px;\n  padding: 1px;\n  height: auto;\n  margin: 0;\n  cursor: default;\n}\n\n.daterangepicker select.monthselect {\n  margin-right: 2%;\n  width: 56%;\n}\n\n.daterangepicker select.yearselect {\n  width: 40%;\n}\n\n.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {\n  width: 50px;\n  margin: 0 auto;\n  background: #eee;\n  border: 1px solid #eee;\n  padding: 2px;\n  outline: 0;\n  font-size: 12px;\n}\n\n.daterangepicker .calendar-time {\n  text-align: center;\n  margin: 4px auto 0 auto;\n  line-height: 30px;\n  position: relative;\n}\n\n.daterangepicker .calendar-time select.disabled {\n  color: #ccc;\n  cursor: not-allowed;\n}\n\n.daterangepicker .drp-buttons {\n  clear: both;\n  text-align: right;\n  padding: 8px;\n  border-top: 1px solid #ddd;\n  display: none;\n  line-height: 12px;\n  vertical-align: middle;\n}\n\n.daterangepicker .drp-selected {\n  display: inline-block;\n  font-size: 12px;\n  padding-right: 8px;\n}\n\n.daterangepicker .drp-buttons .btn {\n  margin-left: 8px;\n  font-size: 12px;\n  font-weight: bold;\n  padding: 4px 8px;\n}\n\n.daterangepicker.show-ranges.single.rtl .drp-calendar.left {\n  border-right: 1px solid #ddd;\n}\n\n.daterangepicker.show-ranges.single.ltr .drp-calendar.left {\n  border-left: 1px solid #ddd;\n}\n\n.daterangepicker.show-ranges.rtl .drp-calendar.right {\n  border-right: 1px solid #ddd;\n}\n\n.daterangepicker.show-ranges.ltr .drp-calendar.left {\n  border-left: 1px solid #ddd;\n}\n\n.daterangepicker .ranges {\n  float: none;\n  text-align: left;\n  margin: 0;\n}\n\n.daterangepicker.show-calendar .ranges {\n  margin-top: 8px;\n}\n\n.daterangepicker .ranges ul {\n  list-style: none;\n  margin: 0 auto;\n  padding: 0;\n  width: 100%;\n}\n\n.daterangepicker .ranges li {\n  font-size: 12px;\n  padding: 8px 12px;\n  cursor: pointer;\n}\n\n.daterangepicker .ranges li:hover {\n  background-color: #eee;\n}\n\n.daterangepicker .ranges li.active {\n  background-color: #08c;\n  color: #fff;\n}\n\n/*  Larger Screen Styling */\n@media (min-width: 564px) {\n  .daterangepicker {\n    width: auto;\n  }\n\n  .daterangepicker .ranges ul {\n    width: 140px;\n  }\n\n  .daterangepicker.single .ranges ul {\n    width: 100%;\n  }\n\n  .daterangepicker.single .drp-calendar.left {\n    clear: none;\n  }\n\n  .daterangepicker.single .ranges, .daterangepicker.single .drp-calendar {\n    float: left;\n  }\n\n  .daterangepicker {\n    direction: ltr;\n    text-align: left;\n  }\n\n  .daterangepicker .drp-calendar.left {\n    clear: left;\n    margin-right: 0;\n  }\n\n  .daterangepicker .drp-calendar.left .calendar-table {\n    border-right: none;\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  .daterangepicker .drp-calendar.right {\n    margin-left: 0;\n  }\n\n  .daterangepicker .drp-calendar.right .calendar-table {\n    border-left: none;\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n\n  .daterangepicker .drp-calendar.left .calendar-table {\n    padding-right: 8px;\n  }\n\n  .daterangepicker .ranges, .daterangepicker .drp-calendar {\n    float: left;\n  }\n}\n\n@media (min-width: 730px) {\n  .daterangepicker .ranges {\n    width: auto;\n  }\n\n  .daterangepicker .ranges {\n    float: left;\n  }\n\n  .daterangepicker.rtl .ranges {\n    float: right;\n  }\n\n  .daterangepicker .drp-calendar.left {\n    clear: none !important;\n  }\n}\n", "\n/* /web/static/src/webclient/navbar/navbar.scss */\n\n.o_main_navbar {\n  --o-navbar-height: 46px;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  height: var(--o-navbar-height);\n  min-width: min-content;\n  border-bottom: 0;\n  background: linear-gradient(45deg, #714B67, #5e4a59);\n}\n\n@media (min-width: 992px) {\n  .o_main_navbar {\n    --o-navbar-height: 40px;\n  }\n}\n\n.o_main_navbar > ul {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n}\n\n.o_main_navbar .dropdown-toggle {\n  border: 0;\n}\n\n.o_main_navbar .dropdown-menu {\n  margin-top: 0;\n  border-top: 0;\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item {\n  padding-left: 30px;\n}\n\n.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item + .dropdown-item:not(.o_dropdown_menu_group_entry) {\n  margin-top: .8em;\n}\n\n.o_main_navbar .o_navbar_apps_menu .dropdown-toggle {\n  padding-left: 16px;\n  font-size: 1.40833333rem;\n}\n\n.o_main_navbar .o_menu_brand {\n  padding-left: 0;\n  font-size: 1.40833333rem;\n}\n\n.o_main_navbar .o_menu_brand:hover {\n  background: none;\n}\n\n.o_main_navbar .o_menu_sections {\n  width: 0;\n}\n\n.o_main_navbar .o_menu_sections .o_more_dropdown_section_group {\n  margin-top: .8em;\n}\n\n.o_main_navbar .o_menu_sections .o_more_dropdown_section_group:first-child {\n  margin-top: -0.5rem;\n  padding-top: 0.75rem;\n}\n\n@media (min-width: 992px) {\n  .o_main_navbar .o_menu_systray > * > a, .o_main_navbar .o_menu_systray > * > .dropdown > a, .o_main_navbar .o_menu_systray > * > button, .o_main_navbar .o_menu_systray > * > label {\n    padding: 0 9.6px;\n  }\n}\n\n.o_main_navbar .o_menu_systray > * > a .badge, .o_main_navbar .o_menu_systray > * > .dropdown > a .badge, .o_main_navbar .o_menu_systray > * > button .badge, .o_main_navbar .o_menu_systray > * > label .badge {\n  margin-right: -.5em;\n  border: 0;\n  height: 15px;\n  padding: 2px 4px;\n  background-color: var(--o-navbar-badge-bg, #01939a);\n  font-size: 11px;\n  color: var(--o-navbar-badge-color, inherit);\n  text-shadow: var(--o-navbar-badge-text-shadow, 1px 1px 0 rgba(0, 0, 0, 0.3));\n  transform: translate(-10%, -30%);\n}\n\nbody.o_is_superuser .o_menu_systray {\n  position: relative;\n  background: repeating-linear-gradient(135deg, #d9b904, #d9b904 10px, #373435 10px, #373435 20px);\n}\n\nbody.o_is_superuser .o_menu_systray:before {\n  content: \"\";\n  position: absolute;\n  top: 2px;\n  left: 2px;\n  bottom: 2px;\n  right: 2px;\n  background-color: #714B67;\n}\n\n", "\n/* /web/static/src/legacy/scss/ui.scss */\n\n:root .o_hidden {\n  display: none !important;\n}\n\n.o_disabled {\n  pointer-events: none;\n  opacity: 0.5;\n}\n\n.o_text_overflow {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n}\n\n.ui-autocomplete {\n  z-index: 1056;\n  max-width: 600px;\n}\n\n.ui-autocomplete .ui-menu-item > a {\n  display: block;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n}\n\n.dropdown-toggle.o-no-caret::before, .dropdown-toggle.o-no-caret::after {\n  content: normal;\n}\n\n:not(.collapsed) > .o-collapsed-label,\n.collapsed > .o-not-collapsed-label {\n  display: none;\n}\n\n.o_rtl .ui-autocomplete {\n  direction: ltr;\n  right: 0;\n  left: auto;\n}\n\n.o_rtl .ui-datepicker-next, .o_rtl .ui-datepicker-prev {\n  -webkit-transform: rotate(180deg);\n  -o-transform: rotate(180deg);\n  transform: rotate(180deg);\n}\n\n.o_catch_attention {\n  position: relative;\n  z-index: 1;\n  animation: catchAttention 200ms ease 0s infinite normal;\n}\n\n.o_treeEntry {\n  padding-left: var(--treeEntry-padding-h, 1.5rem);\n  position: relative;\n}\n\n.o_treeEntry:before, .o_treeEntry:after {\n  position: absolute;\n  left: var(--treeEntry--beforeAfter-left, calc(var(--treeEntry-padding-h, 1.5rem) * .5));\n  background: var(--treeEntry--beforeAfter-color, #dee2e6);\n  content: '';\n}\n\n.o_treeEntry:before {\n  top: var(--treeEntry--before-top, 0);\n  width: 1px;\n  height: 100%;\n}\n\n.o_treeEntry:after {\n  display: var(--treeEntry--after-display, initial);\n  top: calc(.5em + var(--treeEntry-padding-v, 0.5rem));\n  width: calc(var(--treeEntry-padding-h, 1.5rem) * .5);\n  height: 1px;\n}\n\n.o_treeEntry:last-of-type:before {\n  height: calc(.5em + var(--treeEntry-padding-v, 0.5rem));\n}\n\n@keyframes catchAttention {\n  0%, 20%, 40%, 60%, 80%, 100% {\n    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  0% {\n    transform: translateY(-30%);\n  }\n  20% {\n    transform: translateY(-25%);\n  }\n  40% {\n    transform: translateY(-20%);\n  }\n  60% {\n    transform: translateY(-15%);\n  }\n  80% {\n    transform: translateY(-10%);\n  }\n  100% {\n    transform: translateY(-5%);\n  }\n}\n\nspan.o_force_ltr {\n  display: inline;\n}\n\n.o_force_ltr {\n  unicode-bidi: embed;\n  /*rtl:ignore*/\n  direction: ltr;\n}\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"],\n.o_force_ltr {\n  text-align: end !important;\n}\n*/\n.o_object_fit_cover {\n  object-fit: cover;\n}\n\n.o_image_24_cover {\n  width: 24px;\n  height: 24px;\n  object-fit: cover;\n}\n\n.o_image_40_cover {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n}\n\n.o_image_64_cover {\n  width: 64px;\n  height: 64px;\n  object-fit: cover;\n}\n\n.o_image_64_contain {\n  width: 64px;\n  height: 64px;\n  object-fit: contain;\n}\n\n.o_image_64_max {\n  max-width: 64px;\n  max-height: 64px;\n}\n\n.o_image_128_max {\n  max-width: 128px !important;\n  max-height: 128px !important;\n}\n\n.o_width_128 {\n  width: 128px;\n}\n\n.o_web_accesskey_overlay {\n  position: absolute;\n  top: 0;\n  left: auto;\n  bottom: auto;\n  right: 0;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: #FFFFFF;\n  font-size: 1rem;\n  font-family: sans-serif;\n}\n\n", "\n/* /web/static/src/legacy/scss/mimetypes.scss */\n\n.o_image {\n  display: inline-block;\n  width: 38px;\n  height: 38px;\n  background-image: url(\"/web/static/img/mimetypes/unknown.svg\");\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n\n.o_image[data-mimetype^='image'] {\n  background-image: url(\"/web/static/img/mimetypes/image.svg\");\n}\n\n.o_image[data-mimetype^='audio'] {\n  background-image: url(\"/web/static/img/mimetypes/audio.svg\");\n}\n\n.o_image[data-mimetype^='text'], .o_image[data-mimetype$='rtf'] {\n  background-image: url(\"/web/static/img/mimetypes/text.svg\");\n}\n\n.o_image[data-mimetype*='octet-stream'], .o_image[data-mimetype*='download'], .o_image[data-mimetype*='python'] {\n  background-image: url(\"/web/static/img/mimetypes/binary.svg\");\n}\n\n.o_image[data-mimetype^='video'], .o_image[title$='.mp4'], .o_image[title$='.avi'] {\n  background-image: url(\"/web/static/img/mimetypes/video.svg\");\n}\n\n.o_image[data-mimetype$='archive'], .o_image[data-mimetype$='compressed'], .o_image[data-mimetype*='zip'], .o_image[data-mimetype$='tar'], .o_image[data-mimetype*='package'] {\n  background-image: url(\"/web/static/img/mimetypes/archive.svg\");\n}\n\n.o_image[data-mimetype='application/pdf'] {\n  background-image: url(\"/web/static/img/mimetypes/pdf.svg\");\n}\n\n.o_image[data-mimetype^='text-master'], .o_image[data-mimetype*='document'], .o_image[data-mimetype*='msword'], .o_image[data-mimetype*='wordprocessing'] {\n  background-image: url(\"/web/static/img/mimetypes/document.svg\");\n}\n\n.o_image[data-mimetype*='application/xml'], .o_image[data-mimetype$='html'] {\n  background-image: url(\"/web/static/img/mimetypes/web_code.svg\");\n}\n\n.o_image[data-mimetype$='css'], .o_image[data-mimetype$='less'], .o_image[data-ext$='less'] {\n  background-image: url(\"/web/static/img/mimetypes/web_style.svg\");\n}\n\n.o_image[data-mimetype*='-image'], .o_image[data-mimetype*='diskimage'], .o_image[data-ext$='dmg'] {\n  background-image: url(\"/web/static/img/mimetypes/disk.svg\");\n}\n\n.o_image[data-mimetype$='csv'], .o_image[data-mimetype*='vc'], .o_image[data-mimetype*='excel'], .o_image[data-mimetype$='numbers'], .o_image[data-mimetype$='calc'], .o_image[data-mimetype*='mods'], .o_image[data-mimetype*='spreadsheet'] {\n  background-image: url(\"/web/static/img/mimetypes/spreadsheet.svg\");\n}\n\n.o_image[data-mimetype^='key'] {\n  background-image: url(\"/web/static/img/mimetypes/certificate.svg\");\n}\n\n.o_image[data-mimetype*='presentation'], .o_image[data-mimetype*='keynote'], .o_image[data-mimetype*='teacher'], .o_image[data-mimetype*='slideshow'], .o_image[data-mimetype*='powerpoint'] {\n  background-image: url(\"/web/static/img/mimetypes/presentation.svg\");\n}\n\n.o_image[data-mimetype*='cert'], .o_image[data-mimetype*='rules'], .o_image[data-mimetype*='pkcs'], .o_image[data-mimetype$='stl'], .o_image[data-mimetype$='crl'] {\n  background-image: url(\"/web/static/img/mimetypes/certificate.svg\");\n}\n\n.o_image[data-mimetype*='-font'], .o_image[data-mimetype*='font-'], .o_image[data-ext$='ttf'] {\n  background-image: url(\"/web/static/img/mimetypes/font.svg\");\n}\n\n.o_image[data-mimetype*='-dvi'] {\n  background-image: url(\"/web/static/img/mimetypes/print.svg\");\n}\n\n.o_image[data-mimetype*='script'], .o_image[data-mimetype*='x-sh'], .o_image[data-ext*='bat'], .o_image[data-mimetype$='bat'], .o_image[data-mimetype$='cgi'], .o_image[data-mimetype$='-c'], .o_image[data-mimetype*='java'], .o_image[data-mimetype*='ruby'] {\n  background-image: url(\"/web/static/img/mimetypes/script.svg\");\n}\n\n.o_image[data-mimetype*='javascript'] {\n  background-image: url(\"/web/static/img/mimetypes/javascript.svg\");\n}\n\n.o_image[data-mimetype*='calendar'], .o_image[data-mimetype$='ldif'] {\n  background-image: url(\"/web/static/img/mimetypes/calendar.svg\");\n}\n\n.o_image[data-mimetype$='postscript'], .o_image[data-mimetype$='cdr'], .o_image[data-mimetype$='xara'], .o_image[data-mimetype$='cgm'], .o_image[data-mimetype$='graphics'], .o_image[data-mimetype$='draw'], .o_image[data-mimetype*='svg'] {\n  background-image: url(\"/web/static/img/mimetypes/vector.svg\");\n}\n\n", "\n/* /web/static/src/legacy/scss/modal.scss */\n\n.modal.o_technical_modal .modal-content .modal-header .o_subtitle {\n  margin-left: 10px;\n}\n\n.modal.o_technical_modal .modal-content .modal-header .modal-title {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.modal.o_technical_modal .modal-content .modal-body.o_act_window {\n  padding: 0;\n}\n\n@media (max-width: 767.98px) {\n  .modal.o_technical_modal .modal-content .modal-body.o_act_window .o_action {\n    display: -webkit-box; display: -webkit-flex; display: flex;\n    -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;\n    height: 100%;\n  }\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_header {\n  padding-top: 10px;\n  padding-right: 16px;\n  padding-bottom: 10px;\n  padding-left: 16px;\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_header::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_header .o_search_options {\n  display: inline-block;\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_header .o_pager {\n  float: right;\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_changes td:first-child {\n  padding-right: 10px;\n  vertical-align: top;\n  white-space: nowrap;\n}\n\n.modal.o_technical_modal .modal-content .modal-body .o_modal_changes td:not(:first-child) {\n  width: 100%;\n}\n\n.modal.o_technical_modal .modal-content .modal-footer {\n  text-align: left;\n}\n\n@media (min-width: 576px) {\n  .modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error {\n    overflow: visible;\n    display: -webkit-box; display: -webkit-flex; display: flex;\n    -webkit-flex-flow: column nowrap; flex-flow: column nowrap;\n  }\n  .modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > .alert, .modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > button {\n    -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  }\n  .modal.o_technical_modal .modal-dialog .modal-content .modal-body.o_dialog_error > .o_error_detail {\n    -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n    min-height: 0;\n    overflow: auto;\n  }\n}\n\n@media (max-width: 575.98px) {\n  .modal.o_technical_modal.o_modal_full .modal-dialog {\n    margin: 0px;\n    height: 100%;\n  }\n  .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content {\n    height: 100%;\n    border: none;\n  }\n  .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header {\n    background: linear-gradient(45deg, #714B67, #5e4a59);\n  }\n  .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .modal-title, .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .o_subtitle, .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header button.close {\n    color: #FFF;\n  }\n  .modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-body {\n    height: 100%;\n    overflow-y: auto;\n  }\n}\n\n.modal.o_inactive_modal {\n  z-index: 1049;\n}\n\n.o_dialog > .modal {\n  display: block;\n}\n\n", "\n/* /web/static/src/legacy/scss/animation.scss */\n\n/************** DEFINITION of ANIMATIONS **************/\n@keyframes markAnim {\n  0% {\n    opacity: 0;\n    transform: scaleX(0.5) scaleY(0.5);\n  }\n  30% {\n    opacity: 1;\n    transform: scaleX(1) scaleY(1);\n  }\n  100% {\n    opacity: 0;\n    transform: scaleX(1) scaleY(1);\n  }\n}\n\n@keyframes bounceIn {\n  0%, 20%, 40%, 60%, 80%, 100% {\n    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  }\n  0% {\n    opacity: 0;\n    transform: scale3d(0.3, 0.3, 0.3);\n  }\n  20% {\n    transform: scale3d(1.1, 1.1, 1.1);\n  }\n  40% {\n    transform: scale3d(0.9, 0.9, 0.9);\n  }\n  60% {\n    opacity: 1;\n    transform: scale3d(1.03, 1.03, 1.03);\n  }\n  80% {\n    transform: scale3d(0.97, 0.97, 0.97);\n  }\n  100% {\n    opacity: 1;\n    transform: scale3d(1, 1, 1);\n  }\n}\n\n@keyframes flash {\n  from, 50%, to {\n    opacity: 1;\n  }\n  25%, 75% {\n    opacity: 0;\n  }\n}\n\n", "\n/* /web/static/src/legacy/scss/datepicker.scss */\n\n.visually-hidden {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  margin: -1px;\n  padding: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n\n.o_datepicker {\n  position: relative;\n}\n\n.o_datepicker .o_datepicker_input {\n  width: 100%;\n  cursor: pointer;\n}\n\n.o_datepicker .o_datepicker_button {\n  position: absolute;\n  top: 2px;\n  left: auto;\n  bottom: auto;\n  right: 4px;\n  pointer-events: none;\n}\n\n.o_datepicker .o_datepicker_button:after {\n  content: \"\";\n  display: inline-block;\n  width: 0;\n  height: 0;\n  vertical-align: middle;\n  -moz-transform: scale(0.9999);\n  border-bottom: 0;\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 4px solid var(--o-caret-color, currentColor);\n}\n\n.o_datepicker .o_datepicker_warning {\n  top: 0;\n  right: 20px;\n}\n\ndiv.dropdown-menu.bootstrap-datetimepicker-widget {\n  min-width: 0;\n  padding: 0;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  overflow: visible;\n  z-index: 1056;\n  width: 22rem;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table {\n  --datepicker-color: #212529;\n  --datepicker__thead-bg: #f8f9fa;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table th, .bootstrap-datetimepicker-widget .datepicker table td {\n  border-radius: 0;\n  border-bottom-width: 1px;\n  width: 12.5%;\n  height: 30px;\n  line-height: 30px;\n  color: var(--datepicker-color);\n}\n\n.bootstrap-datetimepicker-widget .datepicker table th.prev, .bootstrap-datetimepicker-widget .datepicker table th.next, .bootstrap-datetimepicker-widget .datepicker table th.old, .bootstrap-datetimepicker-widget .datepicker table th.new, .bootstrap-datetimepicker-widget .datepicker table td.prev, .bootstrap-datetimepicker-widget .datepicker table td.next, .bootstrap-datetimepicker-widget .datepicker table td.old, .bootstrap-datetimepicker-widget .datepicker table td.new {\n  opacity: 0.5;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table th.prev:hover, .bootstrap-datetimepicker-widget .datepicker table th.prev:focus, .bootstrap-datetimepicker-widget .datepicker table th.prev.focus, .bootstrap-datetimepicker-widget .datepicker table th.next:hover, .bootstrap-datetimepicker-widget .datepicker table th.next:focus, .bootstrap-datetimepicker-widget .datepicker table th.next.focus, .bootstrap-datetimepicker-widget .datepicker table th.old:hover, .bootstrap-datetimepicker-widget .datepicker table th.old:focus, .bootstrap-datetimepicker-widget .datepicker table th.old.focus, .bootstrap-datetimepicker-widget .datepicker table th.new:hover, .bootstrap-datetimepicker-widget .datepicker table th.new:focus, .bootstrap-datetimepicker-widget .datepicker table th.new.focus, .bootstrap-datetimepicker-widget .datepicker table td.prev:hover, .bootstrap-datetimepicker-widget .datepicker table td.prev:focus, .bootstrap-datetimepicker-widget .datepicker table td.prev.focus, .bootstrap-datetimepicker-widget .datepicker table td.next:hover, .bootstrap-datetimepicker-widget .datepicker table td.next:focus, .bootstrap-datetimepicker-widget .datepicker table td.next.focus, .bootstrap-datetimepicker-widget .datepicker table td.old:hover, .bootstrap-datetimepicker-widget .datepicker table td.old:focus, .bootstrap-datetimepicker-widget .datepicker table td.old.focus, .bootstrap-datetimepicker-widget .datepicker table td.new:hover, .bootstrap-datetimepicker-widget .datepicker table td.new:focus, .bootstrap-datetimepicker-widget .datepicker table td.new.focus {\n  opacity: 1;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table thead tr:nth-child(1) th {\n  border-top: 0;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table thead tr:nth-child(2) th {\n  height: 20px;\n  line-height: 20px;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table thead tr:nth-child(2) th, .bootstrap-datetimepicker-widget .datepicker table td.cw {\n  background-color: var(--datepicker__thead-bg);\n  font-size: 0.875rem;\n  font-weight: 700;\n  color: #4A4F59;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table td {\n  font-size: 0.9375rem;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table td.today:not(.active):before {\n  border-bottom-color: #017e84;\n}\n\n.bootstrap-datetimepicker-widget .datepicker table td.active {\n  border-bottom-color: #fff;\n  color: #fff;\n}\n\n.bootstrap-datetimepicker-widget .picker-switch {\n  font-size: 1.25rem;\n}\n\n.bootstrap-datetimepicker-widget .accordion-toggle span.fa {\n  margin: 0;\n  border-radius: 0;\n  color: #4A4F59;\n  border-top: 1px solid var(--datepicker-border-color, #dee2e6);\n  box-shadow: 0 1px 0 var(--datepicker-border-color, #dee2e6);\n}\n\n.bootstrap-datetimepicker-widget .accordion-toggle span.fa.primary {\n  color: #017e84;\n}\n\n", "\n/* /web/static/src/legacy/scss/daterangepicker.scss */\n\n.daterangepicker.show-calendar {\n  --daterangepicker-border: 1px solid rgba(0, 0, 0, 0.15);\n  --daterangepicker-border-radius: 0.25rem;\n  --daterangepicker-bg: #fff;\n  --daterangepicker-color: #212529;\n  --daterangepicker__table-bg: transparent;\n  --daterangepicker__thead-bg: #f8f9fa;\n  --daterangepicker__cell-border-color: #dee2e6;\n  --daterangepicker__cell-bg--hover: #f8f9fa;\n  --daterangepicker__select-bg: #fff;\n  --daterangepicker__select-border-color: #ced4da;\n  --daterangepicker__select-color: #212529;\n  border: var(--daterangepicker-border);\n  border-radius: var(--daterangepicker-border-radius);\n  background-color: var(--daterangepicker-bg);\n  box-shadow: var(--daterangepicker-box-shadow);\n  font-family: inherit;\n}\n\n.daterangepicker.show-calendar:before {\n  border-bottom-color: var(--daterangerpicker__arrow-border-color, rgba(0, 0, 0, 0.15));\n}\n\n.daterangepicker.show-calendar:after {\n  border-bottom-color: var(--daterangerpicker__arrow-background-color, #fff);\n}\n\n.daterangepicker.show-calendar .calendar-table {\n  border: 0;\n  background-color: var(--daterangepicker__table-bg);\n}\n\n.daterangepicker.show-calendar .calendar-table .next span, .daterangepicker.show-calendar .calendar-table .prev span {\n  border-color: var(--daterangepicker-color);\n}\n\n.daterangepicker.show-calendar .calendar-table td, .daterangepicker.show-calendar .calendar-table th {\n  border-bottom-color: var(--daterangepicker__cell-border-color);\n}\n\n.daterangepicker.show-calendar .calendar-table td, .daterangepicker.show-calendar .calendar-table td.start-date.end-date, .daterangepicker.show-calendar .calendar-table th, .daterangepicker.show-calendar .calendar-table th.start-date.end-date {\n  border-radius: 0;\n}\n\n.daterangepicker.show-calendar .calendar-table td:not(.active), .daterangepicker.show-calendar .calendar-table th:not(.active) {\n  color: var(--daterangepicker-color);\n}\n\n.daterangepicker.show-calendar .calendar-table td.available:hover, .daterangepicker.show-calendar .calendar-table th.available:hover {\n  background-color: var(--daterangepicker__cell-bg--hover);\n}\n\n.daterangepicker.show-calendar .calendar-table td.prev, .daterangepicker.show-calendar .calendar-table td.next, .daterangepicker.show-calendar .calendar-table td.off.ends, .daterangepicker.show-calendar .calendar-table th.prev, .daterangepicker.show-calendar .calendar-table th.next, .daterangepicker.show-calendar .calendar-table th.off.ends {\n  opacity: 0.5;\n}\n\n.daterangepicker.show-calendar .calendar-table td.prev:hover, .daterangepicker.show-calendar .calendar-table td.prev:focus, .daterangepicker.show-calendar .calendar-table td.prev.focus, .daterangepicker.show-calendar .calendar-table td.next:hover, .daterangepicker.show-calendar .calendar-table td.next:focus, .daterangepicker.show-calendar .calendar-table td.next.focus, .daterangepicker.show-calendar .calendar-table td.off.ends:hover, .daterangepicker.show-calendar .calendar-table td.off.ends:focus, .daterangepicker.show-calendar .calendar-table td.off.ends.focus, .daterangepicker.show-calendar .calendar-table th.prev:hover, .daterangepicker.show-calendar .calendar-table th.prev:focus, .daterangepicker.show-calendar .calendar-table th.prev.focus, .daterangepicker.show-calendar .calendar-table th.next:hover, .daterangepicker.show-calendar .calendar-table th.next:focus, .daterangepicker.show-calendar .calendar-table th.next.focus, .daterangepicker.show-calendar .calendar-table th.off.ends:hover, .daterangepicker.show-calendar .calendar-table th.off.ends:focus, .daterangepicker.show-calendar .calendar-table th.off.ends.focus {\n  opacity: 1;\n}\n\n.daterangepicker.show-calendar .calendar-table td {\n  font-size: 0.9375rem;\n}\n\n.daterangepicker.show-calendar .calendar-table td.today {\n  position: relative;\n}\n\n.daterangepicker.show-calendar .calendar-table td.today:before {\n  content: '';\n  display: inline-block;\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n  border: solid transparent;\n  border-width: 0 0 7px 7px;\n  border-bottom-color: #017e84;\n  border-top-color: rgba(0, 0, 0, 0.15);\n}\n\n.daterangepicker.show-calendar .calendar-table td.in-range {\n  background-color: rgba(113, 75, 103, 0.3);\n  color: #4A4F59;\n  text-shadow: none;\n}\n\n.daterangepicker.show-calendar .calendar-table td.active, .daterangepicker.show-calendar .calendar-table td.active:hover, .daterangepicker.show-calendar .calendar-table td.in-range:hover {\n  background-color: #714B67;\n  color: #fff;\n  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);\n}\n\n.daterangepicker.show-calendar .calendar-table td.off {\n  background-color: var(--daterangepicker__cell-bg--off, rgba(108, 117, 125, 0.1));\n}\n\n.daterangepicker.show-calendar .calendar-table thead tr:nth-child(1) th {\n  height: 30px;\n  line-height: 30px;\n}\n\n.daterangepicker.show-calendar .calendar-table thead tr:nth-child(1) th.month {\n  font-size: 1.25rem;\n}\n\n.daterangepicker.show-calendar .calendar-table thead tr:nth-child(2) th {\n  background-color: var(--daterangepicker__thead-bg);\n  font-size: 0.875rem;\n  font-weight: 700;\n  color: #4A4F59;\n}\n\n.daterangepicker.show-calendar .calendar-time {\n  /*rtl:ignore*/\n  direction: ltr;\n}\n\n.daterangepicker.show-calendar .calendar-time select.hourselect, .daterangepicker.show-calendar .calendar-time select.minuteselect, .daterangepicker.show-calendar .calendar-time select.secondselect, .daterangepicker.show-calendar .calendar-time select.ampmselect {\n  display: initial;\n  border-color: var(--daterangepicker__select-border-color);\n  background-color: var(--daterangepicker__select-bg);\n  color: var(--daterangepicker__select-color);\n  -webkit-appearance: menulist-button;\n  -moz-appearance: menulist-button;\n  appearance: menulist-button;\n}\n\n.daterangepicker.show-calendar .drp-buttons {\n  border-top: 0;\n}\n\n.daterangepicker.show-calendar .drp-buttons .btn {\n  text-transform: uppercase;\n  font-weight: 400;\n}\n\n.daterangepicker.show-calendar .drp-buttons .drp-selected {\n  display: none;\n}\n\n@media (max-width: 575.98px) {\n  .daterangepicker.show-calendar {\n    position: fixed;\n    top: auto !important;\n    left: 0 !important;\n    bottom: 0;\n    right: 0 !important;\n    width: auto;\n    max-height: 90vh;\n    overflow-y: auto;\n    border-top-left-radius: 1rem;\n    border-top-right-radius: 1rem;\n    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  }\n  .daterangepicker.show-calendar .drp-calendar {\n    max-width: inherit;\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .daterangepicker.show-calendar .drp-buttons {\n    position: sticky;\n    bottom: 0;\n    background-color: #fff;\n  }\n}\n\n", "\n/* /web/static/src/legacy/scss/banner.scss */\n\n.o_has_banner .o_view_nocontent {\n  top: 30%;\n}\n\n@media (max-width: 767.98px) {\n  .o_has_banner .o_view_nocontent {\n    position: relative;\n    margin: auto;\n  }\n}\n\n", "\n/* /web/static/src/legacy/scss/colorpicker.scss */\n\n.o_colorpicker_widget .o_color_pick_area {\n  height: 125px;\n  background-image: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0) 50%, black 100%), linear-gradient(to right, gray 0%, rgba(128, 128, 128, 0) 100%);\n  cursor: crosshair;\n}\n\n.o_colorpicker_widget .o_color_slider {\n  background: linear-gradient(#F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%, #00F 66.66%, #F0F 83.33%, #F00 100%);\n}\n\n.o_colorpicker_widget .o_color_slider, .o_colorpicker_widget .o_opacity_slider {\n  width: 4%;\n  margin-right: 2%;\n  cursor: pointer;\n}\n\n.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer {\n  position: absolute;\n  top: auto;\n  left: -50%;\n  bottom: auto;\n  right: auto;\n  width: 200%;\n  height: 8px;\n  margin-top: -2px;\n}\n\n.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview {\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.9);\n  border: 1px solid black;\n}\n\n.o_colorpicker_widget .o_color_picker_inputs {\n  font-size: 10px;\n}\n\n.o_colorpicker_widget .o_color_picker_inputs input {\n  font-family: monospace !important;\n  height: 18px;\n  font-size: 11px;\n}\n\n.o_colorpicker_widget .o_color_picker_inputs .o_hex_div input {\n  width: 7ch;\n}\n\n.o_colorpicker_widget .o_color_picker_inputs .o_rgba_div input {\n  margin-right: 3px;\n  width: 3ch;\n}\n\n", "\n/* /web/static/src/legacy/scss/popover.scss */\n\n@keyframes slide-top {\n  0% {\n    opacity: 0;\n    transform: translateY(-5%);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slide-right {\n  0% {\n    opacity: 0;\n    transform: translateX(5%);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes slide-bottom {\n  0% {\n    opacity: 0;\n    transform: translateY(5%);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slide-left {\n  0% {\n    opacity: 0;\n    transform: translateX(-5%);\n  }\n  100% {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.o_popover {\n  max-width: 100vw;\n  position: fixed;\n  z-index: 1060;\n  border: 1px solid #dee2e6;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n}\n\n.o_popover .o_popover_header {\n  background-color: #e9ecef;\n  font-size: 1.1rem;\n  padding: 0.5rem 0.75rem;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.o_popover .o_popover_header:empty {\n  display: none;\n}\n\n.o_popover--top {\n  animation: 0.2s slide-top;\n}\n\n.o_popover--top::before, .o_popover--top::after {\n  content: '';\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -8px;\n  border: 8px solid transparent;\n  border-top-color: #e9ecef;\n}\n\n.o_popover--top::before {\n  margin-top: 1px;\n  border-top-color: #dee2e6;\n}\n\n.o_popover--right {\n  animation: 0.2s slide-right;\n}\n\n.o_popover--right::before, .o_popover--right::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  right: 100%;\n  margin-top: -8px;\n  border: 8px solid transparent;\n  border-right-color: #e9ecef;\n}\n\n.o_popover--right::before {\n  margin-right: 1px;\n  border-right-color: #dee2e6;\n}\n\n.o_popover--bottom {\n  animation: 0.2s slide-bottom;\n}\n\n.o_popover--bottom::before, .o_popover--bottom::after {\n  content: '';\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -8px;\n  border: 8px solid transparent;\n  border-bottom-color: #e9ecef;\n}\n\n.o_popover--bottom::before {\n  margin-bottom: 1px;\n  border-bottom-color: #dee2e6;\n}\n\n.o_popover--left {\n  animation: 0.2s slide-left;\n}\n\n.o_popover--left::before, .o_popover--left::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 100%;\n  margin-top: -8px;\n  border: 8px solid transparent;\n  border-left-color: #e9ecef;\n}\n\n.o_popover--left::before {\n  margin-left: 1px;\n  border-left-color: #dee2e6;\n}\n\n", "\n/* /web/static/src/legacy/scss/translation_dialog.scss */\n\n.o_translation_dialog .o_language_current {\n  font-weight: bold;\n}\n\n.o_translation_dialog .row {\n  margin-bottom: 9px;\n}\n\n", "\n/* /web/static/src/legacy/scss/keyboard.scss */\n\n.o_shortcut_table {\n  border-collapse: separate;\n  border-spacing: 0 1em;\n}\n\n.o_shortcut_table .o_key {\n  background-color: #F4F7F8;\n  border-radius: 3px;\n  border: 1px solid #B4B4B4;\n  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2), inset 0px -1px 1px 1px rgba(230, 230, 230, 0.8), inset 0px 2px 0px 0px rgba(255, 255, 255, 0.8);\n  display: inline-block;\n  font-family: Consolas,\"Liberation Mono\",Courier,monospace;\n  font-size: 0.85em;\n  padding: 2px 4px;\n}\n\n", "\n/* /web/static/src/legacy/scss/name_and_signature.scss */\n\n.o_web_sign_name_and_signature {\n  position: relative;\n}\n\n.o_web_sign_signature_group {\n  background: #ffffff;\n}\n\n.o_web_sign_signature_container {\n  position: relative;\n}\n\n/* Signature Dialog */\n.card.o_web_sign_auto_font_selection {\n  position: absolute;\n  top: 0;\n  left: auto;\n  bottom: auto;\n  right: 0;\n}\n\n.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list {\n  overflow: auto;\n}\n\n.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list > a {\n  height: 100px;\n}\n\n.card.o_web_sign_auto_font_selection .o_web_sign_auto_font_list > a > img {\n  height: 100%;\n}\n\n.o_field_widget .o_signature {\n  outline: 1px solid rgba(108, 117, 125, 0.3);\n  position: relative;\n}\n\n.o_field_widget .o_signature.o_signature_empty {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n.o_field_widget .o_signature > p {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.o_field_invalid .o_signature {\n  outline: 3px solid #dc3545;\n  cursor: pointer;\n}\n\n.o_form_editable .o_signature:hover {\n  outline: 3px solid #017e84;\n  cursor: pointer;\n}\n\n.o_signature_stroke {\n  position: absolute;\n  border-top: #D1D0CE solid 2px;\n  bottom: 16%;\n  width: 72%;\n  left: 14%;\n}\n\n", "\n/* /web/static/src/legacy/scss/web.zoomodoo.scss */\n\n.zoomodoo {\n  position: relative;\n  /* 'Shrink-wrap' the element */\n  display: inline-block;\n  *display: inline;\n  *zoom: 1;\n}\n\n.zoomodoo img {\n  vertical-align: bottom;\n}\n\n.zoomodoo-flyout {\n  position: absolute;\n  z-index: 100;\n  overflow: hidden;\n  background: #FFF;\n  top: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.zoomodoo-flyout img {\n  max-width: 500%;\n}\n\n.zoomodoo-hover .zoomodoo-flyout {\n  left: 0;\n}\n\n.zoomodoo-next .zoomodoo-flyout {\n  left: 100%;\n}\n\n", "\n/* /web/static/src/legacy/scss/fontawesome_overridden.scss */\n\n@font-face {\n  font-family: 'FontAwesome-tiktok-only';\n  src: url(\"/web/static/src/legacy/scss/../../../fonts/tiktok_only.woff\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: block;\n}\n\n.fa.fa-tiktok {\n  font-family: 'FontAwesome-tiktok-only' !important;\n}\n\n.fa.fa-tiktok:before {\n  content: '\\e07b';\n}\n\n.o_rtl .fa.fa-align-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-caret-right, .o_rtl .fa.fa-caret-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-caret-square-o-left {\n  transform: rotate(180deg);\n}\n\n", "\n/* /web_tour/static/src/scss/tip.scss */\n\n@keyframes move-left-right {\n  0% {\n    transform: translate(-3px, 0);\n  }\n  100% {\n    transform: translate(3px, 0);\n  }\n}\n\n@keyframes move-bottom-top {\n  0% {\n    transform: translate(0, -3px);\n  }\n  100% {\n    transform: translate(0, 3px);\n  }\n}\n\n.o_tooltip_parent {\n  position: relative !important;\n  opacity: 0.999 !important;\n}\n\n.o_tooltip {\n  /*rtl:begin:ignore*/\n  position: absolute !important;\n  top: 50% !important;\n  left: 50% !important;\n  /*rtl:end:ignore*/\n  z-index: 1080 !important;\n  opacity: 0 !important;\n  width: 28px !important;\n  height: 28px !important;\n  margin: 0 !important;\n  padding: 0 !important;\n  transition: opacity 400ms ease 0ms !important;\n}\n\n.o_tooltip.o_animated {\n  animation: move-bottom-top 500ms ease-in 0ms infinite alternate !important;\n}\n\n.o_tooltip.o_animated.right, .o_tooltip.o_animated.left {\n  animation-name: move-left-right !important;\n}\n\n.o_tooltip.o_tooltip_visible {\n  opacity: 1 !important;\n}\n\n.o_tooltip.o_tooltip_fixed {\n  position: fixed !important;\n}\n\n.o_tooltip::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: auto;\n  bottom: auto;\n  right: 0;\n  width: 28px;\n  height: 28px;\n  border: 3px solid white;\n  border-radius: 0 50% 50% 50%;\n  background: radial-gradient(#86597b, #714B67);\n  box-shadow: 0 0 40px 2px rgba(255, 255, 255, 0.5);\n}\n\n.o_tooltip.top::before {\n  transform: rotate(180deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);\n}\n\n.o_tooltip.right::before {\n  transform: rotate(270deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);\n}\n\n.o_tooltip.bottom::before {\n  transform: rotate(0deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);\n}\n\n.o_tooltip.left::before {\n  transform: rotate(90deg) translateY(8.25979621px) scaleY(1.12430027) rotate(45deg);\n}\n\n.o_tooltip > .o_tooltip_overlay {\n  display: none;\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n}\n\n.o_tooltip > .o_tooltip_content {\n  overflow: hidden;\n  direction: ltr;\n  position: relative;\n  padding: 7px 14px;\n  background-color: inherit;\n  color: transparent;\n  visibility: hidden;\n  line-height: 1.5;\n  font-size: 1rem;\n  font-family: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-weight: normal;\n}\n\n.o_tooltip > .o_tooltip_content .o_skip_tour {\n  display: inline-block;\n  margin-top: 4px;\n  cursor: pointer;\n  color: gray;\n}\n\n.o_tooltip > .o_tooltip_content .o_skip_tour:hover {\n  color: #4d4d4d;\n}\n\n.o_tooltip > .o_tooltip_content > p:last-child {\n  margin-bottom: 0;\n}\n\n.o_tooltip.active {\n  border: 3px solid #714B67 !important;\n  background-color: white !important;\n  transition: width 150ms ease 50ms, height 150ms ease 50ms, margin 150ms ease 50ms !important;\n}\n\n.o_tooltip.active::before {\n  width: 12px;\n  height: 12px;\n  border-color: #714B67;\n  border-radius: 0;\n  background: white;\n  box-shadow: none;\n}\n\n.o_tooltip.active > .o_tooltip_overlay {\n  display: block;\n}\n\n.o_tooltip.active > .o_tooltip_content {\n  color: black;\n  visibility: visible;\n  transition: color 0ms ease 200ms;\n}\n\n.o_tooltip.active.right {\n  transform: translateX(6px) !important;\n}\n\n.o_tooltip.active.right::before {\n  position: absolute;\n  top: 5px;\n  left: -12px;\n  bottom: auto;\n  right: auto;\n  transform: translateX(50%) rotate(45deg);\n}\n\n.o_tooltip.active.top {\n  transform: translateY(-6px) !important;\n}\n\n.o_tooltip.active.top::before {\n  /*rtl:begin:ignore*/\n  position: absolute;\n  top: auto;\n  left: 5px;\n  bottom: -12px;\n  right: auto;\n  /*rtl:end:ignore*/\n  transform: translateY(-50%) rotate(45deg);\n}\n\n.o_tooltip.active.left {\n  transform: translateX(-6px) !important;\n}\n\n.o_tooltip.active.left::before {\n  position: absolute;\n  top: 5px;\n  left: auto;\n  bottom: auto;\n  right: -12px;\n  transform: translateX(-50%) rotate(45deg);\n}\n\n.o_tooltip.active.bottom {\n  transform: translateY(6px) !important;\n}\n\n.o_tooltip.active.bottom::before {\n  /*rtl:begin:ignore*/\n  position: absolute;\n  top: -12px;\n  left: 5px;\n  bottom: auto;\n  right: auto;\n  /*rtl:end:ignore*/\n  transform: translateY(50%) rotate(45deg);\n}\n\n.o_tooltip.active.inverse.left::before, .o_tooltip.active.inverse.right::before {\n  top: auto;\n  bottom: 5px;\n}\n\n.o_tooltip.active.inverse.top::before, .o_tooltip.active.inverse.bottom::before {\n  left: auto/*rtl:ignore*/;\n  right: 5px/*rtl:ignore*/;\n}\n\n@media print {\n  .o_tooltip {\n    display: none !important;\n  }\n}\n\n.tab-pane.active .o_list_renderer.o_tooltip_parent {\n  z-index: 999;\n}\n\n", "\n/* /web_editor/static/src/js/editor/odoo-editor/src/base_style.scss */\n\nli.oe-nested {\n  display: block;\n}\n\n.o_table tr {\n  border-color: #C9CCD2;\n}\n\n.o_table tr td {\n  padding: 0.5rem;\n}\n\n.o_text_columns {\n  max-width: 100% !important;\n  padding: 0 !important;\n}\n\n.o_text_columns > .row {\n  margin: 0 !important;\n}\n\n.o_text_columns > .row > .col-xs-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xs-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xs-12:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-sm-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-sm-12:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-md-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-md-12:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-lg-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-lg-12:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xl-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xl-12:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-1:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-1:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-2:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-2:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-3:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-3:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-4:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-4:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-5:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-5:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-6:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-6:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-7:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-7:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-8:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-8:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-9:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-9:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-10:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-10:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-11:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-11:last-of-type {\n  padding-right: 0;\n}\n\n.o_text_columns > .row > .col-xxl-12:first-of-type {\n  padding-left: 0;\n}\n\n.o_text_columns > .row > .col-xxl-12:last-of-type {\n  padding-right: 0;\n}\n\n.oe-tabs {\n  display: inline-block;\n  white-space: pre-wrap;\n  max-width: 40px;\n  width: 40px;\n}\n\n", "\n/* /web_enterprise/static/src/webclient/home_menu/home_menu_background.scss */\n\n.o_home_menu_background, .o_web_client.o_home_menu_background {\n  background-size: cover;\n  background-attachment: fixed;\n  background-color: var(--homeMenu-bg-color, #917878);\n  background-image: var(--homeMenu-bg-image, radial-gradient(at 100% 0%, rgba(59, 44, 63, 0.6) 0px, transparent 50%), radial-gradient(at 7% 13%, rgba(119, 114, 126, 0.42) 0px, transparent 50%), radial-gradient(at 96% 94%, rgba(117, 111, 123, 0.51) 0px, transparent 50%), radial-gradient(at 3% 96%, rgba(59, 44, 63, 0.41) 0px, transparent 50%), url(\"/web_enterprise/static/img/home-menu-bg-overlay.svg\"));\n}\n\n", "\n/* /web_enterprise/static/src/webclient/navbar/navbar.scss */\n\n.o_main_navbar .o_menu_toggle {\n  color: #FFF;\n}\n\n.o_main_navbar .o_menu_toggle rect, .o_main_navbar .o_menu_toggle g {\n  transform-origin: 0 50%;\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back {\n  transform: scaleX(-1);\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back rect {\n  width: 6px;\n  height: 3px;\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back rect:first-child {\n  transform: translate(12%, 0);\n  rx: 1;\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back #o_menu_toggle_row_0 {\n  transform: scale3d(0.5, 1, 1) translate(0, 45%) skewY(-22deg);\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back #o_menu_toggle_row_0 + g rect {\n  width: 0;\n  height: 0;\n}\n\n.o_main_navbar .o_menu_toggle.o_menu_toggle_back #o_menu_toggle_row_2 {\n  transform: scale3d(0.5, 1, 1) translate(0, -37%) skewY(22deg);\n}\n\n@media screen and (min-width: 992px) and (prefers-reduced-motion: no-preference) {\n  .o_main_navbar .o_menu_toggle:hover rect {\n    width: 6px;\n    height: 3px;\n  }\n  .o_main_navbar .o_menu_toggle:hover rect:first-child {\n    transform: translate(12%, 0);\n    rx: 1;\n  }\n  .o_main_navbar .o_menu_toggle:hover #o_menu_toggle_row_0 {\n    transform: scale3d(0.5, 1, 1) translate(0, 45%) skewY(-22deg);\n  }\n  .o_main_navbar .o_menu_toggle:hover #o_menu_toggle_row_0 + g rect {\n    width: 0;\n    height: 0;\n  }\n  .o_main_navbar .o_menu_toggle:hover #o_menu_toggle_row_2 {\n    transform: scale3d(0.5, 1, 1) translate(0, -37%) skewY(22deg);\n  }\n  .o_main_navbar .o_menu_toggle, .o_main_navbar .o_menu_toggle g {\n    transition: all .3s;\n  }\n  .o_main_navbar .o_menu_toggle rect {\n    transition: all .1s;\n  }\n}"], "file": "/web/assets/1201-4589889/web.assets_common.css", "sourceRoot": "../../../"}