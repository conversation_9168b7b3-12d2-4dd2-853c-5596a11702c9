/*!
 * Signature Pad v4.1.4 | https://github.com/szimek/signature_pad
 * (c) 2022 <PERSON><PERSON><PERSON> | Released under the MIT license
 */

/*!
 * https://github.com/Starcounter-Jack/JSO<PERSON>-<PERSON>
 * (c) 2017-2021 <PERSON>
 * MIT license
 */

/*!
 * https://github.com/Starcounter-Jack/JSON-Patch
 * (c) 2017-2022 <PERSON>
 * MIT licensed
 */

/*! @formio/choices.js v10.2.1 | © 2024 <PERSON> | https://github.com/jshjohnson/Choices#readme */

/*! @license DOMPurify 3.0.5 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.5/LICENSE */

/*! Native Promise Only
    v0.8.1 (c) Kyle Simpson
    MIT License: http://getify.mit-license.org
*/

/*! formiojs v4.19.4 | https://unpkg.com/formiojs@4.19.4/LICENSE.txt */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */

/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */

//! Copyright (c) JS Foundation and other contributors

//! github.com/moment/moment-timezone

//! license : MIT

//! moment-timezone.js

//! moment.js

//! version : 0.5.40
