{"version": 12, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 7, "rowNumber": 44, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "20": {"size": 23}, "21": {"size": 23}, "22": {"size": 23}, "23": {"size": 23}, "24": {"size": 23}, "25": {"size": 23}, "26": {"size": 23}, "27": {"size": 23}, "28": {"size": 23}, "29": {"size": 23}, "30": {"size": 23}, "31": {"size": 40}, "32": {"size": 40}}, "cols": {"0": {"size": 275}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 275}, "5": {"size": 100}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Weekly Stock Moves](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[],\"context\":{\"group_by\":[\"date:week\",\"picking_type_id\"],\"graph_measure\":\"product_uom_qty\",\"graph_mode\":\"line\",\"graph_groupbys\":[\"date:week\",\"picking_type_id\"]},\"modelName\":\"stock.move\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Weekly Stock Moves\"})", "border": 1}, "A19": {"style": 1, "content": "[Top Products](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"product_id\",\"!=\",false]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"__count\",\"product_qty\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"stock.move\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Products\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Product\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",1)"}, "A22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",2)"}, "A23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",3)"}, "A24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",4)"}, "A25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",5)"}, "A26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",6)"}, "A27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",7)"}, "A28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",8)"}, "A29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",9)"}, "A30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#product_id\",10)"}, "A32": {"style": 1, "content": "[Top Warehouses](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"warehouse_id\",\"!=\",false]],\"context\":{\"group_by\":[\"warehouse_id\"],\"pivot_measures\":[\"__count\",\"product_qty\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"warehouse_id\"]},\"modelName\":\"stock.move\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Warehouses\"})", "border": 1}, "A33": {"style": 2, "content": "=_t(\"Warehouse\")", "border": 2}, "A34": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",1)"}, "A35": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",2)"}, "A36": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",3)"}, "A37": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",4)"}, "A38": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",5)"}, "A39": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",6)"}, "A40": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",7)"}, "A41": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",8)"}, "A42": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",9)"}, "A43": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#warehouse_id\",10)"}, "B19": {"style": 5, "border": 1}, "B20": {"style": 6, "content": "=_t(\"Moves\")", "border": 2}, "B21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",1)"}, "B22": {"format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",2)"}, "B23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",3)"}, "B24": {"format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",4)"}, "B25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",5)"}, "B26": {"format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",6)"}, "B27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",7)"}, "B28": {"format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",8)"}, "B29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",9)"}, "B30": {"format": 1, "content": "=ODOO.PIVOT(3,\"__count\",\"#product_id\",10)"}, "B32": {"style": 5, "border": 1}, "B33": {"style": 6, "content": "=_t(\"Moves\")", "border": 2}, "B34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",1)"}, "B35": {"format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",2)"}, "B36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",3)"}, "B37": {"format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",4)"}, "B38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",5)"}, "B39": {"format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",6)"}, "B40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",7)"}, "B41": {"format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",8)"}, "B42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",9)"}, "B43": {"format": 1, "content": "=ODOO.PIVOT(1,\"__count\",\"#warehouse_id\",10)"}, "C19": {"style": 5, "border": 1}, "C20": {"style": 6, "content": "=_t(\"Quantity\")", "border": 2}, "C21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",1)"}, "C22": {"format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",2)"}, "C23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",3)"}, "C24": {"format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",4)"}, "C25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",5)"}, "C26": {"format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",6)"}, "C27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",7)"}, "C28": {"format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",8)"}, "C29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",9)"}, "C30": {"format": 1, "content": "=ODOO.PIVOT(3,\"product_qty\",\"#product_id\",10)"}, "C32": {"style": 5, "border": 1}, "C33": {"style": 6, "content": "=_t(\"Quantity\")", "border": 2}, "C34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",1)"}, "C35": {"format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",2)"}, "C36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",3)"}, "C37": {"format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",4)"}, "C38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",5)"}, "C39": {"format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",6)"}, "C40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",7)"}, "C41": {"format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",8)"}, "C42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",9)"}, "C43": {"format": 1, "content": "=ODOO.PIVOT(1,\"product_qty\",\"#warehouse_id\",10)"}, "E19": {"style": 1, "content": "[Top Partners](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"partner_id\",\"!=\",false]],\"context\":{\"group_by\":[\"partner_id\"],\"pivot_measures\":[\"__count\",\"product_qty\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"partner_id\"]},\"modelName\":\"stock.move\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Partners\"})", "border": 1}, "E20": {"style": 2, "content": "=_t(\"Partner\")", "border": 2}, "E21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",1)"}, "E22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",2)"}, "E23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",3)"}, "E24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",4)"}, "E25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",5)"}, "E26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",6)"}, "E27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",7)"}, "E28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",8)"}, "E29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",9)"}, "E30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#partner_id\",10)"}, "E32": {"style": 1, "content": "[Top Locations](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"location_dest_id\",\"!=\",false]],\"context\":{\"group_by\":[\"location_dest_id\"],\"pivot_measures\":[\"__count\",\"product_qty\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"location_dest_id\"]},\"modelName\":\"stock.move\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Locations\"})", "border": 1}, "E33": {"style": 2, "content": "=_t(\"Location\")", "border": 2}, "E34": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",1)"}, "E35": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",2)"}, "E36": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",3)"}, "E37": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",4)"}, "E38": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",5)"}, "E39": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",6)"}, "E40": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",7)"}, "E41": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",8)"}, "E42": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",9)"}, "E43": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_dest_id\",10)"}, "F19": {"style": 5, "border": 1}, "F20": {"style": 6, "content": "=_t(\"Moves\")", "border": 2}, "F21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",1)"}, "F22": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",2)"}, "F23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",3)"}, "F24": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",4)"}, "F25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",5)"}, "F26": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",6)"}, "F27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",7)"}, "F28": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",8)"}, "F29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",9)"}, "F30": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#partner_id\",10)"}, "F32": {"style": 5, "border": 1}, "F33": {"style": 6, "content": "=_t(\"Moves\")", "border": 2}, "F34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",1)"}, "F35": {"format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",2)"}, "F36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",3)"}, "F37": {"format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",4)"}, "F38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",5)"}, "F39": {"format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",6)"}, "F40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",7)"}, "F41": {"format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",8)"}, "F42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",9)"}, "F43": {"format": 1, "content": "=ODOO.PIVOT(2,\"__count\",\"#location_dest_id\",10)"}, "G19": {"style": 5, "border": 1}, "G20": {"style": 6, "content": "=_t(\"Quantity\")", "border": 2}, "G21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",1)"}, "G22": {"format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",2)"}, "G23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",3)"}, "G24": {"format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",4)"}, "G25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",5)"}, "G26": {"format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",6)"}, "G27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",7)"}, "G28": {"format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",8)"}, "G29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",9)"}, "G30": {"format": 1, "content": "=ODOO.PIVOT(4,\"product_qty\",\"#partner_id\",10)"}, "G32": {"style": 5, "border": 1}, "G33": {"style": 6, "content": "=_t(\"Quantity\")", "border": 2}, "G34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",1)"}, "G35": {"format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",2)"}, "G36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",3)"}, "G37": {"format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",4)"}, "G38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",5)"}, "G39": {"format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",6)"}, "G40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",7)"}, "G41": {"format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",8)"}, "G42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",9)"}, "G43": {"format": 1, "content": "=ODOO.PIVOT(2,\"product_qty\",\"#location_dest_id\",10)"}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "C7": {"border": 1}, "C8": {"border": 2}, "D7": {"border": 1}, "D8": {"border": 2}, "E7": {"border": 1}, "E8": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "G7": {"border": 1}, "G8": {"border": 2}}, "conditionalFormats": [], "figures": [{"id": "68ebf228-b00b-456b-84f5-ada5212d184e", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "top", "metaData": {"groupBy": ["date:week", "picking_type_id"], "measure": "product_uom_qty", "order": null, "resModel": "stock.move"}, "searchParams": {"comparison": null, "context": {}, "domain": [], "groupBy": ["date:week", "picking_type_id"], "orderBy": []}, "type": "odoo_line", "verticalAxisPosition": "left"}}, {"id": "828d0e17-137f-4f2b-9d2c-e848859dbff6", "x": 0, "y": 0, "width": 221, "height": 127, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Average Cycle Time", "type": "scorecard", "background": "", "baseline": "Data!E2", "baselineDescr": "last period", "keyValue": "Data!D2"}}, {"id": "dba70661-e001-43b9-9dab-d97e0469e83a", "x": 228, "y": 0, "width": 221, "height": 126, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Average Delay", "type": "scorecard", "background": "", "baseline": "Data!E3", "baselineDescr": "last period", "keyValue": "Data!D3"}}, {"id": "eccd9e96-997e-4a92-ad09-91f142d23248", "x": 457, "y": 0, "width": 221, "height": 126, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Moved Quantity", "type": "scorecard", "background": "", "baseline": "Data!E4", "baselineDescr": "since last period", "keyValue": "Data!D4"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "5e7f330a-cb9a-409e-a49c-e16a7d817143", "name": "Data", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"style": 2, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Avg cycle time\")"}, "A3": {"content": "=_t(\"Avg delay\")"}, "A4": {"content": "=_t(\"Product quantity\")"}, "B1": {"style": 2, "content": "=_t(\"Current\")"}, "B2": {"content": "=ODOO.PIVOT(5,\"cycle_time\")"}, "B3": {"content": "=ODOO.PIVOT(5,\"delay\")"}, "B4": {"content": "=ODOO.PIVOT(5,\"product_qty\")"}, "C1": {"style": 2, "content": "=_t(\"Previous\")"}, "C2": {"content": "=ODOO.PIVOT(6,\"cycle_time\")"}, "C3": {"content": "=ODOO.PIVOT(6,\"delay\")"}, "C4": {"content": "=ODOO.PIVOT(6,\"product_qty\")"}, "D1": {"style": 2, "content": "=_t(\"Current\")"}, "D2": {"style": 8, "content": "=CONCATENATE(ROUNDUP(B2),_t(\" days\"))"}, "D3": {"style": 8, "content": "=CONCATENATE(ROUNDUP(B3),_t(\" days\"))"}, "D4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "E1": {"style": 2, "content": "=_t(\"Previous\")"}, "E2": {"style": 7, "format": 2, "content": "=(ROUNDUP(C2))"}, "E3": {"style": 7, "format": 2, "content": "=(ROUNDUP(C3))"}, "E4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C4)"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true}, "3": {"fillColor": "#f2f2f2", "textColor": "#741b47"}, "4": {"textColor": "#741b47"}, "5": {"fontSize": 16, "bold": true}, "6": {"bold": true, "align": "right"}, "7": {"fillColor": "#f2f2f2"}, "8": {"align": "right", "fillColor": "#f2f2f2"}}, "formats": {"1": "0", "2": "#,##0"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "chartOdooMenusReferences": {"68ebf228-b00b-456b-84f5-ada5212d184e": "stock.menu_stock_root", "828d0e17-137f-4f2b-9d2c-e848859dbff6": "stock_enterprise.stock_dashboard_menuitem", "dba70661-e001-43b9-9dab-d97e0469e83a": "stock_enterprise.stock_dashboard_menuitem", "eccd9e96-997e-4a92-ad09-91f142d23248": "stock_enterprise.stock_dashboard_menuitem"}, "odooVersion": 4, "lists": {}, "listNextId": 1, "pivots": {"1": {"colGroupBys": [], "context": {}, "domain": [["warehouse_id", "!=", false]], "id": "1", "measures": [{"field": "__count"}, {"field": "product_qty"}], "model": "stock.move", "rowGroupBys": ["warehouse_id"], "name": "Stock Moves Analysis by Warehouse", "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "desc"}}, "2": {"colGroupBys": [], "context": {}, "domain": ["&", ["location_dest_id", "!=", false], ["location_dest_usage", "=", "internal"]], "id": "2", "measures": [{"field": "__count"}, {"field": "product_qty"}], "model": "stock.move", "rowGroupBys": ["location_dest_id"], "name": "Stock Moves Analysis by Destination Location", "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "desc"}}, "3": {"colGroupBys": [], "context": {}, "domain": [["product_id", "!=", false]], "id": "3", "measures": [{"field": "__count"}, {"field": "product_qty"}], "model": "stock.move", "rowGroupBys": ["product_id"], "name": "Stock Moves Analysis by Product", "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "desc"}}, "4": {"colGroupBys": [], "context": {}, "domain": [["partner_id", "!=", false]], "id": "4", "measures": [{"field": "__count"}, {"field": "product_qty"}], "model": "stock.move", "rowGroupBys": ["partner_id"], "name": "Stock Moves Analysis by Destination Address ", "sortedColumn": {"groupId": [[], []], "measure": "product_qty", "order": "desc"}}, "5": {"colGroupBys": [], "context": {}, "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "id": "5", "measures": [{"field": "cycle_time"}, {"field": "delay"}, {"field": "product_qty"}], "model": "stock.report", "rowGroupBys": [], "name": "stats - current", "sortedColumn": null}, "6": {"colGroupBys": [], "context": {}, "domain": ["&", ["picking_type_code", "!=", false], "&", ["state", "=", "done"], ["picking_type_code", "=", "outgoing"]], "id": "6", "measures": [{"field": "cycle_time"}, {"field": "delay"}, {"field": "product_qty"}], "model": "stock.report", "rowGroupBys": [], "name": "stats - previous", "sortedColumn": null}}, "pivotNextId": 7, "globalFilters": [{"id": "1457f866-ca36-44b7-b0c8-977a7b199b05", "type": "date", "label": "Period", "defaultValue": "last_three_months", "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "date", "type": "datetime", "offset": 0}, "2": {"field": "date", "type": "datetime", "offset": 0}, "3": {"field": "date", "type": "datetime", "offset": 0}, "4": {"field": "date", "type": "datetime", "offset": 0}, "5": {"field": "date_done", "type": "datetime", "offset": 0}, "6": {"field": "date_done", "type": "datetime", "offset": -1}}, "listFields": {}, "graphFields": {"68ebf228-b00b-456b-84f5-ada5212d184e": {"field": "date", "type": "datetime", "offset": 0}}}, {"id": "52ae0b4e-e0a6-4f79-a9f5-d09949e1255c", "type": "relation", "label": "Warehouse", "modelName": "stock.warehouse", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "warehouse_id", "type": "many2one"}, "2": {"field": "warehouse_id", "type": "many2one"}, "3": {"field": "warehouse_id", "type": "many2one"}, "4": {"field": "warehouse_id", "type": "many2one"}, "5": {"field": "picking_id.move_ids.warehouse_id", "type": "many2one"}, "6": {"field": "picking_id.move_ids.warehouse_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"68ebf228-b00b-456b-84f5-ada5212d184e": {"field": "warehouse_id", "type": "many2one"}}}, {"id": "23dda4a2-0b24-45d6-83c1-d6feefb5c485", "type": "relation", "label": "Location", "modelName": "stock.location", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "location_dest_id", "type": "many2one"}, "2": {"field": "location_dest_id", "type": "many2one"}, "3": {"field": "location_dest_id", "type": "many2one"}, "4": {"field": "location_dest_id", "type": "many2one"}, "5": {"field": "picking_id.move_ids.location_dest_id", "type": "many2one"}, "6": {"field": "picking_id.move_ids.location_dest_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"68ebf228-b00b-456b-84f5-ada5212d184e": {"field": "location_dest_id", "type": "many2one"}}}, {"id": "9557b5ee-217e-4394-b979-52b748e3de52", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_id", "type": "many2one"}, "2": {"field": "product_id", "type": "many2one"}, "3": {"field": "product_id", "type": "many2one"}, "4": {"field": "product_id", "type": "many2one"}, "5": {"field": "product_id", "type": "many2one"}, "6": {"field": "product_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"68ebf228-b00b-456b-84f5-ada5212d184e": {"field": "product_id", "type": "many2one"}}}, {"id": "3d708afa-14fb-4b34-be3b-b82f82c75d26", "type": "relation", "label": "Partner", "modelName": "res.partner", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "partner_id", "type": "many2one"}, "2": {"field": "partner_id", "type": "many2one"}, "3": {"field": "partner_id", "type": "many2one"}, "4": {"field": "partner_id", "type": "many2one"}, "5": {"field": "partner_id", "type": "many2one"}, "6": {"field": "partner_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"68ebf228-b00b-456b-84f5-ada5212d184e": {"field": "partner_id", "type": "many2one"}}}]}